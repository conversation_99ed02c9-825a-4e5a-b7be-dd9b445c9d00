/* eslint-disable prettier/prettier */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-non-null-assertion */
/* eslint-disable @typescript-eslint/no-explicit-any */

import { InputHTMLAttributes, useCallback, useEffect, useState } from 'react'
import { ReactSVG } from 'react-svg'
import * as S from './styles'
import { StringUtils } from 'utils/stringUtils'
import { NumberUtils } from 'utils/numberUtils'
import { DateUtils } from 'utils/dateUtils'
export type isDefaultType = 'default' | 'small'
export type StateTypes = 'default' | 'danger' | 'success' | 'octopus' | 'ihealth'

export type MasksTypes = 'cep' | 'cnpj' | 'cpf' | 'phone' | 'date' | 'time' | 'card' | 'monetary' | 'maskThreePointTwo' | 'maskFivePointTwo'

export interface InputProps extends InputHTMLAttributes<HTMLInputElement> {
    assistiveText?: string
    disabled?: boolean
    iconLeft?: any
    isDefault: isDefaultType
    mask?: MasksTypes
    label?: string
    required?: boolean
    className?: string
    loading?: string | boolean
    state?: StateTypes
    type?: 'number' | 'text' | 'password' | 'time' | 'date'
    iconPassword?: boolean
    onClickInIconRight?: boolean
    value?: string
    maxLength?: number
    initialValue?: any

    handleOnChange?: (value: any) => any
    handleOnFocus?: () => any
    handleOnBlur?: () => any
    iconRight?: any
    onClickIconRight?: () => void
    onClickIconLeft?: () => void
    isSelect?: boolean
}

const Input = ({
    label = 'placeholder',
    assistiveText,
    isDefault = 'default',
    type = 'text',
    mask,
    iconLeft = '',
    loading,
    state = 'default',
    iconPassword = false,
    value = '',
    handleOnChange,
    handleOnBlur,
    handleOnFocus,
    required,
    className,
    iconRight = '',
    onClickIconLeft,
    onClickIconRight,
    isSelect,
    disabled = false,
    initialValue,
    maxLength,

    ...rest
}: InputProps) => {
    // const [valueState, setValue] = useState(initialValue !== '' ? initialValue : value)
    const [valueState, setValue] = useState(value)
    const [isShowPassword, setIsShowPassword] = useState(false)
    const [typeState, setType] = useState(type)
    const [isFocus, setIsFocus] = useState(false)
    const [isFill, setIsFill] = useState(false)

    const changeMask = (value: string) => {
        let valueWithMask = value
        if (mask === 'cpf') {
            valueWithMask = StringUtils.maskCpf(value)
        } else if (mask === 'monetary') {
            valueWithMask = NumberUtils.maskMoney(value)
        } else if (mask === 'date') {
            valueWithMask = DateUtils.maskDate(value)
        } else if (mask === 'maskThreePointTwo') {
            valueWithMask = NumberUtils.maskFloat(value, 3, 2)
        } else if (mask === 'maskFivePointTwo') {
            valueWithMask = NumberUtils.maskFloat(value, 5, 2)
        }
        return valueWithMask
    }
    const changeInputValue = (event: React.FormEvent<HTMLInputElement>) => {
        let value = event.currentTarget.value
        value = changeMask(value)
        // if (maxLength !== undefined) {
        //     value = value.slice(0, maxLength)
        // }
        setValue(value)
        !!handleOnChange && handleOnChange!(value)
    }

    function onFocus() {
        setIsFocus(true)

        !!handleOnFocus && handleOnFocus()
    }

    function onBlur() {
        setIsFocus(false)
        setIsFill(!!valueState)

        !!handleOnBlur && handleOnBlur()
    }

    function onClickVisibilit() {
        const val = !isShowPassword
        setIsShowPassword(val)
        if (val) return setType(type)
        return setType('password')
    }

    function clickedIconRight() {
        !!onClickIconRight && onClickIconRight()
    }

    useEffect(() => {
        if (iconPassword) {
            setType('password')
        } else {
            setType(type)
        }
    }, [iconPassword])

    // useEffect(() => {
    //     if (initialValue !== undefined && initialValue !== '') {
    //         changeMask

    //         setValue(changeMask(initialValue))
    //         setIsFill(true)
    //         onBlur()
    //     }
    // }, [initialValue])

    const inicializarValue = useCallback((originalValue) => {
        setValue(changeMask(originalValue))
        // if (originalValue) setIsFill(true)
        setIsFill(true)
    }, [])

    useEffect(() => {
        // if (!initialValues) return

        inicializarValue(initialValue)
    }, [initialValue, inicializarValue])

    return (
        <>
            <S.Wrapper
                isFocus={isFocus}
                isFill={type !== 'date' ? isFill : true}
                isIconLeft={!!iconLeft}
                state={state}
                className={className}
                disabled={disabled}
                // eslint-disable-next-line @typescript-eslint/no-empty-function
                onClick={isSelect ? clickedIconRight : () => {}}
            >
                <S.WrapperLeft>
                    {!!iconLeft && (
                        <S.IconLeft onClick={onClickIconLeft}>
                            <ReactSVG src={iconLeft ? iconLeft : ''} wrapper="span" />
                        </S.IconLeft>
                    )}

                    <S.Input
                        value={valueState}
                        placeholder={isDefault === 'small' ? label : ''}
                        onChange={changeInputValue}
                        onFocus={onFocus}
                        onBlur={onBlur}
                        onWheel={(event) => event.currentTarget.blur()}
                        type={typeState}
                        disabled={isSelect || disabled}
                        maxLength={maxLength}
                        {...rest}
                    />
                </S.WrapperLeft>
                {isDefault === 'default' && (
                    <S.Label>
                        {label}
                        {required && <span> *</span>}
                    </S.Label>
                )}
                {iconPassword && (
                    <S.ButtonPassword state={state} onClick={onClickVisibilit}>
                        {isShowPassword ? (
                            <ReactSVG src={'/faturamento/assets/icons/mai-ic-visible-false.svg'} wrapper="span" />
                        ) : (
                            <ReactSVG src={'/faturamento/assets/icons/mai-ic-visible-true.svg'} wrapper="span" />
                        )}
                    </S.ButtonPassword>
                )}
                {!!iconRight && (
                    <S.ButtonIconRight type="button" state={state} onClick={clickedIconRight}>
                        <ReactSVG src={iconRight} wrapper="span" />
                    </S.ButtonIconRight>
                )}
                {!!loading && <img className="loading" src="/faturamento/animations/loading.gif" alt="Loading" />}
            </S.Wrapper>
            {!!assistiveText && (
                <S.ContentAssistiveText state={state}>
                    <div>
                        {' '}
                        <ReactSVG src="/faturamento/assets/icons/iconWarning.svg" />
                    </div>

                    {assistiveText}
                </S.ContentAssistiveText>
            )}
        </>
    )
}

export default Input

