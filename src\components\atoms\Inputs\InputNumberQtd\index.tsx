import React, { useEffect, useState } from 'react'
import Input from '../Input'
import * as S from './styles'

type InputNumberQtdProps = {
    handleOnChange: any
    count?: number
    maxCount?: number
    minCount?: number
    label?: string
    disabled?: boolean
    required?: boolean
    className?: string
    textAlign?: 'left' | 'center' | 'right'
}

const InputNumberQtd = ({
    handleOnChange,
    count,
    className,
    label = 'Quantidade',
    disabled = false,
    maxCount = 99,
    required,
    minCount = 0,
    textAlign = 'left'
}: InputNumberQtdProps) => {
    const [value, setValue] = useState<number>(0)

    // useEffect(() => {
    //     handleOnChange(value)
    // }, [value])

    useEffect(() => {
        handleOnChange(value !== null ? value : count)
    }, [value])

    useEffect(() => {
        !!count && setValue(count)
    }, [count])

    return (
        <S.Wrapper className={className}>
            <Input
                label={label}
                required={required}
                isDefault="default"
                style={{ textAlign }}
                value={value.toString()}
                initialValue={value.toString()}
                onChange={(e) => {
                    const currentCount = Number(e.target.value)
                    if (currentCount) {
                        setValue(currentCount <= maxCount && currentCount >= minCount ? currentCount : value)
                    } else {
                        setValue(0)
                    }
                }}
                // maxLength={2}
                type="number"
                disabled={disabled}
                onClickIconLeft={() => !disabled && setValue(value - 1 <= minCount ? minCount : value - 1)}
                onClickIconRight={() => !disabled && setValue(value + 1 >= maxCount ? maxCount : value + 1)}
                iconLeft={'/faturamento/assets/icons/decrement.svg'}
                iconRight={'/faturamento/assets/icons/blue-plus.svg'}
            />
        </S.Wrapper>
    )
}

export default InputNumberQtd
