/* eslint-disable @typescript-eslint/no-unused-vars */

import styled from 'styled-components'

export const Container = styled.div`
    max-width: 1400px;

    padding: 16px;

    margin: 0 auto;

    width: 95%;

    background-color: #fff;

    border-radius: 8px;
`

export const PeriodHeader = styled.div`
    display: flex;

    flex-direction: row;

    justify-content: space-between;
`

export const ContainerInputs = styled.div`
    .row {
        display: flex;

        flex-direction: row;

        margin: 24px 0px;

        gap: 16px;

        .rule-name {
            flex-basis: 23%;
        }

        .contract-type {
            flex-basis: 100%;
        }

        .effective-date {
            flex-basis: 23%;
        }

        .medical-bills {
            flex-basis: 23%;
        }

        .first-verification {
            flex: 1;
        }

        .second-verification {
            flex: 1;
        }

        .provider {
            flex-basis: 20%;
        }

        @media screen and (max-width: 1024px) {
            flex-direction: column;

            gap: 16px;

            margin: 0 0px 16px 0;

            input,
            label {
                font-size: 1.4rem;
            }
        }
    }
`

export const SubTitle = styled.span`
    font-style: normal;

    font-weight: 400;

    font-size: 14px;

    line-height: 24px;

    color: rgba(0, 0, 0, 0.56);
`

export const WrapperSititle = styled.div`
    padding: 12px 0 16px 0;
`
