import { AxiosResponse } from 'axios'
import { apiAnaliseContas } from '../apis/apiAnaliseContas'
import { ObjectUtils } from 'utils/objectUtils'
import { IDatasCobrancaDTO, IGetDatasCobrancaProps } from 'types/analiseContas/cobranca'

const baseUrl = '/cobranca'

export class CobrancaService {
    static async getDatasCobranca(props: IGetDatasCobrancaProps): Promise<AxiosResponse<IDatasCobrancaDTO>> {
        const params = ObjectUtils.propsToParams(props)
        const url = `${baseUrl}/datas-cobranca?${params}`

        return apiAnaliseContas.get(url)
    }
}
