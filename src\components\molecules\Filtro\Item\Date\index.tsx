import React, { InputHTMLAttributes } from 'react'
import { useFiltroContext } from '../../Root'
import Input from 'components/molecules/novos/Input'

interface IInput {
    field: string
}
const DateInput = ({ field, ...props }: IInput & InputHTMLAttributes<HTMLInputElement>) => {
    const { setFieldValue, filter } = useFiltroContext()

    const handleOnChange: React.ChangeEventHandler<HTMLInputElement> = (e) => {
        setFieldValue(e?.target?.value, field)
    }

    return <Input value={filter?.[field]} onChange={handleOnChange} type="date" {...props} />
}

export default DateInput
