import styled, { css } from 'styled-components'
export const WrapperItem = styled.div`
    ${({ theme }) => css`
        display: flex;
        flex-direction: column;
        width: 100%;
        align-items: center;

        label {
            width: 100%;
            font-size: 12px;
            font-weight: normal;
            display: flex;
            align-items: center;
            margin-bottom: 14px;

            cursor: pointer;

            input {
                display: none;
            }
        }
    `}
`

export const Check = styled.div`
    ${({ theme }) => css`
        width: 18px;
        height: 18px;
        border: 2px solid rgba(0, 0, 0, 0.4);
        background: #fff;
        border-radius: 4px;
        box-sizing: border-box;

        margin-right: 24px;
        margin-bottom: 1px;

        border-radius: ${theme.border.radius};

        &::before {
            content: '';
            width: 100%;
            height: 100%;
            border-radius: 1px;
            background-image: url('/faturamento/assets/icons/mai-ic-check-white.svg');
            background-color: rgba(0, 68, 204, 1);
            background-position: center;
            display: block;
            background-repeat: no-repeat;
            transform: scale(0);
            transition: all 0.1s;
        }
    `}
`

export const Label = styled.div`
    ${({ theme }) => css`
        flex: 1;

        p {
            font-size: 16px;
            font-weight: 400;
            line-height: 24px;
            color: rgba(62, 78, 101, 1);
            padding: 0;
        }
    `}
`

export const ContainerChecked = styled.div`
    display: flex;
    align-items: center;
`

export const Wrapper = styled.div`
    ${({ theme }) => css`
        display: flex;
        flex-direction: column;
        align-items: center;
        place-content: center;
        width: fit-content !important;

        input:checked + ${ContainerChecked} {
            ${Check} {
                border-color: ${theme.colors.primary.default};
                border-radius: 4px;
            }

            ${Check}::before {
                transform: scale(1);
            }

            ${Label} {
                p {
                    border-color: ${theme.colors.pink.primary.default};
                }
            }
        }
    `}
`
