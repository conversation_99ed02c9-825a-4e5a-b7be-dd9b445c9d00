import * as S from './styles'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import Layout from 'components/molecules/Layout'
import SearchBar from 'components/molecules/SearchBar'
import Pagination from 'components/molecules/Pagination'
import Button from 'components/atoms/Button'
import DividerSectionCard from 'components/atoms/DividerSectionCard'
import { IPagination } from 'types/common/pagination'
import { AnaliseContasService } from 'src/services/analiseContasApi/analiseContasServices'
import { DateUtils } from 'utils/dateUtils'
import { IResumoQuery } from 'types/analiseContas/resumo'
import { IPagePrestador, IPrestadorQuery } from 'types/analiseContas/prestador'
import { PaginationHelper } from 'utils/helpers/paginationHelper'
import { useToast } from 'src/hooks/toast'
import { IExercicioQuery } from 'types/analiseContas/exercicio'
import CardSliderCompetencias from 'components/molecules/CardSliderCompetencias'
import { SituacaoPrestadorAnaliseDeContas } from 'types/common/enums'
import moment from 'moment'
import { useRouter } from 'next/router'
import NoContent from 'components/molecules/NoContent'
import Box from '@mui/material/Box'
import CircularProgress from '@mui/material/CircularProgress'
import {
    Grid,
    Card,
    CardContent,
    Typography,
    Container,
    Checkbox,
    Tooltip,
    TooltipProps,
    tooltipClasses,
    FormControl,
    InputLabel,
    MenuItem,
    Switch,
    Alert
} from '@mui/material'
import { ReactSVG } from 'react-svg'
import InfoOutlined from '@mui/icons-material/InfoOutlined'
import { styled } from '@mui/material/styles'
import ExpandedListContent from 'components/molecules/ExpandedListContent'
import ModalAction from './DetalhamentoTemplate/Tabs/Resumo/ModalAction'
import {
    ICompetenciaDto,
    ICompetenciaContentQuery,
    IAguardandoAprovacaoDto,
    IAprovadosDto,
    IPrestadoresFilterOptionsDto,
    IListaPrestadoresDto,
    IContentList
} from 'types/composicaoPagamento/types'
import { ComposicaoPagamento } from 'src/services/composicaoPagamentoApi'
import AlertBG from 'components/atoms/Alert'
import // listagemPrestadoresAguardandoAprovacao,
// listagemPrestadoresAprovados,
// aguardandoAprovacaoMock,
// aprovadosMock,
// listagemPrestadoresAguardandoAprovacao,
// listagemPrestadoresAprovados,
// providerFilterOptionsMock
'./mock'
import Selectable from 'components/molecules/Select'
import TablePagination from 'components/molecules/TablePagination'
import ProviderTableContent from 'components/atoms/ProviderTableContent'
import Modal from 'components/atoms/Modal'
import { NumberUtils } from 'utils/numberUtils'
import { currencyMaskBRL } from 'utils/helpers/currencyMaskBRL'
import ModalGenericComponent from 'components/organisms/ModalGeneric'
import Skeleton from 'react-loading-skeleton'
import dynamic from 'next/dynamic'

const AnimatedLoadingLottie = dynamic(() => import('components/atoms/AnimatedLoadingLottie'), { ssr: false })

// const STORAGE_KEY = '@composicao-faturamento-competenciaData'

const CardTooltip = styled(({ className, ...props }: TooltipProps) => <Tooltip {...props} classes={{ popper: className }} />)(({ theme }) => ({
    [`& .${tooltipClasses.tooltip}`]: {
        backgroundColor: theme.palette.common.white,
        boxShadow: theme.shadows[1],
        padding: '16px',
        borderRadius: '8px'
    }
}))

// const DropdownItem = styled(MenuItem)`
//     font-size: 12px;
// `

interface IFieldValues {
    nome_codigo_cnpj?: string
    categoria?: string
    tipo?: string
    producao?: string
    municipio?: string
    macrorregiao?: string
    prioritario?: boolean
}

interface IFilterOptions {
    categoria: IOptionsParse[]
    tipo: IOptionsParse[]
    producao: IOptionsParse[]
    Municipios: IOptionsParse[]
    macrorregiao: IOptionsParse[]
}

interface IOptionsParse {
    label: string
    value: string
}

interface IContentSelected {
    teto: number
    producao: number
    extrateto: number
    idPrestador: number
    consulta: number
    liminar: number
    glosa_biometrica: number
    recurso_glosa: number
    extrateto_porcentagem: number
    valor_faturamento: number
}

const ComposicaoPagamentoTemplate = () => {
    const { addToast } = useToast()
    const router = useRouter()

    const [loadingProviders, setLoadingProviders] = useState(false)
    const [loadingEnviarParaContas, setLoadingEnviarParaContas] = useState(false)
    const [isLoadingCompetenciaSelecionada, setIsLoadingCompetenciaSelecionada] = useState(false)
    const [competencias, setCompetencias] = useState<ICompetenciaContentQuery[]>()
    const [anoExercicio, setAnoExercicio] = useState<number>()
    const [competenciaSelecionada, setCompetenciaSelecionada] = useState<ICompetenciaContentQuery>()
    const [composicaoGeral, setComposicaoGeral] = useState<IAguardandoAprovacaoDto>()
    const [aprovados, setAprovados] = useState<IAprovadosDto>()
    const [anos, setAnos] = useState<string[]>([])
    const [prestadoresPage, setPrestadoresPage] = useState<IContentList[]>()
    const [pagination, setPagination] = useState<IPagination>()
    const [fieldValues, setFieldValues] = useState<IFieldValues>({})
    // const [loadingFilters, setLoadingFilters] = useState<boolean>(false)
    const [status, setStatus] = useState(1) // 1 ou 2
    const [filterOpen, setFilterOpen] = useState(false)
    const [modalApproveId, setModalApproveId] = useState<number>()
    const [openApproveModal, setOpenApproveModal] = useState(false)
    const [aproveBtnModal, setAproveBtnModal] = useState(false)
    const [refreshComponents, setRefreshComponents] = useState(false)
    const [clearSearch, setClearSearch] = useState(false)
    const [isOpenModal, setIsOpenModal] = useState(false)
    const [guiaPagination, setGuiaPagination] = useState<IPagination>()
    const [totalValue, setTotalValue] = useState(0)
    const [pageNumber, setPageNumber] = useState(0)
    const [numberOfProviders, setNumberOfProviders] = useState(0)
    const [totalAPagar, setTotalAPagar] = useState(0)

    const [priorityOnlyFilter, setPriorityOnlyFilter] = useState(false)
    const initialValueSelect = useMemo(() => anoExercicio && { label: anoExercicio.toString(), value: anoExercicio.toString() }, [anoExercicio])
    const anosExercicio = useMemo(() => anos.map((ano) => ({ label: ano, value: ano })), [anos])
    const [providerSelected, setProviderSelected] = useState<IContentSelected[]>()
    const [categorias, setCategorias] = useState<string[]>([])
    const [enableGerarRelatorioBtn, setEnableGerarRelatorioBtn] = useState<boolean>(false)

    const [opcoesRelatorioSelecionada, setOpcoesRelatoriosSelecionada] = useState({
        categorias: [],
        typeDoc: 'PDF'
    })

    const [filterOptions, setFilterOptions] = useState<IFilterOptions>({
        categoria: [],
        macrorregiao: [],
        producao: [],
        Municipios: [],
        tipo: []
    })

    const resetValues = {
        categoria: undefined,
        macrorregiao: undefined,
        municipio: undefined,
        nome_codigo_cnpj: undefined,
        prioritario: false,
        producao: undefined,
        tipo: undefined
    }

    const disponivelParaEnvioContasAPagar =
        composicaoGeral?.aguardandoAprovacao === 0 &&
        (competenciaSelecionada?.fase === 'PROCESSAMENTO_EM_ANDAMENTO' || competenciaSelecionada?.fase === 'COMPOSICAO_PAGAMENTO_EM_ANDAMENTO')

    const getCompetencias = () => {
        // TODO:
        // Utilizando mock, remover 'competenciaMock' e adicionar 'data'.
        // Descomentar tbm o retorno da api.

        ComposicaoPagamento.getCompetenciasData(anoExercicio).then(({ data }) => {
            const sortCompetencias = data?.competencias?.sort((a, b) => moment(a.competencia).diff(b.competencia))
            setCompetencias(sortCompetencias)
            const exercicios = data?.anos.map((x) => x)
            setAnos(exercicios)
            const selecionada = sortCompetencias[sortCompetencias.length - 1]
            setCompetenciaSelecionada(selecionada)
        })
    }

    const getAguardandoAprovacao = () => {
        ComposicaoPagamento.getComposicaoGeralData(competenciaSelecionada?.competencia).then(({ data }) => {
            setComposicaoGeral(data)
        })
    }

    const getAprovados = () => {
        ComposicaoPagamento.getAprovadosData(competenciaSelecionada?.competencia).then(({ data }) => {
            setAprovados(data)
        })
    }

    // GET PRESTADORES LIST

    function getPrestadoresListContent() {
        setLoadingProviders(true)
        ComposicaoPagamento?.getPrestadoresList(
            { competencia: competenciaSelecionada?.competencia, status: status === 1 ? 'AGUARDANDO_APROVACAO' : 'APROVADO' },
            {
                size: 10,
                page: pageNumber,
                categoria: fieldValues?.categoria ? fieldValues?.categoria : null,
                macrorregiao: fieldValues?.macrorregiao ? fieldValues?.macrorregiao : null,
                municipio: fieldValues?.municipio ? fieldValues?.municipio : null,
                nome_codigo_cnpj: fieldValues?.nome_codigo_cnpj ? fieldValues?.nome_codigo_cnpj : null,
                prioritario: fieldValues?.prioritario ? fieldValues?.prioritario : null,
                producao: fieldValues?.producao ? fieldValues?.producao : null,
                tipo: fieldValues?.tipo ? fieldValues?.tipo : null
            }
        )
            .then(({ data }) => {
                setPrestadoresPage(data?.prestadorConsultaDto?.content)
                setTotalAPagar(data?.totalAPagar)
                setLoadingProviders(false)
                setGuiaPagination(PaginationHelper.parserPagination(data?.prestadorConsultaDto, setPageNumber))
            })
            .catch((err) => {
                // console.log(err)
            })
    }

    //

    useEffect(() => {
        getCompetencias()
    }, [anoExercicio])

    useEffect(() => {
        if (competenciaSelecionada) {
            getPrestadoresListContent()

            if (status === 1) {
                getAguardandoAprovacao()
            } else {
                getAprovados()
            }
        }
    }, [pageNumber, anoExercicio, competenciaSelecionada, status, refreshComponents, clearSearch])

    const onChangeAnoExercicio = useCallback(
        (ano: React.SetStateAction<number>) => {
            // resetValues()
            setAnoExercicio(ano)
        },
        [setAnoExercicio]
    )

    const parseCompetencias = useMemo(() => {
        return competencias?.map((item) => {
            return {
                competencia: item.competencia,
                status: item.status?.toString(),
                month: DateUtils.getMonthName(item.competencia),
                fase: item?.fase
            }
        })
    }, [competencias])

    // const handlePesquisar = () => {
    //     // carregarPrestadores(numberPage, filtro, true)
    // }

    const handleOnClosePesquisa = () => {
        setFieldValues({
            ...fieldValues,
            nome_codigo_cnpj: undefined
        }),
            setClearSearch(!clearSearch)
    }

    const handleSetCompetencia = (competencia: any) => {
        setCompetenciaSelecionada(competencia)
    }

    // PARSE SELECT OPTIONS

    const parseSelectOptions = (retornoAPI: IPrestadoresFilterOptionsDto) => {
        return Object.keys(retornoAPI)?.reduce((resultado, chave) => {
            const valoresFormatados = retornoAPI[chave]?.map((valor) => ({
                label: chave === 'producao' ? valor?.pretty_name : valor,
                value: chave === 'producao' ? valor?.name : valor
            }))

            return {
                ...resultado,
                [chave]: [{ label: 'Todos', value: null }, ...valoresFormatados]
            }
        }, {} as IFilterOptions)
    }

    const handleClickToOpenFilter = () => {
        setFilterOpen(!filterOpen)

        ComposicaoPagamento?.getPrestadoresFilterOptions(competenciaSelecionada?.competencia)
            .then(({ data }) => {
                setFilterOptions(parseSelectOptions(data))
                // setLoadingFilters(false)
            })
            .catch((err) => {
                // console.log('')
            })
    }

    const priorizarPrestador = (id_prestador: number) => {
        ComposicaoPagamento?.priorizar(id_prestador).catch((err) => {
            addToast({
                title: 'Não foi possível priorizar prestador, tente novamente',
                type: 'error'
            })
        })
    }

    const tableAguardandoAprovacao: { label: string; value: any | 'acao' }[] = [
        { label: 'Prestador', value: 'prestador' },
        { label: 'Tipo de prestador', value: 'tipoPrestador' },
        { label: 'Teto', value: 'teto' },
        { label: 'Produção', value: 'producao' },
        { label: 'Extrateto', value: 'extrateto' },
        { label: 'Total para aprovação', value: 'totalAprovacao' },
        { label: '', value: 'iconJoinha' }
    ]

    const tableAprovados: { label: string; value: any | 'acao' }[] = [
        { label: 'Prestador', value: 'prestador' },
        { label: 'Tipo de prestador', value: 'tipoPrestador' },
        { label: 'Liberado', value: 'liberado' },
        { label: 'Faturamento', value: 'faturamento' },
        { label: 'Total aprovado', value: 'totalAprovado' }
    ]

    const getFiltersCategoria = () => {
        ComposicaoPagamento?.getPrestadoresFilterOptions(competenciaSelecionada?.competencia)
            .then(({ data }) => {
                setCategorias(() => data?.categoria)
            })
            .catch((err) => {
                // console.log('')
            })
    }

    // ACTION BUTTONS

    const handleClickToFavorite = () => {
        alert('clicou em fav')
    }

    const handleClickSendToAccountsPayable = () => {
        setLoadingEnviarParaContas(true)
        const { competencia } = competenciaSelecionada
        ComposicaoPagamento?.fecharComposicao(competencia)
            .then((resp) => {
                getCompetencias()
                addToast({
                    title: 'Competência enviada para contas a pagar',
                    type: 'success'
                })
            })
            .catch((err) => {
                addToast({
                    title: err?.response?.data?.message || err?.message || err?.data?.message || 'Não foi possível enviar para contas.',
                    type: 'error'
                })
            })
            .finally(() => {
                setLoadingEnviarParaContas(false)
                setRefreshComponents(!refreshComponents)
            })
    }

    const addOrRemove = (name) => {
        const newCategories = opcoesRelatorioSelecionada?.categorias
        const index = opcoesRelatorioSelecionada?.categorias.indexOf(name)
        if (index === -1) {
            newCategories.push(name)
        } else {
            newCategories.splice(index, 1)
        }
        setOpcoesRelatoriosSelecionada((prev) => ({
            ...prev,
            categorias: newCategories
        }))
    }

    const handleGerarRelatorio = () => {
        setEnableGerarRelatorioBtn(false)
        if (opcoesRelatorioSelecionada?.typeDoc === 'PDF') {
            ComposicaoPagamento.gerarRelatorioPDF({
                competencia: competenciaSelecionada?.competencia,
                categoria: opcoesRelatorioSelecionada?.categorias
            })
                .then((response) => {
                    const url = window.URL.createObjectURL(response.data)
                    const link = document.createElement('a')
                    link.href = url
                    link.setAttribute('download', `RelatorioPrestadoresAprovados.pdf`) //or any other extension
                    document.body.appendChild(link)
                    link.click()
                })
                .catch((error) => {
                    console.log(error)
                    if (error?.response?.status === 400) {
                        addToast({
                            title: 'Não foram encontrados prestadores aprovados com os parâmetros fornecidos',
                            type: 'info',
                            duration: 3000
                        })
                    }
                })
                .finally(() => setEnableGerarRelatorioBtn(false))
        } else if (opcoesRelatorioSelecionada?.typeDoc === 'XLSX') {
            ComposicaoPagamento.gerarRelatorioXLXS({
                competencia: competenciaSelecionada?.competencia,
                categoria: opcoesRelatorioSelecionada?.categorias
            })
                .then((response) => {
                    const url = window.URL.createObjectURL(response.data)
                    const link = document.createElement('a')
                    link.href = url
                    link.setAttribute('download', `RelatorioPrestadoresAprovados.xlsx`) //or any other extension
                    document.body.appendChild(link)
                    link.click()
                })
                .catch((error) => {
                    if (error?.response?.status === 400) {
                        addToast({
                            title: 'Não foram encontrados prestadores aprovados com os parâmetros fornecidos',
                            type: 'info',
                            duration: 3000
                        })
                    }
                })
                .finally(() => setEnableGerarRelatorioBtn(false))
        }
        setEnableGerarRelatorioBtn(false)
    }

    // Função de aprovar itens da tabela de prestadores.

    const handleClickToOpenApproveModal = () => {
        setAproveBtnModal(true)
        if (modalApproveId && providerSelected.length === 0) {
            ComposicaoPagamento?.putAprovarPrestador(competenciaSelecionada?.competencia, 'AGUARDANDO_APROVACAO', {
                providerId: [modalApproveId],
                categoria: fieldValues?.categoria ? fieldValues?.categoria : null,
                macrorregiao: fieldValues?.macrorregiao ? fieldValues?.macrorregiao : null,
                municipio: fieldValues?.municipio ? fieldValues?.municipio : null,
                nome_codigo_cnpj: fieldValues?.nome_codigo_cnpj ? fieldValues?.nome_codigo_cnpj : null,
                prioritario: fieldValues?.prioritario ? fieldValues?.prioritario : null,
                producao: fieldValues?.producao ? (fieldValues?.producao as 'MAIOR_QUE_TETO' | 'MENOR_IGUAL_TETO') : null,
                tipo: fieldValues?.tipo ? fieldValues?.tipo : null
            })
                .then((response) => {
                    addToast({
                        type: 'success',
                        title: 'Prestador aprovado com sucesso!',
                        duration: 4000
                    })
                    setFieldValues(resetValues)
                    setRefreshComponents(!refreshComponents)
                })
                .catch((error) => {
                    addToast({
                        type: 'error',
                        title: 'Não foi possível realizar aprovação!',
                        duration: 4000
                    })
                })
                .finally(() => {
                    setOpenApproveModal(false)
                    setModalApproveId(null)
                    setAproveBtnModal(false)
                })
        } else {
            const providerList = providerSelected?.map((providerId) => {
                return providerId?.idPrestador
            })

            ComposicaoPagamento?.putAprovarPrestador(competenciaSelecionada?.competencia, 'AGUARDANDO_APROVACAO', {
                providerId: providerList,
                categoria: fieldValues?.categoria ? fieldValues?.categoria : null,
                macrorregiao: fieldValues?.macrorregiao ? fieldValues?.macrorregiao : null,
                municipio: fieldValues?.municipio ? fieldValues?.municipio : null,
                nome_codigo_cnpj: fieldValues?.nome_codigo_cnpj ? fieldValues?.nome_codigo_cnpj : null,
                prioritario: fieldValues?.prioritario ? fieldValues?.prioritario : null,
                producao: fieldValues?.producao ? (fieldValues?.producao as 'MAIOR_QUE_TETO' | 'MENOR_IGUAL_TETO') : null,
                tipo: fieldValues?.tipo ? fieldValues?.tipo : null
            })
                .then((response) => {
                    addToast({
                        type: 'success',
                        title: 'Prestador aprovado com sucesso!',
                        duration: 4000
                    })
                    setFieldValues(resetValues)
                    setRefreshComponents(!refreshComponents)
                })
                .catch((error) => {
                    addToast({
                        type: 'error',
                        title: 'Não foi possível realizar aprovação!',
                        duration: 4000
                    })
                })
                .finally(() => {
                    setOpenApproveModal(false)
                    setProviderSelected([])
                    setAproveBtnModal(false)
                })
        }
    }

    // Contabiliza quantos prestadores foram selecionados

    useEffect(() => {
        setNumberOfProviders(providerSelected?.length)
    }, [providerSelected])

    useEffect(() => {
        if (numberOfProviders !== 0) {
            setNumberOfProviders(0)
            setProviderSelected([])
        }
    }, [status])

    // Contabiliza quanto é o total a pagar

    useEffect(() => {
        const valorTotalAprovado = providerSelected?.reduce((acumulador, item: any) => acumulador + item?.total_aprovado?.total_aprovado, 0)
        setTotalValue(valorTotalAprovado)
    }, [providerSelected])

    return (
        <Layout isLoggedIn title="Composição de pagamento">
            <S.Container>
                <CardSliderCompetencias
                    data={parseCompetencias}
                    hasDefaultValueInSelect={true}
                    dateFilter={anosExercicio}
                    valueSelect={initialValueSelect}
                    onChangeAnoExercicio={onChangeAnoExercicio}
                    competenciaSelecionada={competenciaSelecionada}
                    setCompetenciaSelecionada={handleSetCompetencia}
                />
            </S.Container>

            <S.ContainerInfo>
                {competenciaSelecionada?.fase === 'EM_ANALISE_DE_CONTAS' && (
                    <AlertBG
                        title="Essa competência está na fase de análise de contas"
                        description="Para consultar os prestadores, acesse Análise de contas"
                    />
                )}

                {competenciaSelecionada?.fase === 'AGUARDANDO_ENVIO' && (
                    <AlertBG title="Análise de contas dessa competência foi encerrada. Aguardando envio." />
                )}

                {competenciaSelecionada?.fase === 'COMPOSICAO_PAGAMENTO_EM_ANDAMENTO' && <></>}

                {competenciaSelecionada?.fase === 'PROCESSAMENTO_EM_ANDAMENTO' && (
                    <AlertBG
                        title="Processamento em andamento"
                        color="secondary"
                        iconSrc="/faturamento/assets/icons/warning.svg"
                        description="O tempo do processamento varia de acordo com a quantidade de dados."
                    />
                )}

                {competenciaSelecionada?.fase === 'COMPOSICAO_PAGAMENTO_FINALIZADA' && (
                    <AlertBG
                        title="Composição de pagamento finalizada"
                        description={
                            <p>
                                As contas já estão na fase de pagamento.{' '}
                                <span
                                    onClick={() => (window.location.href = `${process.env.NEXT_PUBLIC_CONTAS}`)}
                                    style={{ textDecoration: 'none', fontWeight: '600', color: '#2B45D4', cursor: 'pointer' }}
                                >
                                    Acesse Contas
                                </span>{' '}
                                a pagar para conferir.
                            </p>
                        }
                    />
                )}
            </S.ContainerInfo>

            <S.FilterCard>
                <S.CardItem
                    isFocus={status === 1}
                    onClick={() => {
                        setPageNumber(0)
                        setStatus(1)
                    }}
                >
                    <S.CardName>Aguardando aprovação</S.CardName>
                    <S.CardQuantity isFocus={status === 1} status="analyzing">
                        {status === 1 && composicaoGeral?.aguardandoAprovacao}
                        {status === 2 && aprovados?.aguardandoAprovacao}
                    </S.CardQuantity>
                </S.CardItem>
                <S.CardItem
                    isFocus={status === 2}
                    onClick={() => {
                        setPageNumber(0)
                        setStatus(2)
                    }}
                >
                    <S.CardName>Aprovados</S.CardName>
                    <S.CardQuantity isFocus={status === 2} status="finish">
                        {status === 1 && composicaoGeral?.aprovados}
                        {status === 2 && aprovados?.aprovados}
                    </S.CardQuantity>
                </S.CardItem>
            </S.FilterCard>

            {status === 1 ? (
                <DividerSectionCard dividerContent={true}>
                    <S.TitleSection>Composição geral</S.TitleSection>

                    {isLoadingCompetenciaSelecionada ? (
                        <Box sx={{ display: 'flex', minHeight: 218, alignItems: 'center', justifyContent: 'center' }}>
                            <CircularProgress sx={{ color: '#2B45D4' }} />
                        </Box>
                    ) : (
                        <Box display="flex" flexDirection="row">
                            <Container style={{ paddingLeft: 0, paddingRight: 8 }}>
                                <Box my={1}>
                                    <Card style={{ backgroundColor: 'rgba(43, 69, 212, 0.04)', padding: '16px' }} elevation={0}>
                                        <CardContent style={{ padding: 0 }}>
                                            <S.Text typeStyle="1">Teto</S.Text>
                                        </CardContent>
                                        <CardContent
                                            style={{
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'flex-end',
                                                padding: 0
                                            }}
                                        >
                                            <S.Text typeStyle="2">
                                                {currencyMaskBRL(composicaoGeral?.composicaoAguardandoAprovacao?.teto) || '-'}
                                            </S.Text>
                                        </CardContent>
                                    </Card>
                                </Box>

                                <Box display="flex" flexDirection="row" justifyContent="space-between">
                                    <Box my={1} mr={0.5} flexBasis="49%">
                                        <Card style={{ backgroundColor: 'rgba(43, 69, 212, 0.04)', padding: '16px' }} elevation={0}>
                                            <CardContent style={{ padding: 0 }}>
                                                <S.Text typeStyle="1">Consulta</S.Text>
                                            </CardContent>
                                            <CardContent style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', padding: 0 }}>
                                                <S.Text typeStyle="2">
                                                    {currencyMaskBRL(composicaoGeral?.composicaoAguardandoAprovacao?.consulta) || '-'}
                                                </S.Text>
                                            </CardContent>
                                        </Card>
                                    </Box>

                                    <Box my={1} ml={0.5} flexBasis="49%">
                                        <Card style={{ backgroundColor: 'rgba(43, 69, 212, 0.04)', padding: '16px' }} elevation={0}>
                                            <CardContent style={{ padding: 0 }}>
                                                <S.Text typeStyle="1">Liminar</S.Text>
                                            </CardContent>
                                            <CardContent style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', padding: 0 }}>
                                                <S.Text typeStyle="2">
                                                    {currencyMaskBRL(composicaoGeral?.composicaoAguardandoAprovacao?.liminar) || '-'}
                                                </S.Text>
                                            </CardContent>
                                        </Card>
                                    </Box>
                                </Box>

                                <Box my={1}>
                                    <Card style={{ backgroundColor: 'rgba(43, 69, 212, 0.04)', padding: '16px' }} elevation={0}>
                                        <CardContent style={{ padding: 0, display: 'flex', justifyContent: 'space-between', flexDirection: 'row' }}>
                                            <Box display="flex">
                                                <S.Text typeStyle="1" style={{ fontWeight: '600', color: '#1e1e1e' }}>
                                                    Extrateto
                                                </S.Text>

                                                <CardTooltip
                                                    placement="top-end"
                                                    title={
                                                        <React.Fragment>
                                                            <S.Text typeStyle="1" style={{ fontWeight: '600', color: '#1e1e1e' }}>
                                                                Extrateto
                                                            </S.Text>

                                                            <Typography color="#00000099" fontSize="12px" fontWeight="400" marginTop="8px">
                                                                É o valor de produção que ultrapassou o teto estabelecido. Para o cálculo, o sistema
                                                                soma os valores de guias do mais antigo para o mais novo que estão disponíveis para
                                                                pagamento, até chegar em um total aproximado.
                                                            </Typography>
                                                            <Typography color="#000" fontSize="12px" fontWeight="600" marginTop="8px">
                                                                Como calcular
                                                            </Typography>
                                                            <Typography color="#00000099" fontSize="12px" fontWeight="400" marginTop="8px">
                                                                Extrateto ≈ Teto - Produção
                                                            </Typography>
                                                            <Typography color="#000" fontSize="12px" fontWeight="600" marginTop="8px">
                                                                Observação
                                                            </Typography>
                                                            <Typography color="#00000099" fontSize="12px" fontWeight="400" marginTop="8px">
                                                                Prestadores que não possuem teto ou produção abaixo do teto não possuem extrateto.
                                                            </Typography>
                                                        </React.Fragment>
                                                    }
                                                >
                                                    <Box style={{ cursor: 'pointer', marginLeft: '6px' }} display="flex" alignItems="center">
                                                        <InfoOutlined style={{ color: '#2B45D4' }} />
                                                    </Box>
                                                </CardTooltip>
                                            </Box>
                                            <S.Text typeStyle="1">
                                                {composicaoGeral?.composicaoAguardandoAprovacao?.extratetoPorcentagem}% acima do teto
                                            </S.Text>
                                        </CardContent>
                                        <CardContent style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', padding: 0 }}>
                                            <S.Text typeStyle="2">
                                                {currencyMaskBRL(composicaoGeral?.composicaoAguardandoAprovacao?.extrateto) || '-'}
                                            </S.Text>
                                        </CardContent>
                                    </Card>
                                </Box>
                            </Container>

                            <Container style={{ paddingLeft: 8, paddingRight: 0 }}>
                                <Box my={1}>
                                    <Card style={{ backgroundColor: 'rgba(43, 69, 212, 0.04)', padding: '16px' }} elevation={0}>
                                        <CardContent style={{ padding: 0 }}>
                                            <S.Text typeStyle="1">Produção</S.Text>
                                        </CardContent>
                                        <CardContent style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', padding: 0 }}>
                                            <S.Text typeStyle="2">
                                                {currencyMaskBRL(composicaoGeral?.composicaoAguardandoAprovacao?.producao) || '-'}
                                            </S.Text>
                                        </CardContent>
                                    </Card>
                                </Box>

                                <Box display="flex" flexDirection="row" justifyContent="space-between">
                                    <Box my={1} mr={0.5} flexBasis="49%">
                                        <Card style={{ backgroundColor: 'rgba(43, 69, 212, 0.04)', padding: '16px' }} elevation={0}>
                                            <CardContent style={{ padding: 0 }}>
                                                <S.Text typeStyle="1">Glosa de Biometria(OS 09/2012)</S.Text>
                                            </CardContent>
                                            <CardContent style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', padding: 0 }}>
                                                <S.Text typeStyle="2">
                                                    {currencyMaskBRL(composicaoGeral?.composicaoAguardandoAprovacao?.glosaBiometria) || '-'}
                                                </S.Text>
                                            </CardContent>
                                        </Card>
                                    </Box>

                                    <Box my={1} ml={0.5} flexBasis="49%">
                                        <Card style={{ backgroundColor: 'rgba(43, 69, 212, 0.04)', padding: '16px' }} elevation={0}>
                                            <CardContent style={{ padding: 0 }}>
                                                <S.Text typeStyle="1">Recurso de glosa apurado</S.Text>
                                            </CardContent>
                                            <CardContent style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', padding: 0 }}>
                                                <S.Text typeStyle="2">
                                                    {currencyMaskBRL(composicaoGeral?.composicaoAguardandoAprovacao?.recursoGlosa) || '-'}
                                                </S.Text>
                                            </CardContent>
                                        </Card>
                                    </Box>
                                </Box>

                                <Box my={1}>
                                    <Card style={{ backgroundColor: 'rgba(43, 69, 212, 0.04)', padding: '16px' }} elevation={0}>
                                        <CardContent style={{ padding: 0, display: 'flex', justifyContent: 'space-between', flexDirection: 'row' }}>
                                            <Box display="flex">
                                                <S.Text typeStyle="1" style={{ fontWeight: '600', color: '#1e1e1e' }}>
                                                    Valor do faturamento
                                                </S.Text>

                                                <CardTooltip
                                                    placement="top-start"
                                                    title={
                                                        <React.Fragment>
                                                            <S.Text typeStyle="1" style={{ fontWeight: '600', color: '#1e1e1e' }}>
                                                                Valor do faturamento
                                                            </S.Text>

                                                            <Typography color="#00000099" fontSize="12px" fontWeight="400" marginTop="8px">
                                                                É o valor a ser pago ao prestador sem as liberações.
                                                            </Typography>
                                                            <Typography color="#000" fontSize="12px" fontWeight="600" marginTop="8px">
                                                                Como calcular
                                                            </Typography>
                                                            <Typography color="#00000099" fontSize="12px" fontWeight="400" marginTop="8px">
                                                                Valor do faturamento = Produção - valor extrateto - Glosa de biometria + Consulta
                                                            </Typography>
                                                        </React.Fragment>
                                                    }
                                                >
                                                    <Box style={{ cursor: 'pointer', marginLeft: '6px' }} display="flex" alignItems="center">
                                                        <InfoOutlined style={{ color: '#2B45D4' }} />
                                                    </Box>
                                                </CardTooltip>
                                            </Box>
                                        </CardContent>
                                        <CardContent style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', padding: 0 }}>
                                            <S.Text typeStyle="2" style={{ fontWeight: '600', color: '#1e1e1e' }}>
                                                {currencyMaskBRL(composicaoGeral?.composicaoAguardandoAprovacao?.faturamento) || '-'}
                                            </S.Text>
                                        </CardContent>
                                    </Card>
                                </Box>
                            </Container>
                        </Box>
                    )}
                </DividerSectionCard>
            ) : (
                <DividerSectionCard dividerContent={true}>
                    <S.TitleSection>Total a pagar</S.TitleSection>

                    {competenciaSelecionada && composicaoGeral?.aguardandoAprovacao > 0 && (
                        <S.AlertInfo>
                            <ReactSVG src="/faturamento/assets/icons/info.svg" wrapper="span" />
                            <p>Aguarde a aprovação de todos os prestadores para concluir a composição</p>
                        </S.AlertInfo>
                    )}

                    {isLoadingCompetenciaSelecionada ? (
                        <Box sx={{ display: 'flex', minHeight: 218, alignItems: 'center', justifyContent: 'center' }}>
                            <CircularProgress sx={{ color: '#2B45D4' }} />
                        </Box>
                    ) : (
                        <Box display="flex" flexDirection="row">
                            <Container style={{ paddingLeft: 0, paddingRight: 8 }}>
                                <Box display="flex" flexDirection="row" justifyContent="space-between">
                                    <Box my={1} mr={0.5} flexBasis="49%">
                                        <Card style={{ backgroundColor: 'rgba(43, 69, 212, 0.04)', padding: '16px' }} elevation={0}>
                                            <CardContent style={{ padding: 0 }}>
                                                <S.Text typeStyle="1">Extra teto liberado</S.Text>
                                            </CardContent>
                                            <CardContent style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', padding: 0 }}>
                                                <S.Text typeStyle="2" style={{ fontWeight: '600', color: '#1e1e1e' }}>
                                                    {currencyMaskBRL(aprovados?.composicaoAprovado?.extratetoLiberado) || '-'}
                                                </S.Text>
                                            </CardContent>
                                        </Card>
                                    </Box>

                                    <Box my={1} ml={0.5} flexBasis="49%">
                                        <Card style={{ backgroundColor: 'rgba(43, 69, 212, 0.04)', padding: '16px' }} elevation={0}>
                                            <CardContent style={{ padding: 0 }}>
                                                <S.Text typeStyle="1">Liminar liberado</S.Text>
                                            </CardContent>
                                            <CardContent style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', padding: 0 }}>
                                                <S.Text typeStyle="2" style={{ fontWeight: '600', color: '#1e1e1e' }}>
                                                    {currencyMaskBRL(aprovados?.composicaoAprovado?.liminarLiberado) || '-'}
                                                </S.Text>
                                            </CardContent>
                                        </Card>
                                    </Box>
                                </Box>

                                <Box my={1}>
                                    <Card style={{ backgroundColor: 'rgba(43, 69, 212, 0.04)', padding: '16px' }} elevation={0}>
                                        <CardContent style={{ padding: 0, display: 'flex', justifyContent: 'space-between', flexDirection: 'row' }}>
                                            <S.Text typeStyle="1">Total faturamento</S.Text>
                                        </CardContent>
                                        <CardContent style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', padding: 0 }}>
                                            <S.Text typeStyle="2">{currencyMaskBRL(aprovados?.composicaoAprovado?.faturamentoTotal) || '-'}</S.Text>
                                        </CardContent>
                                    </Card>
                                </Box>
                            </Container>

                            <Container style={{ paddingLeft: 8, paddingRight: 0 }}>
                                <Box display="flex" flexDirection="row" justifyContent="space-between">
                                    <Box my={1} mr={0.5} flexBasis="49%">
                                        <Card style={{ backgroundColor: 'rgba(43, 69, 212, 0.04)', padding: '16px' }} elevation={0}>
                                            <CardContent style={{ padding: 0 }}>
                                                <S.Text typeStyle="1">Glosa de Biometria liberado</S.Text>
                                            </CardContent>
                                            <CardContent style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', padding: 0 }}>
                                                <S.Text typeStyle="2">
                                                    {currencyMaskBRL(aprovados?.composicaoAprovado?.glosaBiometriaLiberado) || '-'}
                                                </S.Text>
                                            </CardContent>
                                        </Card>
                                    </Box>

                                    <Box my={1} ml={0.5} flexBasis="49%">
                                        <Card style={{ backgroundColor: 'rgba(43, 69, 212, 0.04)', padding: '16px' }} elevation={0}>
                                            <CardContent style={{ padding: 0 }}>
                                                <S.Text typeStyle="1">Recurso Glosa liberado</S.Text>
                                            </CardContent>
                                            <CardContent style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', padding: 0 }}>
                                                <S.Text typeStyle="2">
                                                    {currencyMaskBRL(aprovados?.composicaoAprovado?.recursoGlosaLiberado) || '-'}
                                                </S.Text>
                                            </CardContent>
                                        </Card>
                                    </Box>
                                </Box>

                                <Box my={1}>
                                    <Card style={{ backgroundColor: 'rgba(43, 69, 212, 0.04)', padding: '16px' }} elevation={0}>
                                        <CardContent style={{ padding: 0, display: 'flex', justifyContent: 'space-between', flexDirection: 'row' }}>
                                            <S.Text typeStyle="1">Total a pagar</S.Text>
                                        </CardContent>
                                        <CardContent style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', padding: 0 }}>
                                            <S.Text typeStyle="2">{currencyMaskBRL(aprovados?.composicaoAprovado?.totalAprovado) || '-'}</S.Text>
                                        </CardContent>
                                    </Card>
                                </Box>
                            </Container>
                        </Box>
                    )}

                    <S.ButtonsContainer>
                        <Button
                            iconLeft="/faturamento/assets/imgs/docs.svg"
                            themeButton="primary"
                            typeButton="ghost"
                            onClick={() => {
                                getFiltersCategoria()

                                setIsOpenModal(true)
                            }}
                        >
                            Gerar relatório
                        </Button>
                        {competenciaSelecionada && competenciaSelecionada?.fase !== 'COMPOSICAO_PAGAMENTO_FINALIZADA' && (
                            <Button
                                disabled={
                                    !disponivelParaEnvioContasAPagar ||
                                    loadingEnviarParaContas ||
                                    competenciaSelecionada?.fase === 'PROCESSAMENTO_EM_ANDAMENTO'
                                }
                                iconLeft="/faturamento/assets/imgs/dolar.svg"
                                themeButton="secondary"
                                onClick={() => handleClickSendToAccountsPayable()}
                            >
                                {loadingEnviarParaContas || competenciaSelecionada?.fase === 'PROCESSAMENTO_EM_ANDAMENTO'
                                    ? 'Processando...'
                                    : 'Enviar para contas a pagar'}
                            </Button>
                        )}
                    </S.ButtonsContainer>
                </DividerSectionCard>
            )}

            <DividerSectionCard dividerContent={true}>
                <S.TitleSection>Prestadores</S.TitleSection>

                <Box maxWidth="70%" display="flex" flexDirection="row" alignItems="center">
                    <SearchBar
                        value={fieldValues?.nome_codigo_cnpj}
                        handleOnSearch={() => {
                            setPageNumber(0)
                            setRefreshComponents(!refreshComponents)
                        }}
                        handleOnClose={() => {
                            handleOnClosePesquisa()
                        }}
                        placeholder="Procure por nome, código ou CNPJ"
                        handleOnChange={(e) => {
                            setFieldValues({
                                ...fieldValues,
                                nome_codigo_cnpj: e.target.value
                            })
                        }}
                    />

                    <Button
                        typeButton="text"
                        themeButton="primary"
                        onClick={() => handleClickToOpenFilter()}
                        style={{ width: 'fit-content', marginLeft: 16 }}
                    >
                        Filtrar
                    </Button>
                </Box>

                {/* TODO: FILTER */}

                {/* {filterOpen && ( */}
                <Box
                    display="flex"
                    flexDirection="column"
                    padding="24px"
                    style={{ backgroundColor: '#2B45D404', borderRadius: 4, margin: '24px 0', display: filterOpen ? 'block' : 'none' }}
                >
                    {/* {!loadingFilters && ( */}
                    <>
                        <S.Form key={refreshComponents as any}>
                            <S.FormRow1>
                                <Selectable
                                    label="Categoria"
                                    options={filterOptions?.categoria}
                                    onChange={(e) => {
                                        setFieldValues({
                                            ...fieldValues,
                                            categoria: e?.value
                                        })
                                    }}
                                />
                                <Selectable
                                    label="Tipo"
                                    options={filterOptions?.tipo}
                                    onChange={(e) => {
                                        setFieldValues({
                                            ...fieldValues,
                                            tipo: e?.value
                                        })
                                    }}
                                />
                                <Selectable
                                    label="Produção"
                                    options={filterOptions?.producao}
                                    onChange={(e) => {
                                        setFieldValues({
                                            ...fieldValues,
                                            producao: e?.value
                                        })
                                    }}
                                />
                            </S.FormRow1>

                            <S.FormRow2>
                                <Selectable
                                    label="Município"
                                    options={filterOptions?.Municipios}
                                    onChange={(e) => {
                                        setFieldValues({
                                            ...fieldValues,
                                            municipio: e?.value
                                        })
                                    }}
                                />
                                <Selectable
                                    label="Macrorregião"
                                    options={filterOptions?.macrorregiao}
                                    onChange={(e) => {
                                        setFieldValues({
                                            ...fieldValues,
                                            macrorregiao: e?.value
                                        })
                                    }}
                                />
                            </S.FormRow2>
                        </S.Form>
                        <Box display="flex" flexDirection="row" alignItems="center" paddingTop="24px">
                            <Switch
                                checked={priorityOnlyFilter}
                                inputProps={{ 'aria-label': 'controlled' }}
                                onChange={() => {
                                    setPriorityOnlyFilter(!priorityOnlyFilter),
                                        setFieldValues({
                                            ...fieldValues,
                                            prioritario: !priorityOnlyFilter
                                        })
                                }}
                            />
                            <Typography color="#00000056" fontSize="16px" fontWeight="600" marginLeft={'20px'}>
                                Somente prioritário
                            </Typography>
                        </Box>
                    </>
                    {/* )} */}

                    <Box display="flex" flexDirection="row" alignItems="center" justifyContent="flex-end" paddingTop="24px">
                        <Button
                            typeButton="text"
                            themeButton="gray"
                            onClick={() => (setFieldValues(resetValues), setRefreshComponents(!refreshComponents), setPriorityOnlyFilter(false))}
                            style={{ width: 'fit-content', marginLeft: 16 }}
                        >
                            Limpar
                        </Button>
                        <Button
                            typeButton="text"
                            themeButton="primary"
                            onClick={() => {
                                setPageNumber(0)
                                getPrestadoresListContent()

                                if (status === 1) {
                                    getAguardandoAprovacao()
                                } else {
                                    getAprovados()
                                }
                                // setRefreshComponents(!refreshComponents)
                            }}
                            style={{ width: 'fit-content', marginLeft: 16 }}
                        >
                            Buscar
                        </Button>
                    </Box>
                </Box>
                {/* )} */}

                {/* TODO: LISTAGEM PRESTADORES */}

                {loadingProviders ? (
                    Array.from({ length: 5 }).map((_, idx) => (
                        <Skeleton
                            key={idx}
                            baseColor="#F6F6F9"
                            highlightColor="#d0d0d0"
                            borderRadius={4}
                            style={{ height: 60, marginTop: '16px', width: '100%' }}
                        />
                    ))
                ) : // <AnimatedLoadingLottie style={{ display: 'flex', alignItems: 'center', margin: 'auto', minHeight: 400 }} />
                prestadoresPage?.length === 0 ? (
                    <div style={{ marginTop: '24px' }}>
                        <NoContent title="Por enquanto não há prestadores" />
                    </div>
                ) : (
                    <Box
                        mt={3}
                        maxWidth="100%"
                        // onClick={() => route.push('/composicao-pagamento/detalhamento')}
                    >
                        <>
                            {status === 1 ? (
                                <TablePagination
                                    titles={tableAguardandoAprovacao}
                                    selectIdField={'id'}
                                    enableSelect={true}
                                    padding={1}
                                    pagination={guiaPagination}
                                    showContentExpanded={true}
                                    checkSelected={providerSelected}
                                    handleSelect={setProviderSelected}
                                    values={prestadoresPage?.map((item: IContentList) => {
                                        return {
                                            ...item,
                                            id: item?.informacoes_gerais?.id,
                                            idPrestador: item?.informacoes_gerais?.id,
                                            prestador: (
                                                <ProviderTableContent
                                                    item={item}
                                                    handleClickToFavorite={() => priorizarPrestador(item?.informacoes_gerais?.id)}
                                                />
                                            ),
                                            tipoPrestador: item?.informacoes_gerais?.tipo ? item?.informacoes_gerais?.tipo : '---',
                                            teto: currencyMaskBRL(item?.composicao_geral?.teto) || '-',
                                            producao: currencyMaskBRL(item?.composicao_geral?.producao) || '-',
                                            extrateto: currencyMaskBRL(item?.composicao_geral?.extrateto) || '-',
                                            totalAprovacao: currencyMaskBRL(item?.total_aprovado?.total_aprovado) || '-',
                                            iconJoinha: (
                                                <S.BtnLike disabled={providerSelected?.length <= 0}>
                                                    <ReactSVG
                                                        src={`/faturamento/assets/icons/like.svg`}
                                                        wrapper="span"
                                                        onClick={() => {
                                                            if (providerSelected?.length <= 0) {
                                                                setOpenApproveModal(true)
                                                                setModalApproveId(item?.informacoes_gerais?.id)
                                                            }
                                                        }}
                                                    />
                                                </S.BtnLike>
                                            )
                                        }
                                    })}
                                    customGridStyles={'2.0fr 1.4fr 1fr 1.1fr 1.0fr 1.1fr 0.3fr 0.3fr'}
                                />
                            ) : (
                                <TablePagination
                                    titles={tableAprovados}
                                    selectIdField={'id'}
                                    enableSelect={false}
                                    padding={1}
                                    pagination={guiaPagination}
                                    showContentExpanded={true}
                                    checkSelected={providerSelected}
                                    handleSelect={setProviderSelected}
                                    values={prestadoresPage?.map((item: IContentList) => {
                                        return {
                                            ...item,
                                            id: item?.informacoes_gerais?.id,
                                            prestador: (
                                                <ProviderTableContent
                                                    item={item}
                                                    handleClickToFavorite={() => priorizarPrestador(item?.informacoes_gerais?.id)}
                                                />
                                            ),
                                            tipoPrestador: item?.informacoes_gerais?.tipo ? item?.informacoes_gerais?.tipo : '---',
                                            liberado: currencyMaskBRL(item?.total_aprovado?.liberado_total) || '-',
                                            faturamento: currencyMaskBRL(item?.total_aprovado?.faturamento_total) || '-',
                                            totalAprovado: currencyMaskBRL(item?.total_aprovado?.total_aprovado) || '-'
                                        }
                                    })}
                                    customGridStyles={'3.9fr 2.5fr 0.8fr 1.1fr 1.0fr 0.3fr'}
                                />
                            )}
                        </>
                    </Box>
                )}
            </DividerSectionCard>

            {status === 1 && (
                <DividerSectionCard dividerContent={true}>
                    <Box display="flex" flexDirection="row" justifyContent="space-between">
                        <Box style={{ padding: '12px 24px', backgroundColor: 'rgba(43, 69, 212, 0.04)', width: 'fit-content', borderRadius: 8 }}>
                            <Box display="flex" flexDirection="row" alignItems="center">
                                <Typography fontSize={14} color="#00000056" fontWeight="600">
                                    Prestadores selecionados
                                </Typography>
                                <Typography fontSize={18} ml={3} color="#2B45D4" fontWeight="600">
                                    {numberOfProviders > 0 ? numberOfProviders : guiaPagination?.totalRegistros}
                                </Typography>
                            </Box>
                        </Box>

                        <Box display="flex" flexDirection="row" justifyContent="space-between">
                            <Box style={{ padding: '12px 24px', backgroundColor: 'rgba(43, 69, 212, 0.04)', width: 'fit-content', borderRadius: 8 }}>
                                <Box display="flex" flexDirection="row" alignItems="center">
                                    <Typography fontSize={14} color="#00000056" fontWeight="600">
                                        Total a pagar
                                    </Typography>
                                    <Typography fontSize={18} ml={3} color="#2B45D4" fontWeight="600">
                                        {numberOfProviders > 0 ? currencyMaskBRL(totalValue) || '-' : currencyMaskBRL(totalAPagar)}
                                    </Typography>
                                </Box>
                            </Box>
                            <Button
                                themeButton="warning"
                                style={{ width: 'fit-content', marginLeft: 16 }}
                                // disabled={providerSelected?.length <= 0}
                                disabled={prestadoresPage?.length === 0}
                                onClick={() => {
                                    setOpenApproveModal(true)
                                }}
                            >
                                <Box display="flex" flexDirection="row" alignItems="center">
                                    <ReactSVG
                                        src="/faturamento/assets/icons/ic-like.svg"
                                        beforeInjection={(svg) => {
                                            const pathElement = svg.querySelector('path')
                                            if (pathElement) {
                                                pathElement.setAttribute('fill', '#000')
                                            }
                                        }}
                                    />
                                    <Typography fontSize={16} fontWeight={600} ml={1}>
                                        {providerSelected?.length > 0 ? 'Aprovar' : 'Aprovar todos'}
                                    </Typography>
                                </Box>
                            </Button>
                        </Box>
                    </Box>
                </DividerSectionCard>
            )}

            <Modal
                isOpen={isOpenModal}
                title={'Relatório dos prestadores aprovados'}
                titleStyle={{ fontSize: '2.4rem', fontWeight: '600' }}
                style={{ padding: '40px' }}
                onClose={() => {
                    setIsOpenModal(!isOpenModal)
                }}
            >
                <S.WrapperModal>
                    {categorias?.length > 0 && (
                        <>
                            <p style={{ marginTop: '24px' }}>Categoria</p>
                            <S.Grid>
                                {categorias?.map((categoria) => (
                                    <label key={categoria} htmlFor={categoria}>
                                        <Checkbox id={categoria} value={categoria} name={categoria} onChange={(e) => addOrRemove(e.target.value)} />
                                        {categoria}
                                    </label>
                                ))}
                            </S.Grid>
                        </>
                    )}

                    <S.Content>
                        <p>Formato do arquivo</p>

                        <label htmlFor="PDF">
                            <Checkbox
                                id="PDF"
                                checked={opcoesRelatorioSelecionada?.typeDoc === 'PDF'}
                                value={'PDF'}
                                onChange={(i) => {
                                    setOpcoesRelatoriosSelecionada((prev) => ({
                                        ...prev,
                                        typeDoc: i.target.value === prev.typeDoc ? null : i.target.value
                                    }))
                                }}
                                name="typeDoc"
                            />
                            PDF
                        </label>
                        <label htmlFor="Excel">
                            <Checkbox
                                id="excel"
                                checked={opcoesRelatorioSelecionada?.typeDoc === 'XLSX'}
                                value={'XLSX'}
                                onChange={(i) => {
                                    setOpcoesRelatoriosSelecionada((prev) => ({
                                        ...prev,
                                        typeDoc: i.target.value === prev.typeDoc ? null : i.target.value
                                    }))
                                }}
                                name="typeDoc"
                            />
                            Excel
                        </label>
                    </S.Content>

                    <S.ContainerButton>
                        <Button typeButton="text" themeButton="gray" onClick={() => setIsOpenModal(false)}>
                            Cancelar
                        </Button>
                        <Button
                            typeButton="flat"
                            disabled={enableGerarRelatorioBtn || opcoesRelatorioSelecionada?.typeDoc === null}
                            themeButton="secondary"
                            onClick={() => handleGerarRelatorio()}
                        >
                            Gerar
                        </Button>
                    </S.ContainerButton>
                </S.WrapperModal>
            </Modal>
            {/*
            <ModalGenericComponent
                title="Aprovar a composição?"
                openModal={openApproveModal}
                actionButton="Aprovar"
                cancelButton="Cancelar"
                actionFunction={() => handleClickToOpenApproveModal()}
                setOpenModal={() => setOpenApproveModal(false)}
            >

            </ModalGenericComponent> */}

            <Modal
                isOpen={openApproveModal}
                title="Aprovar a composição?"
                titleStyle={{ fontSize: '2.4rem', fontWeight: '600' }}
                style={{ padding: '40px' }}
                onClose={() => setOpenApproveModal(false)}
            >
                <S.WrapperApproveModal>
                    <S.BtnAction>
                        <Button
                            themeButton="gray"
                            typeButton="text"
                            onClick={() => {
                                setOpenApproveModal(false)
                                // setModalApproveId(null)
                            }}
                        >
                            Cancel
                        </Button>
                        <Button disabled={aproveBtnModal} themeButton="secondary" typeButton="flat" onClick={() => handleClickToOpenApproveModal()}>
                            Aprovar
                        </Button>
                    </S.BtnAction>
                </S.WrapperApproveModal>
            </Modal>
        </Layout>
    )
}

export default ComposicaoPagamentoTemplate
