import React, { HtmlHTMLAttributes } from 'react'
import { Content, Wrapper } from './styles'
import { useFiltroContext } from '../../Root'

export default function FiltroLateralContent(props: HtmlHTMLAttributes<HTMLDivElement>) {
    const { setShow } = useFiltroContext()

    return (
        <>
            <Wrapper
                onClick={(e) => {
                    e.preventDefault()
                    setShow(false)
                }}
            />
            <Content {...props} />
        </>
    )
}
