import styled from 'styled-components'
import theme from 'styles/theme'

export const Container = styled.div`
    max-width: 1400px;
    padding: 16px;
    margin: 0 auto;
    background-color: #fff;
    border-radius: 8px;
`

export const FormInput = styled.form`
    gap: 8px;
`

export const ProceduresButtons = styled.div`
    display: flex;
    padding-top: 35px;

    button:first-of-type {
        margin-right: 16px;
    }
`

export const ButtonContainer = styled.div`
    display: flex;
    padding: 25px 0 35px 0;
    justify-content: flex-end;
`

export const ContainerInputs = styled.div`
    .row {
        display: flex;
        margin: 40px 0px;
        gap: 16px;

        .rule-name {
            flex: 1;
        }
        .effective-date {
            flex: 1;
        }
        .medical-bills {
            flex: 1;
        }
        .first-verification {
            flex: 1;
        }
        .second-verification {
            flex: 1;
        }
        .provider {
            flex-basis: 33%;
        }

        @media screen and (max-width: 1024px) {
            flex-direction: column;
            gap: 16px;
            margin: 0 0px 16px 0;

            input,
            label {
                font-size: 1.4rem;
            }
        }
    }
`

export const ProvidersList = styled.div`
    display: flex;
    align-items: center;
    gap: 1.6rem 2.4rem;
    flex-wrap: wrap;
    margin-top: 2.4rem;
`

export const ProviderItem = styled.div`
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.4rem 0.8rem;
    border-radius: 0.4rem;
    background-color: rgba(43, 69, 212, 0.04);

    p {
        font-size: 1.4rem;
        line-height: 2.4rem;
        font-weight: 600;
        color: ${theme.colors.black[88]};
    }

    .closeButton {
        width: 2.4rem;
        height: 2.4rem;
        padding: 0.58rem;
        cursor: pointer;

        > div {
            width: 100%;
            height: 100%;
            line-height: 0;
        }

        svg {
            width: 100%;
            height: 100%;
            path {
                fill: #ff0073;
            }
        }
    }
`
