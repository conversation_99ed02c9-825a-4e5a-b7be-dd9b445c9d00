import React from 'react'
import Layout from 'components/molecules/Layout'
import RegraExcludenciaVisualizarTemplate from 'src/templates/FaturamentoOperadora/ParametrosFaturamentoTemplates/RegraExcludencia/RegraExcludenciaVisualizarTemplate'

const Detalhes = () => {
    // return <RegraExcludenciaVisualizarTemplate />;
    return <div>teste</div>
}

// Obtendo o Layout para a página
// Detalhes.getLayout = function getLayout(page: JSX.Element) {
// 	return <Layout isLoggedIn={true}>{page}</Layout>;
// };

export default Detalhes
