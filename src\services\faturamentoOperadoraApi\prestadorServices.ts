import { AxiosResponse } from 'axios'
import { IPagePrestador, IPrestadorQuery } from 'types/faturamentoOperadora/provider'
import { IPrestadorDTO, IPrestadorEnderecoDTO } from 'types/prestador'
import { ObjectUtils } from 'utils/objectUtils'
import { apiCredenciamento } from '../apis/apiCredenciamento'
import { IPageResult } from 'types/pagination'

export interface IGetPrestadorProps {
    page?: number
    size?: number
    cpfCnpj?: string
    nomeCompletoNomeFantasia?: string
    nomeCompletoNomeFantasiaRazaoSocialCpfCnpj?: string
}

const baseUrl = '/prestador'

export class PrestadorService {
    static async getPrestador(props?: IGetPrestadorProps) {
        const params = ObjectUtils.propsToParams(props)
        const url = props ? `${baseUrl}?${params}` : baseUrl
        return apiCredenciamento.get<IPageResult<IPrestadorQuery>>(url)
    }

    static async getPrestadorByConfigID(id: string): Promise<AxiosResponse<IPrestadorQuery>> {
        return apiCredenciamento.get(`${baseUrl}/ID/${id}`)
    }

    static async getByID(id: string): Promise<AxiosResponse<IPrestadorQuery>> {
        return apiCredenciamento.get(`${baseUrl}/${id}`)
    }

    static async getPrestadorVinculado(uuidUsuario: string): Promise<AxiosResponse<IPrestadorDTO>> {
        const url = `${baseUrl}/sso/${uuidUsuario}`

        return apiCredenciamento.get(url)
    }

    static async getListaPrestadores(idsPrestadores: string[], page = 0, size = 10): Promise<AxiosResponse<IPageResult<IPrestadorEnderecoDTO>>> {
        let params = `page=${page}&size=${size}`
        idsPrestadores?.forEach((id) => {
            params += `&idsPrestadores=${id}`
        })
        const url = `${baseUrl}/listar-prestadores?${params}`
        return apiCredenciamento.get(url)
    }
}
