import { IPageResult } from 'types/pagination'
import { ModuloCobranca, SituacaoLote, TipoEnvio, TipoLote } from './common/enums'

export type IPageLoteRecusa = IPageResult<ILoteRecusaQuery>
export interface ILoteRecusaQuery {
    loteCobrancaId: string
    nomeUsuario: string
    prestadorId?: string
    numeroLote: number
    dataEnvio: string
    dataRecusa?: string
    tipoLote: TipoLote
    tipoEnvio: TipoEnvio
    moduloCobranca?: ModuloCobranca
    situacaoLote: SituacaoLote
}
