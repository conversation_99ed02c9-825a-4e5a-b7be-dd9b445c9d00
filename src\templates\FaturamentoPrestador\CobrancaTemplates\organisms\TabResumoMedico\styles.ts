/* eslint-disable prettier/prettier */
import styled from 'styled-components'
import Button from 'components/atoms/Button'
import { Grid } from '@mui/material';

export const Container = styled.div`
    padding: 24px;
    background: #ffffff;
    display: flex;
    flex-direction: column;
    gap: 32px;
`
export const Buttons = styled.div`
    display: flex;
    flex-direction: row;
    gap: 16px;
    button {
        width: fit-content;
    }
`

export const CustomButton = styled(Button)`
	display: flex;
	gap: 4px;
	justify-content: center;
	align-items: center;
	padding: 8px 16px;
	border: none;
	border-radius: 8px;
	background: rgba(43, 69, 212, 0.04);
	color: #2b45d4;
	cursor: pointer;
	width: fit-content;
	svg {
		height: 16px;
	}
	svg path {
		fill: #2b45d4 !important;
	}

    span {
        font-weight: 400 !important;
    }
`;

export const TableContent = styled.div``;

export const Top = styled(Grid)`
	p{
		font-size: 1.2rem;
		font-weight: 600;
		color: rgba(0,0,0,0.88);
		padding: 10px 5px;
	}
`;

export const List = styled(Grid)`
	background-color: rgba(43,69,212,0.04);
	border-radius: 8px;
	align-items: center;
	padding: 15px 10px;
	
	& + div{
		margin-top: 10px;
	}

	p{
		font-size: 1.4rem;
		font-weight: 600;
		color: rgba(0,0,0,0.88);
	}

	/* BADGE STYLES */

	.badge{
		border: 1px solid black;
		border-radius: 8px;
		padding: 8px 10px;
		width: fit-content;
	}


	.box-RED {
		border: 1px solid rgba(255, 88, 76, 1);
		background-color: rgba(244, 67, 54, 0.04);
		color: rgba(255, 88, 76, 1);
	}

	.box-ORANGE {
		border: 1px solid rgba(229, 149, 0, 1);
		background-color: rgba(255, 176, 0, 0.04);
		color: rgba(229, 149, 0, 1);
	}

	.box-GREEN {		
		border: 1px solid rgba(56, 180, 73, 1);
		background-color: rgba(56, 180, 73, 0.04);
		color: rgba(56, 180, 73, 1);
	}

	.box-BLUE {
		border: 1px solid #c4cdff;
		background-color: rgba(196, 205, 255, 0.1);
		color: rgba(36, 58, 178, 1);
	}

	
`;

