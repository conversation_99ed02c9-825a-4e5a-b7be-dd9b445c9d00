import styled from 'styled-components'

export const ContentItem = styled.div`
    display: flex;
`

export const Item = styled.div`
    display: flex;
    margin: 16px;
    padding: 8px 16px;
    flex: 1;
    border-radius: 4px;
    flex-direction: column;
    background: rgba(0, 0, 0, 0.04);

    p {
        font-weight: 600;
        font-size: 14px;
        line-height: 24px;
        color: rgba(0, 0, 0, 0.88);
        margin: 0;
    }

    span {
        color: rgba(0, 0, 0, 0.56);
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
    }
`

export const ContentItemResult = styled.div`
    display: grid;
    padding: 16px;
    background: rgba(43, 69, 212, 0.04);
    border-radius: 8px;
    align-items: center;
    grid-template-columns: 1fr 1fr 2.5fr 0.4fr 0.4fr;
`

export const ContentItemProcedure = styled.div`
    display: grid;
    padding: 24px 16px 0 16px;
    grid-template-columns: 1.5fr 3fr;
`

export const ContentItemResultProcedure = styled.div`
    display: grid;
    padding: 16px;
    margin-top: 8px;
    background: #e8ebfd;
    border-radius: 8px;
    align-items: center;
    margin-bottom: 4px;
    grid-template-columns: 1.5fr 3fr;
`

export const ItemResult = styled.div`
    display: flex;
    flex-direction: column;

    p {
        font-weight: 600;
        font-size: 14px;
        line-height: 24px;
        color: rgba(0, 0, 0, 0.88);
        margin: 0;
    }

    span {
        color: rgba(0, 0, 0, 0.56);
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
    }
`
