/* eslint-disable @typescript-eslint/no-explicit-any */

import moment from 'moment'

/* eslint-disable no-param-reassign */

export const formatDate = (date: any) => {
    if (!date) return ''

    if (date === '') return ''

    if (date === null) return ''

    date = date.toString()

    date = date.replace(/[^\d]/g, '')

    date = date.substring(0, 8)

    if (date.length > 2) {
        date = date.replace(/^(\d{2})(\d*)/, '$1/$2')
    }

    if (date.length > 5) {
        date = date.replace(/(.{5})(\d*)/, '$1/$2')
    }

    return date
}

export const formatDatePTBR = (date: string) => {
    if (!date) return null

    if (date === '') return null

    return new Date(date).toLocaleDateString('pt-BR', {
        timeZone: 'UTC',

        day: '2-digit',

        month: '2-digit',

        year: 'numeric'
    })
}

export const formatDateTimePTBR = (date: string) => {
    if (!date) return null
    if (date === '') return null
    return moment(date).format('DD/MM/YYYY HH:mm')
}

// format('00000123456')

// format('0000000000')

// format('000000000000000')

// format('123456')

// format('000321700')

// format('000846600')
