import styled from 'styled-components'

export const Wrapper = styled.div`
    background: #fff;
    .contentInput {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin-bottom: 32px;

        div {
            max-width: 592px;
        }

        .contentNumber {
            display: flex;
            .pageNumber {
                font-weight: 400;
                font-size: 16px;
                line-height: 24px;
                margin-right: 20px;
            }
        }
    }
`

export const ContentDropLot = styled.div`
    display: flex;
    align-items: center;
    justify-content: space-between;
    p {
        font-family: 'Open Sans';
        font-style: normal;
        font-weight: 400;
        font-size: 16px;
        line-height: 24px;
        /* identical to box height, or 150% */

        color: #000000;
    }
`

export const HeaderLabel = styled.div`
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    margin-bottom: 16px;
    padding: 0 8px;

    div {
        p {
            font-weight: 600;
            font-size: 12px;
            line-height: 16px;
        }
    }
`
export const RowButtons = styled.div`
    display: flex;
    gap: 16px;
`
export const ContentCancelLotes = styled.div`
    display: flex;
    align-items: center;
    gap: 19px;
    padding-left: 12px;
    cursor: pointer;

    p {
        font-weight: 600;
        font-size: 12px;
        line-height: 16px;
        color: rgba(0, 0, 0, 0.88);
    }

    svg path {
        height: 18px;
        width: 18px;
        fill: rgba(0, 0, 0, 0.56);
    }
`
