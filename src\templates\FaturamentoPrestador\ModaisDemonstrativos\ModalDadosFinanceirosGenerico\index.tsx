import * as S from './styles'

import Modal from 'components/atoms/Modal'
import Button from 'components/atoms/Button'
import { padding } from 'polished'

type DetalhesItem = {
    nome: string
    valor: string
}

type DadosFinanceirosGenericoProps = {
    openModal: boolean
    setOpenModal: (value: React.SetStateAction<boolean>) => void
    dadosFinanceiros: DetalhesItem[]
    titulo?: string
}

const ModalDadosFinanceirosGenerico = ({ openModal, setOpenModal, dadosFinanceiros, titulo = 'Detalhamento' }: DadosFinanceirosGenericoProps) => {
    return (
        <Modal
            style={{ width: '540px', padding: '20px' }}
            isOpen={openModal}
            onClose={() => {
                setOpenModal(false)
            }}
        >
            <S.ModalContainer>
                <S.TitleModal>{titulo}</S.TitleModal>
                <S.ListCard>
                    <>
                        {dadosFinanceiros?.map((item, index) => {
                            return (
                                <S.Card key={index}>
                                    <p>{item?.nome}</p>
                                    <p>{item?.valor}</p>
                                </S.Card>
                            )
                        })}
                    </>
                </S.ListCard>
                <S.CardAction>
                    <Button
                        themeButton="gray"
                        typeButton="text"
                        onClick={() => {
                            setOpenModal(false)
                        }}
                    >
                        Cancelar
                    </Button>
                </S.CardAction>
            </S.ModalContainer>
        </Modal>
    )
}

export default ModalDadosFinanceirosGenerico
