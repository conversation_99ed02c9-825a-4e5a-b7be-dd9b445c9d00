import { Box, CircularProgress } from '@mui/material'
import Checkbox from 'components/atoms/CheckBox'
import ButtonDropLots from 'components/molecules/Buttons/ButtonDownloadLots'
import DropLote from 'components/molecules/DropLote'
import NoContent from 'components/molecules/NoContent'
import Pagination from 'components/molecules/Pagination'
import moment from 'moment'
import React, { useEffect, useState } from 'react'
import { useAuth } from 'src/hooks/auth'
import { CobrancaServices } from 'src/services/faturamentoPrestadorApi/cobrancaServices'
import { ModuloEnum } from 'src/services/faturamentoPrestadorApi/geracao-recurso-glosa/enuns'
import { IPrestadorAnaliseQuery } from 'types/cobrancaPrestador/prestadorAnalise'
import { ILoteRecusaQuery, IPageLoteRecusa } from 'types/cobrancaPrestador/visaoRecusa'
import { IPagination } from 'types/common/pagination'
import { retiraSequencial } from 'utils/functions'
import { PaginationHelper } from 'utils/helpers/paginationHelper'
import { capitalize } from 'utils/stringUtils'
import { ICheckeboxFilter } from '../TabLoteMedico'
import * as S from './styles'

type LotesRecusadosProps = {
    competenciaSelecionada: IPrestadorAnaliseQuery
    searchLote: string
    forceUpdate: boolean
    refreshList: boolean
    setLotes?: React.Dispatch<React.SetStateAction<IPageLoteRecusa>>
    lotes?: IPageLoteRecusa
    loadingLotes: boolean
    setLoadingLotes: React.Dispatch<React.SetStateAction<boolean>>
    checkboxFilter: ICheckeboxFilter
    setCheckboxFilter: React.Dispatch<React.SetStateAction<ICheckeboxFilter>>
}

function createDropLot(item: ILoteRecusaQuery) {
    return [
        {
            component: (
                <div>
                    <p>{item?.numeroLote}</p>
                </div>
            )
        },
        {
            component: (
                <div>
                    <p>{moment(item?.dataEnvio).format('DD/MM/YYYY [-] HH:mm')}</p>
                </div>
            )
        },
        {
            component: (
                <div>
                    <p>{item?.dataRecusa ? moment(item?.dataRecusa).format('DD/MM/YYYY [-] HH:mm') : '---'}</p>
                </div>
            )
        },
        {
            component: (
                <div>
                    <p>{capitalize(item?.nomeUsuario)}</p>
                </div>
            )
        },

        {
            component: (
                <div>
                    <p>{item?.tipoEnvio === 'ELETRONICO' ? 'Eletrônico' : capitalize(item?.tipoEnvio)}</p>
                </div>
            )
        }
    ]
}

const handleDownloadRelatorioErrosPdf = (idLote) => {
    CobrancaServices.getRelatorioErrosPdf(idLote).then((response) => {
        console.log(response.headers)
        const fileName = response.headers['content-disposition'].split('filename=')[1] ?? 'download.pdf'
        const url = window.URL.createObjectURL(response.data)
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', fileName) //or any other extension
        document.body.appendChild(link)
        link.click()
    })
}

const ListaLotesRecusados = ({
    competenciaSelecionada,
    searchLote,
    forceUpdate,
    setLotes,
    lotes,
    refreshList,
    loadingLotes,
    setLoadingLotes,
    checkboxFilter,
    setCheckboxFilter
}: LotesRecusadosProps) => {
    const { prestadorVinculado } = useAuth()
    const [pagination, setPagination] = useState<IPagination>()
    const [numberPage, setNumberPage] = useState(0)

    const labels: string[] = ['Lote', 'Data de envio', 'Data de recusa', 'Usuário', 'Envio']

    const returnModulo = () => {
        if (checkboxFilter.lotesCobranca && checkboxFilter.lotesRecursoGlosa) {
            return null
        }
        if (!checkboxFilter.lotesCobranca && !checkboxFilter.lotesRecursoGlosa) {
            return 'EMPTY'
        }
        if (checkboxFilter.lotesCobranca && !checkboxFilter.lotesRecursoGlosa) {
            return ModuloEnum.MEDICO
        }
        if (checkboxFilter.lotesRecursoGlosa && !checkboxFilter.lotesCobranca) {
            return ModuloEnum.RECURSO
        }
    }

    const carregarLotes = (modulo: ModuloEnum | 'EMPTY' = null) => {
        setLoadingLotes(true)
        if (modulo === 'EMPTY') {
            setLotes(null)
            setLoadingLotes(false)
        } else {
            CobrancaServices.getLotesVisaoRecusa(competenciaSelecionada?.competencia, prestadorVinculado.uuid, {
                filtroNumeroLote: searchLote !== '' ? retiraSequencial(searchLote) : null,
                modulo,
                page: numberPage,
                size: 10
            })
                .then(({ data }) => {
                    setLotes(data)

                    const objectPagination = PaginationHelper.parserPagination<ILoteRecusaQuery>(data, setNumberPage)
                    setPagination(objectPagination)
                })
                .finally(() => setLoadingLotes(false))
        }
    }

    useEffect(() => {
        carregarLotes(returnModulo())
    }, [numberPage, refreshList, checkboxFilter])

    return (
        <>
            <S.CheckboxWrapper>
                <Checkbox
                    checked={checkboxFilter.lotesCobranca}
                    onCheck={() => setCheckboxFilter({ ...checkboxFilter, lotesCobranca: !checkboxFilter.lotesCobranca })}
                    label="Lotes de cobrança"
                />

                <Checkbox
                    checked={checkboxFilter.lotesRecursoGlosa}
                    onCheck={() => setCheckboxFilter({ ...checkboxFilter, lotesRecursoGlosa: !checkboxFilter.lotesRecursoGlosa })}
                    label="Lotes de recurso de glosa"
                />
            </S.CheckboxWrapper>
            {loadingLotes ? (
                <Box sx={{ display: 'flex', minHeight: 218, alignItems: 'center', justifyContent: 'center' }}>
                    <CircularProgress sx={{ color: '#2B45D4' }} />
                </Box>
            ) : (
                <S.Wrapper>
                    {!lotes || lotes?.content.length === 0 ? (
                        <NoContent title="No momento não existe nenhum lote recusado" path="/parametros/regras-de-excludencia/nova-regra" />
                    ) : (
                        <>
                            <div className="contentInput">
                                <div className="contentNumber">
                                    <span className="pageNumber">
                                        {pagination?.paginaAtual * pagination?.linhasPorPagina + 1} -{' '}
                                        {lotes?.content?.length + pagination?.paginaAtual * pagination?.linhasPorPagina}
                                        {' de '}
                                        {pagination?.totalRegistros}
                                    </span>
                                </div>
                            </div>

                            <S.HeaderLabel>
                                {labels.map((item, index) => (
                                    <div key={index}>
                                        <p>{item}</p>
                                    </div>
                                ))}
                            </S.HeaderLabel>

                            {lotes?.content?.map((lot, index) => {
                                return (
                                    <DropLote items={createDropLot(lot)} key={index}>
                                        <S.ContentDropLot>
                                            <p>Relatório de erros</p>
                                            <S.RowButtons>
                                                <ButtonDropLots icon={'/faturamento/assets/icons/download.svg'}>
                                                    <span onClick={() => handleDownloadRelatorioErrosPdf(lot?.loteCobrancaId)}>Baixar PDF</span>
                                                </ButtonDropLots>
                                            </S.RowButtons>
                                        </S.ContentDropLot>
                                    </DropLote>
                                )
                            })}
                            <div style={{ marginTop: '40px' }}>
                                <Pagination
                                    totalPage={pagination?.totalPaginas}
                                    totalRegister={pagination?.totalRegistros}
                                    actualPage={pagination?.paginaAtual}
                                    setNumberPage={(page) => setNumberPage(page)}
                                />
                            </div>
                        </>
                    )}
                </S.Wrapper>
            )}
        </>
    )
}

export default ListaLotesRecusados
