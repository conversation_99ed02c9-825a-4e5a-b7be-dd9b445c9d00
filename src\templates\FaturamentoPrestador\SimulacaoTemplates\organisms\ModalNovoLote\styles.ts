/* eslint-disable prettier/prettier */
import styled from 'styled-components'
export const ContentModal = styled.div`
    display: flex;
    overflow-y: auto;
    flex-direction: column;

    p {
        margin: 16px 0px;
    }
`

export const RowBoxs = styled.div`
    display: flex;
    flex-direction: row;
    gap: 48px;
`
export const ContainerBottom = styled.div`
    display: flex;
    justify-content: end;
    align-items: center;
    gap: 10px;
    margin-top: 2rem;
    margin-bottom: 0.5rem;
    button {
        width: fit-content;
        padding: 12px 24px;
    }
    button > span {
        font-size: 1.4rem !important;
    }
`
export const ContainerInputs = styled.div`
    display: flex;
    flex-direction: column;
    gap: 24px;
`

export const Spinner = styled.div`
    display: flex;
    align-items: center;

    .loader {
        width: 28px;
        height: 28px;
        border: 4px solid #fff;
        border-bottom-color: transparent;
        border-radius: 50%;
        display: inline-block;
        box-sizing: border-box;
        animation: rotation 1s linear infinite;
    }

    @keyframes rotation {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }
`

export const TipoArquivoLoteList = styled.div`
    display: flex;
    flex-direction: column;
    gap: 8px;
`

export const TipoArquivoLoteOption = styled.div`
    padding: 16px;
    border: 1px solid rgba(0, 0, 0, 0.16);
    border-radius: 8px;

    cursor: pointer;

    span {
        font-weight: 600;
        font-size: 14px;
        line-height: 24px;
        color: rgba(0, 0, 0, 0.88);
    }

    p {
        margin: 0;
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
        color: rgba(0, 0, 0, 0.56);
    }

    transition: background-color 0.2s;

    :hover {
        background-color: rgba(0, 0, 0, 0.04);
    }
`

export const ModeloCSVList = styled.div`
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
`

export const ModeloCSVOption = styled.div`
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 12px 24px;
    gap: 12px;
    background: rgba(0, 0, 0, 0.04);
    border-radius: 64px;

    cursor: pointer;

    .icon {
        div {
            line-height: 1;
            svg path {
                fill: rgba(0, 0, 0, 0.56);
                transition: fill 0.2s;
            }
        }
    }

    .label {
        font-weight: 600;
        font-size: 16px;
        line-height: 24px;
        color: rgba(0, 0, 0, 0.56);
        transition: color 0.2s;
    }

    transition: all 0.2s;

    :hover {
        background-color: rgba(43, 69, 212, 0.04);
        box-shadow: inset 0px 0px 0px 2px #2b45d4;

        .label {
            color: #2b45d4;
        }

        .icon svg path {
            fill: #2b45d4;
        }
    }
`

export const ErrorsWrapper = styled.div`
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
`

export const TitleErrors = styled.p`
    font-weight: bold;
    color: rgba(219, 60, 49, 1);
`

export const PresentationErrors = styled.code`
    max-height: 90px;
    height: 90px;
    background-color: rgba(0, 0, 0, 0.1);
    overflow-x: hidden;
    padding: 6px;
`
