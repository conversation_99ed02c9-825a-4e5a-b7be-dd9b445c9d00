import styled from 'styled-components'
import theme from 'styles/theme'

export const Container = styled.div`
    display: flex;
    flex-direction: column;
    gap: 2.4rem;
    margin-bottom: 5.6rem;
`

export const Header = styled.div`
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2.4rem;
`

export const RefreshButton = styled.div`
    width: 5.6rem;
    height: 5.6rem;
    border-radius: 50%;
    display: grid;
    place-items: center;
    cursor: pointer;
    background-color: rgba(43, 69, 212, 0.04);

    transition: background-color 0.2s;
    &:hover {
        background-color: ${theme.colors.black[16]};
    }

    .refresh-icon {
        div {
            line-height: 0;
        }
    }
`

export const FiltersWrapper = styled.div`
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
    background-color: #ffffff;
    border-radius: 0.8rem;
`

export const Content = styled.div`
    display: flex;
    flex-direction: column;
    gap: 3.2rem;
`

export const Filters = styled.div`
    margin: 0 1.6rem 1.6rem;
    display: flex;
    align-items: center;
    gap: 2.4rem;

    .checkbox-label {
        margin-left: 0.4rem;
        font-size: 1.6rem;
        line-height: 2.4rem;
        font-weight: 400;
        color: ${theme.colors.black[88]};
    }
`
