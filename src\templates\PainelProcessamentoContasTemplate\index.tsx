import React, { useCallback, useEffect, useMemo, useState } from 'react'
import * as S from './styles'
import PageTitle from 'components/molecules/PageTitle'
import { ICompetenciaQuery } from 'types/analiseContas/competencia'
import { IExercicioQuery } from 'types/analiseContas/exercicio'
import { AnaliseContasService } from 'src/services/analiseContasApi/analiseContasServices'
import CardSliderCompetencias from 'components/molecules/CardSliderCompetencias'
import { DateUtils } from 'utils/dateUtils'
import moment from 'moment'
import DadosQuantitativos from './DadosQuantitativos'
import ListaProcessamentoPrestadores from './ListaProcessamentoPrestadores'
import { RegistrosProcessamentoContasService } from 'src/services/processadorRegrasApi/RegistrosProcessamentoContas'
import { IProcessamentoContasQuantitativoResumo } from 'src/services/processadorRegrasApi/RegistrosProcessamentoContas/types'
import { useToast } from 'src/hooks/toast'
import SearchBar from 'components/molecules/SearchBar'
import { Checkbox } from '@material-ui/core'
import { ReactSVG } from 'react-svg'

const currentYear = new Date().getFullYear()
const currentMonth = new Date().getMonth() + 1

const PainelProcessamentoContasTemplate = () => {
    const [anos, setAnos] = useState<IExercicioQuery[]>([])
    const [anoExercicio, setAnoExercicio] = useState<number>()
    const [competencias, setCompetencias] = useState<ICompetenciaQuery[]>()
    const [competenciaSelecionada, setCompetenciaSelecionada] = useState<ICompetenciaQuery>()
    const [loadingDados, setLoadingDados] = useState(true)
    const [refreshDados, setRefreshDados] = useState(false)
    const [filtroCodigoContratado, setFiltroCodigoContratado] = useState<string>()
    const [filtroEmAbertoPor, setFiltroEmAbertoPor] = useState<number>(null)
    const [dadosQuantitativos, setDadosQuantitativos] = useState<IProcessamentoContasQuantitativoResumo>()

    const anosExercicio = useMemo(() => anos.map(({ ano }) => ({ label: ano.toString(), value: ano.toString() })), [anos])
    const anoExercicioSelecionado = useMemo(() => anoExercicio && { label: anoExercicio.toString(), value: anoExercicio.toString() }, [anoExercicio])

    const { addToast } = useToast()

    const parseCompetencias = () => {
        return competencias?.map((item) => {
            return {
                month: DateUtils.getMonthName(item.competencia),
                competencia: item.competencia,
                status: item.situacao.toString()
            }
        })
    }

    const onChangeAnoExercicio = useCallback(
        (ano) => {
            resetValues()
            setAnoExercicio(ano)
        },
        [setAnoExercicio]
    )

    const getCompetencias = useCallback(() => {
        if (!anoExercicio || competencias) return
        AnaliseContasService.getCompetencias(anoExercicio).then(({ data }) => {
            const sortCompetencias = data.sort((a, b) => moment(a.competencia).diff(b.competencia))
            setCompetencias(sortCompetencias)
            setCompetenciaSelecionada(sortCompetencias[sortCompetencias.length - 1])
        })
    }, [anoExercicio])

    const loadDadosQuantitativos = ({
        competencia,
        codigoContratado,
        emAbertoPor
    }: {
        competencia: string
        codigoContratado?: string
        emAbertoPor?: number
    }) => {
        setLoadingDados(true)
        RegistrosProcessamentoContasService.getDadosQuantitativos({
            competencia,
            tipoProcessamentoLote: 'NORMAL',
            codigoContratado,
            emAbertoPor: emAbertoPor || undefined
        })
            .then(({ data }) => {
                setDadosQuantitativos(data)
                setLoadingDados(false)
            })
            .catch((err) => {
                addToast({
                    title: 'Ocorreu um erro ao tentar carregar os dados do processamento de contas',
                    type: 'error',
                    duration: 5000
                })
            })
            .finally(() => setRefreshDados(false))
    }

    const resetValues = useCallback(() => {
        setCompetencias(undefined)
        setCompetenciaSelecionada(undefined)
        setRefreshDados(false)
    }, [])

    const isCompetenciaAtual = () => competenciaSelecionada?.competencia?.startsWith(`${currentYear}-${String(currentMonth).padStart(2, '0')}`)

    const isCompetenciaAberta = () => competenciaSelecionada?.situacao === 'ABERTO'

    const getFiltroCodigoContratado = () => (filtroCodigoContratado?.length >= 14 ? filtroCodigoContratado : undefined)

    useEffect(() => {
        AnaliseContasService.getExercicios().then(({ data }) => {
            const exercicios = data.sort((a, b) => b.ano - a.ano)
            setAnos(exercicios)
            setAnoExercicio(exercicios?.find((e) => e.ano === currentYear)?.ano || exercicios?.[0]?.ano)
        })
    }, [])

    useEffect(() => {
        getCompetencias()
    }, [anoExercicio])

    useEffect(() => {
        competenciaSelecionada?.competencia &&
            loadDadosQuantitativos({
                competencia: competenciaSelecionada?.competencia,
                codigoContratado: getFiltroCodigoContratado(),
                emAbertoPor: filtroEmAbertoPor
            })
    }, [competenciaSelecionada?.competencia])

    useEffect(() => {
        competenciaSelecionada?.competencia &&
            getFiltroCodigoContratado() &&
            loadDadosQuantitativos({
                competencia: competenciaSelecionada?.competencia,
                codigoContratado: getFiltroCodigoContratado(),
                emAbertoPor: filtroEmAbertoPor
            })
    }, [filtroCodigoContratado])

    useEffect(() => {
        competenciaSelecionada?.competencia &&
            loadDadosQuantitativos({
                competencia: competenciaSelecionada?.competencia,
                codigoContratado: getFiltroCodigoContratado(),
                emAbertoPor: filtroEmAbertoPor
            })
    }, [filtroEmAbertoPor])

    useEffect(() => {
        refreshDados &&
            competenciaSelecionada?.competencia &&
            loadDadosQuantitativos({
                competencia: competenciaSelecionada?.competencia,
                codigoContratado: getFiltroCodigoContratado(),
                emAbertoPor: filtroEmAbertoPor
            })
    }, [refreshDados])

    return (
        <S.Container>
            <S.Header>
                <PageTitle title="Painel de processamento de contas" subtitle="Acompanhe o processamento de contas e reprocesse lotes com falhas" />
                <S.RefreshButton
                    className="refresh-button"
                    style={loadingDados ? { opacity: '0.6', cursor: 'not-allowed' } : undefined}
                    onClick={() => setRefreshDados(true)}
                >
                    <ReactSVG src="/faturamento/assets/icons/refresh.svg" wrapper="div" className="refresh-icon" />
                </S.RefreshButton>
            </S.Header>
            <S.Content>
                <S.FiltersWrapper>
                    <CardSliderCompetencias
                        data={parseCompetencias()}
                        dateFilter={anosExercicio}
                        valueSelect={anoExercicioSelecionado}
                        onChangeAnoExercicio={onChangeAnoExercicio}
                        competenciaSelecionada={competenciaSelecionada}
                        setCompetenciaSelecionada={(competencia) => setCompetenciaSelecionada(competencia)}
                    />
                    <S.Filters>
                        <SearchBar
                            placeholder="Buscar CNPJ"
                            type="text"
                            value={filtroCodigoContratado}
                            handleOnSearch={() =>
                                loadDadosQuantitativos({
                                    competencia: competenciaSelecionada?.competencia,
                                    codigoContratado: getFiltroCodigoContratado(),
                                    emAbertoPor: filtroEmAbertoPor
                                })
                            }
                            handleOnClose={() => {
                                setFiltroCodigoContratado(null)
                                loadDadosQuantitativos({
                                    competencia: competenciaSelecionada?.competencia,
                                    emAbertoPor: filtroEmAbertoPor
                                })
                            }}
                            handleOnChange={(e) => {
                                if (e?.target?.value === '') {
                                    loadDadosQuantitativos({
                                        competencia: competenciaSelecionada?.competencia,
                                        emAbertoPor: filtroEmAbertoPor
                                    })
                                }
                                const search = e?.target?.value?.replace(/[^\d]/g, '')
                                setFiltroCodigoContratado(search)
                            }}
                        />
                        <div
                            style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
                            onClick={() =>
                                setFiltroEmAbertoPor((prev) => (prev ? null : RegistrosProcessamentoContasService.tempoBaseConsiderarLoteEmAberto))
                            }
                        >
                            <Checkbox checked={!!filtroEmAbertoPor} />
                            <span className="checkbox-label">Lotes em aberto</span>
                        </div>
                    </S.Filters>
                </S.FiltersWrapper>
                <DadosQuantitativos
                    competencia={competenciaSelecionada?.competencia}
                    isCompetenciaAtual={isCompetenciaAtual()}
                    isCompetenciaAberta={isCompetenciaAberta()}
                    dadosQuantitativos={dadosQuantitativos}
                    loadingDadosQuantitativos={loadingDados}
                    onRefreshDadosQuantitativosPainel={() => setRefreshDados(true)}
                />
                <ListaProcessamentoPrestadores
                    competencia={competenciaSelecionada?.competencia}
                    isCompetenciaAtual={isCompetenciaAtual()}
                    isCompetenciaAberta={isCompetenciaAberta()}
                    dadosQuantitativos={dadosQuantitativos}
                    situacaoCompetencia={competenciaSelecionada?.situacao}
                    filtroCodigoContratado={getFiltroCodigoContratado()}
                    filtroEmAbertoPor={filtroEmAbertoPor || undefined}
                    onRefreshDadosQuantitativosPainel={() => setRefreshDados(true)}
                    refreshDados={refreshDados}
                />
            </S.Content>
        </S.Container>
    )
}

export default PainelProcessamentoContasTemplate
