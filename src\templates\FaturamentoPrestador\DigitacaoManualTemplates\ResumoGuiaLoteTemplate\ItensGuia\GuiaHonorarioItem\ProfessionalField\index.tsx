/* eslint-disable prefer-const */
import { FormControl, InputLabel, MenuItem, Select } from '@mui/material'
import SearchPerson, { ISearchPerson } from 'components/molecules/SearchPerson'
import moment from 'moment'
import { Dispatch, SetStateAction, useEffect, useState } from 'react'
import { ReactSVG } from 'react-svg'
import { useToast } from 'src/hooks/toast'
import { LoteManualGuiaItemService } from 'src/services/faturamentoPrestadorApi/lote-manual-guia-item'
import { GrauParticipacaoEnum } from 'src/services/faturamentoPrestadorApi/lote-manual-guia/enuns'
import { IEquipeExecucao, IGuiaLoteManualDTO, IListProcedimentosDTO } from 'src/services/faturamentoPrestadorApi/lote-manual-guia/types'
import { ContractProxyService } from 'src/services/faturamentoPrestadorApi/proxy-contrato-api'
import { PrestadorProxyService } from 'src/services/faturamentoPrestadorApi/proxy-credenciamento-api'
import { Me } from 'src/services/gestaoUsuarios/me'
import { formatMonetary } from 'utils/masks/formatMonetary'
import { capitalize, getMessageErrorFromApiResponse } from 'utils/stringUtils'
import { ItemFieldValues } from '../..'
import { grauParticipacaoOptions } from './grauParticipacaoMock'
import * as S from './styles'
import { StatusCobrancaEnum } from 'src/services/faturamentoPrestadorApi/lote-manual/enuns'
import { IPrestadorDTO } from 'types/prestador'
import dynamic from 'next/dynamic'
const AnimatedLoadingLottie = dynamic(() => import('components/atoms/AnimatedLoadingLottie'), { ssr: false })

type ProfessionalFieldProps = {
    setFieldValues: Dispatch<SetStateAction<ItemFieldValues>>
    fieldValues: ItemFieldValues
    setValue: Dispatch<SetStateAction<number>>
    setIsEditable: Dispatch<SetStateAction<boolean>>
    setNoValue: Dispatch<SetStateAction<boolean>>

    setSaveTotalValue: Dispatch<SetStateAction<number[]>>
    setTotalValue: Dispatch<SetStateAction<number>>
    saveTotalValue: number[]
    isEditable: boolean

    id: number
    data: IEquipeExecucao
    procedureData: IListProcedimentosDTO
    guideData: IGuiaLoteManualDTO
    dadosPrestador: IPrestadorDTO
}

const ProfessionalField = ({
    setFieldValues,
    setSaveTotalValue,
    saveTotalValue,
    setTotalValue,
    setNoValue,
    fieldValues,
    id,
    data,
    dadosPrestador,
    procedureData,
    guideData,
    isEditable,
    setValue
}: ProfessionalFieldProps) => {
    const [equipe, setEquipe] = useState<IEquipeExecucao>()
    const { addToast } = useToast()
    const [valueState, setValueState] = useState('')
    const [refresh, setRefresh] = useState(false)
    const [loadingValue, setLoadingValue] = useState(false)
    const [currentValue, setCurrentValue] = useState<number>()

    // GET PRESTADOR ID

    const parserProfissional = (data: any) => {
        if (data?.length === 0) {
            addToast({ title: 'Profissional não encontrado.', type: 'info' })
        }
        return data?.map((usr) => ({
            title: capitalize(usr?.nome),
            subTitle: `CPF - ${usr?.cpf} - ${usr?.termoConselho} - ${usr?.numeroDoConselho}`,
            data: usr
        }))
    }

    const loadOptionsProfissional = async (props: string): Promise<ISearchPerson[]> => {
        return PrestadorProxyService.getPrestador({
            nomeCpfCrm: props
        }).then(({ data }) => {
            return parserProfissional(data?.content)
        })
    }

    const getPrecificarItens = (id: number, prestadorId: string, produtoId?: string) => {
        return PrestadorProxyService.getPrecificarItem({
            uuidPrestador: prestadorId,
            'yyyy-MM-dd': fieldValues?.dataExecucao ? moment(fieldValues?.dataExecucao).format('YYYY-MM-DD') : null,
            codigo: procedureData?.codigoProcedimento,
            descricao: procedureData?.descricaoProcedimento,
            idProdutoApi: produtoId,
            tabelaTiss: procedureData?.tabelaTiss
        })
            .then(({ data }) => {
                if (guideData?.dadosBeneficiario?.produtoId || produtoId) {
                    const produtoIdBeneficiario = guideData?.dadosBeneficiario?.produtoId || produtoId

                    getValorUnitarioHonorario(id, +produtoIdBeneficiario)
                    setValue(data?.valor || 0)
                }
            })
            .catch((err) => {
                setValue(0)
                setNoValue(true)
            })
    }

    const handleRemoveItem = (indexToRemove: number) => {
        setFieldValues((prevState) => ({
            ...prevState,
            equipeExecucao: prevState.equipeExecucao.filter((_, index) => index !== indexToRemove)
        }))

        // APAGA OS VALORES A SEREM SOMADOS.
        setSaveTotalValue((prevTotalValue) => prevTotalValue.filter((_, index) => index !== indexToRemove))
    }

    function getBeneficiaryInfoToGetValue(indice: number) {
        ContractProxyService?.getBeneficiary(guideData?.dadosBeneficiario?.beneficiarioId).then(({ data }) => {
            if (data?.idProduto) {
                // setProdutoId(data?.idProduto)
                getPrecificarItens(indice, dadosPrestador?.uuid, String(data?.idProduto))
            }
        })
        // .catch((err) => {
        //     addToast({
        //         type: 'error',
        //         title: 'Erro ao tentar precificar o item',
        //         duration: 4000,
        //         description: getMessageErrorFromApiResponse(err)
        //     })
        // })
    }

    // PEGA O VALOR UNITARIO COM SUA RESPECTIVA PORCENTAGEM

    function getValorUnitarioHonorario(indice: number, produtoIdBeneficiario: number) {
        setLoadingValue(true)

        LoteManualGuiaItemService?.getValueByGrau({
            codigo: procedureData?.codigoProcedimento,
            valorUnitario: fieldValues?.valorUnitario,
            viaAcesso: fieldValues?.viaAcesso,
            grauParticipacao: equipe?.grauParticipacao,
            produtoIdBeneficiario: produtoIdBeneficiario
        })
            .then(({ data }) => {
                setCurrentValue(data)
                setLoadingValue(false)

                setSaveTotalValue((prevTotalValue) => {
                    const newValueArr = [...prevTotalValue]
                    newValueArr[indice] = data
                    return newValueArr
                })
            })
            .catch(() => {
                setLoadingValue(false)
                // console.log('viaAcesso', procedureData?.viaAcesso)
            })
    }

    // DELETE PROFESSIONAL

    function handleClickToDeleteProfessional(uuidEquipe: string) {
        LoteManualGuiaItemService?.deleteProfessional({ uuidItemGuia: procedureData?.uuid, uuidEquipeExecucao: uuidEquipe })
            .then(() => {
                handleRemoveItem(id)
            })
            .catch((err) => {
                addToast({
                    type: 'error',
                    title: 'Erro ao tentar analisar o item',
                    duration: 5000,
                    description: getMessageErrorFromApiResponse(err)
                })
            })
    }

    // CHAMADA PARA PRECIFICAR ITEM

    useEffect(() => {
        if (fieldValues?.dataExecucao && dadosPrestador?.uuid) {
            if (guideData?.dadosBeneficiario?.produtoId) {
                getPrecificarItens(id, dadosPrestador?.uuid, String(guideData?.dadosBeneficiario?.produtoId))
            } else {
                // FUNÇÃO RESPONSÁVEL POR PUXAR OS DADOS DO BENEFICIÁRIO QUE ESTÃO FALTANDO
                // PARA CONSEGUIR FAZER O GET DE VALORES.
                getBeneficiaryInfoToGetValue(id)
            }
        }
    }, [fieldValues?.dataExecucao, equipe?.grauParticipacao, refresh])

    useEffect(() => {
        if (equipe?.uuid && equipe?.grauParticipacao) {
            const equipeAtt = fieldValues?.equipeExecucao.map((item, index) => {
                if (index === id) {
                    return equipe
                } else {
                    return item
                }
            })

            setFieldValues({
                ...fieldValues,
                equipeExecucao: equipeAtt
            })
        }
    }, [equipe])

    useEffect(() => {
        setEquipe(data)

        // SETA O VALOR DE PROFISSIONAL QUANDO HOUVER.
        setValueState(data?.nome)
    }, [data])

    useEffect(() => {
        const soma = saveTotalValue.reduce((acumulador, valorAtual) => acumulador + valorAtual, 0)
        setTotalValue(soma)
    }, [saveTotalValue])

    useEffect(() => {
        console.log('aqui', id >= procedureData?.equipeExecucao?.length)
        console.log('lenght', procedureData?.equipeExecucao?.length)
        console.log('id', id)
    }, [fieldValues])

    return (
        <S.WrapperContent>
            {(isEditable || procedureData?.statusCobranca === StatusCobrancaEnum.AGUARDANDO_ANALISE) && (
                <S.Grid collumns="1fr 1fr 120px .07fr">
                    <SearchPerson
                        setValueState={setValueState}
                        valueState={valueState}
                        onClick={(event) => {
                            setEquipe({
                                ...equipe,
                                ...event?.data
                            })
                        }}
                        label={'Profissional executante'}
                        loadOptions={loadOptionsProfissional}
                    />

                    <FormControl key={equipe?.grauParticipacao}>
                        <InputLabel>Selecione o grau de participação</InputLabel>
                        <Select
                            value={equipe?.grauParticipacao}
                            // error={isSelected}
                            onChange={(event) => {
                                // handleSelectChange(event)

                                setEquipe({
                                    ...equipe,
                                    grauParticipacao: GrauParticipacaoEnum[event?.target?.value]
                                })

                                // if (id === 0 && GrauParticipacaoEnum[event?.target?.value] !== GrauParticipacaoEnum.CIRURGIAO) {
                                //     // APAGA OS VALORES A SEREM SOMADOS.
                                //     setSaveTotalValue((prevTotalValue) => prevTotalValue.filter((_, index) => index !== id))
                                //     setFieldValues((prevState) => ({
                                //         ...prevState,
                                //         equipeExecucao: prevState.equipeExecucao.slice(0, 1)
                                //     }))
                                // }
                            }}
                        >
                            {/* ID NESSE CASO É O INDEX DA LISTAGEM DO COMPONENTE QUE CHAMA O PROFESSIONALFIELD */}
                            {/* {id === 0
                                ? grauParticipacaoOptions1?.map((item, index) => (
                                      <MenuItem key={index} value={item?.value}>
                                          {item?.label}
                                      </MenuItem>
                                  ))
                                : grauParticipacaoOptions2?.map((item, index) => (
                                      <MenuItem key={index} value={item?.value}>
                                          {item?.label}
                                      </MenuItem>
                                  ))} */}

                            {grauParticipacaoOptions?.map((item, index) => (
                                <MenuItem key={index} value={item?.value}>
                                    {item?.label}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>

                    <p>
                        {loadingValue ? (
                            <S.ContainerLottie>
                                <div className="spinner"></div>
                            </S.ContainerLottie>
                        ) : // Se currentValue é definido e não é NaN, formate-o. Caso contrário, use 0.
                        currentValue !== undefined && !isNaN(currentValue) ? (
                            formatMonetary(String(currentValue))
                        ) : (
                            formatMonetary('0')
                        )}
                    </p>

                    {
                        <S.TrashButton
                            title="Remover"
                            onClick={() => {
                                if (data?.savedProfessional) {
                                    handleClickToDeleteProfessional(data?.uuid)
                                } else {
                                    handleRemoveItem(id)
                                }
                            }}
                        >
                            <ReactSVG src="/faturamento/assets/icons/trash.svg" />
                        </S.TrashButton>
                    }
                </S.Grid>
            )}

            {!isEditable && procedureData?.statusCobranca === StatusCobrancaEnum.ANALISADA && (
                <S.ContentGrid>
                    <S.Item>
                        <span>{data?.nome || '-'}</span>
                    </S.Item>
                    <S.Item>
                        <span>{data?.grauParticipacao || '-'}</span>
                    </S.Item>
                    <S.Item>
                        <span>
                            {loadingValue ? (
                                <S.ContainerLottie>
                                    <AnimatedLoadingLottie style={{ width: '240px', margin: 'auto' }} />
                                </S.ContainerLottie>
                            ) : (
                                //   'Carregando...'
                                (currentValue && formatMonetary(String(currentValue))) || '-'
                            )}
                        </span>
                    </S.Item>
                </S.ContentGrid>
            )}
        </S.WrapperContent>
    )
}

export default ProfessionalField
