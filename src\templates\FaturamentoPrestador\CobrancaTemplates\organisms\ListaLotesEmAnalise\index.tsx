import { Box, CircularProgress } from '@mui/material'
import Checkbox from 'components/atoms/CheckBox'
import ButtonDropLots from 'components/molecules/Buttons/ButtonDownloadLots'
import DropLote from 'components/molecules/DropLote'
import NoContent from 'components/molecules/NoContent'
import Pagination from 'components/molecules/Pagination'
import moment from 'moment'
import React, { useEffect, useState } from 'react'
import { useAuth } from 'src/hooks/auth'
import { useToast } from 'src/hooks/toast'
import { CobrancaServices } from 'src/services/faturamentoPrestadorApi/cobrancaServices'
import { GeracaoRecursoGlosa } from 'src/services/faturamentoPrestadorApi/geracao-recurso-glosa'
import { ModuloEnum, TipoEnvioEnum } from 'src/services/faturamentoPrestadorApi/geracao-recurso-glosa/enuns'
import { IGetLotes } from 'src/services/faturamentoPrestadorApi/geracao-recurso-glosa/types'
import { IPrestadorAnaliseQuery } from 'types/cobrancaPrestador/prestadorAnalise'
import { IPagination } from 'types/common/pagination'
import { retiraSequencial } from 'utils/functions'
import { PaginationHelper } from 'utils/helpers/paginationHelper'
import { NumberUtils } from 'utils/numberUtils'
import { capitalize } from 'utils/stringUtils'
import { ICheckeboxFilter } from '../TabLoteMedico'
import * as S from './styles'

type ListaLotesEmAnaliseProps = {
    competenciaSelecionada: IPrestadorAnaliseQuery
    searchLote: string
    forceUpdate: boolean
    refreshList: boolean
    setLotes: any
    lotes: any
    filtro?: string
    checkboxFilter: ICheckeboxFilter
    setCheckboxFilter: React.Dispatch<React.SetStateAction<ICheckeboxFilter>>
    loadingLotes: boolean
    setLoadingLotes: React.Dispatch<React.SetStateAction<boolean>>
}

const ListaLotesEmAnalise = ({
    competenciaSelecionada,
    setLotes,
    lotes,
    refreshList,
    filtro,
    checkboxFilter,
    setCheckboxFilter,
    loadingLotes,
    setLoadingLotes
}: ListaLotesEmAnaliseProps) => {
    const { prestadorVinculado } = useAuth()
    const { addToast } = useToast()

    const dropsLots: React.ReactNode = []
    const labels: string[] = ['Lote', 'Data de envio', 'Usuário', 'Valor apresentado', 'Envio']

    const [valorTotalApresentado, setValorTotalApresentado] = useState<number>(0)
    const [pagination, setPagination] = useState<IPagination>()
    const [numberPage, setNumberPage] = useState(0)

    function createDropLot(item: IGetLotes) {
        return [
            {
                component: (
                    <div>
                        <p>{item?.identificadorLote}</p>
                    </div>
                )
            },
            {
                component: (
                    <div>
                        <p>{moment(item.dataEnvio).format('DD/MM/YYYY [-] HH:mm')}</p>
                    </div>
                )
            },
            {
                component: (
                    <div>
                        <p>{capitalize(item.nomeUsuario)}</p>
                    </div>
                )
            },
            {
                component: (
                    <div>
                        <p>{NumberUtils.maskMoney(item.valorApresentado)}</p>
                    </div>
                )
            },
            {
                component: (
                    <div>
                        <p>{item.tipoEnvio === 'ELETRONICO' ? 'Eletrônico' : capitalize(item.tipoEnvio)}</p>
                    </div>
                )
            }
        ]
    }

    const handleDownloadProtocoloRecebimentoXml = (idLote) => {
        CobrancaServices.getProtocoloRecebimentoXml(idLote).then((response) => {
            const fileName = response.headers['content-disposition'].split('filename=')[1] ?? 'download.xml'
            const url = window.URL.createObjectURL(new Blob([response.data]))
            const link = document.createElement('a')
            link.href = url
            link.setAttribute('download', fileName) //or any other extension
            document.body.appendChild(link)
            link.click()
        })
    }

    const handleDownloadProtocoloRecebimentoPdf = (idLote) => {
        CobrancaServices.getProtocoloRecebimentoPdf(idLote).then((response) => {
            const fileName = response.headers['content-disposition'].split('filename=')[1] ?? 'download.pdf'
            const url = window.URL.createObjectURL(response.data)
            const link = document.createElement('a')
            link.href = url
            link.setAttribute('download', fileName) //or any other extension
            document.body.appendChild(link)
            link.click()
        })
    }

    // const carregarLotes = useCallback(
    //     (filter?: string, page?: number) => {
    //         const getProps: IGetCobrancaProps =
    //             filter === ''
    //                 ? {
    //                       size: 10,
    //                       page: page || 0
    //                   }
    //                 : {
    //                       size: 10,
    //                       page: page || 0,
    //                       filtroNumeroLote: filter
    //                   }

    //         CobrancaServices.getLotes(competenciaSelecionada?.competencia, prestadorVinculado?.uuid, 'EM_ANALISE', getProps).then(({ data }) => {
    //             // if (data.totalElements > 0) {
    //             setLotes(data)
    //             // }
    //             // initLotesChecks(data.content)

    //             const objectPagination = PaginationHelper.parserPagination<ILoteCobrancaDTO>(data, setNumberPage)
    //             setPagination(objectPagination)
    //         })
    //     },
    //     [competenciaSelecionada, prestadorVinculado]
    // )

    const carregarLotes = () => {
        setLoadingLotes(true)
        if (competenciaSelecionada?.competencia && prestadorVinculado?.uuid) {
            GeracaoRecursoGlosa.getlotesEmAnalise({
                identificadorLote: retiraSequencial(filtro),
                competencia: competenciaSelecionada?.competencia,
                prestadorId: prestadorVinculado?.uuid,
                modulo: ModuloEnum.MEDICO,
                lotes: checkboxFilter?.lotesCobranca,
                lotesRecursoGlosa: checkboxFilter?.lotesRecursoGlosa,
                page: numberPage,
                size: 10
            })
                .then(({ data }) => {
                    setLotes(data)

                    const objectPagination = PaginationHelper.parserPagination<IGetLotes>(data, setNumberPage)
                    setPagination(objectPagination)
                })
                .catch((err) => {
                    addToast({
                        title: err?.message ? err?.message : 'Ocorreu um erro ao buscar as informações',
                        type: 'error',
                        duration: 3000
                    })
                })
                .finally(() => setLoadingLotes(false))
        }
    }

    useEffect(() => {
        carregarLotes()
    }, [numberPage, refreshList, checkboxFilter])

    useEffect(() => {
        CobrancaServices.getValorApresentado(competenciaSelecionada?.competencia, prestadorVinculado?.uuid).then(({ data }) => {
            setValorTotalApresentado(data.valorApresentado)
        })
    }, [])

    return (
        <>
            <S.CheckboxWrapper>
                <Checkbox
                    checked={checkboxFilter.lotesCobranca}
                    onCheck={() => setCheckboxFilter({ ...checkboxFilter, lotesCobranca: !checkboxFilter.lotesCobranca })}
                    label="Lotes de cobrança"
                />

                <Checkbox
                    checked={checkboxFilter.lotesRecursoGlosa}
                    onCheck={() => setCheckboxFilter({ ...checkboxFilter, lotesRecursoGlosa: !checkboxFilter.lotesRecursoGlosa })}
                    label="Lotes de recurso de glosa"
                />
            </S.CheckboxWrapper>
            {loadingLotes ? (
                <Box sx={{ display: 'flex', minHeight: 218, alignItems: 'center', justifyContent: 'center' }}>
                    <CircularProgress sx={{ color: '#2B45D4' }} />
                </Box>
            ) : (
                <S.Wrapper>
                    {!lotes || lotes?.content.length === 0 ? (
                        <NoContent title="No momento não existe nenhum lote em análise" path="/parametros/regras-de-excludencia/nova-regra" />
                    ) : (
                        <>
                            <div className="contentInput">
                                <div className="contentNumber">
                                    <span className="pageNumber">
                                        {pagination?.paginaAtual * pagination?.linhasPorPagina + 1} -{' '}
                                        {lotes?.content?.length + pagination?.paginaAtual * pagination?.linhasPorPagina}
                                        {' de '}
                                        {pagination?.totalRegistros}
                                    </span>
                                </div>
                            </div>
                            <S.HeaderSubtitle>
                                Total apresentado dos lotes enviados: <b>{NumberUtils.maskMoney(valorTotalApresentado)}</b>
                            </S.HeaderSubtitle>
                            <S.HeaderLabel>
                                {labels.map((item, index) => (
                                    <div key={index}>
                                        <p>{item}</p>
                                    </div>
                                ))}
                            </S.HeaderLabel>
                            {lotes?.content?.map((lote: IGetLotes, index) => {
                                return (
                                    <DropLote items={createDropLot(lote)} key={index}>
                                        <S.ContentDropLot>
                                            <p>Protocolo de Recebimento</p>
                                            <S.RowButtons>
                                                {lote.tipoEnvio !== TipoEnvioEnum?.MANUAL && (
                                                    <ButtonDropLots icon={'/faturamento/assets/icons/download.svg'}>
                                                        <span onClick={() => handleDownloadProtocoloRecebimentoXml(lote?.loteId)}>Baixar XML</span>
                                                    </ButtonDropLots>
                                                )}
                                                <ButtonDropLots icon={'/faturamento/assets/icons/download.svg'}>
                                                    <span onClick={() => handleDownloadProtocoloRecebimentoPdf(lote?.loteId)}>Baixar PDF</span>
                                                </ButtonDropLots>
                                            </S.RowButtons>
                                        </S.ContentDropLot>
                                    </DropLote>
                                )
                            })}
                            <div style={{ marginTop: '40px' }}>
                                <Pagination
                                    totalPage={pagination?.totalPaginas}
                                    totalRegister={pagination?.totalRegistros}
                                    actualPage={pagination?.paginaAtual}
                                    setNumberPage={pagination?.setNumberPage}
                                />
                            </div>
                        </>
                    )}
                </S.Wrapper>
            )}
        </>
    )
}

export default ListaLotesEmAnalise
