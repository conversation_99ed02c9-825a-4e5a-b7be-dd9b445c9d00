import * as S from './styles'
import moment from 'moment'
import React, { useCallback, useEffect, useState } from 'react'
import { IPagination } from 'types/common/pagination'
import { IPrestadorAnaliseQuery } from 'types/cobrancaPrestador/prestadorAnalise'
import { useAuth } from 'src/hooks/auth'
import { useToast } from 'src/hooks/toast'
import { CobrancaServices, IGetCobrancaProps } from 'src/services/faturamentoPrestadorApi/cobrancaServices'
import { capitalize } from 'utils/stringUtils'
import { PaginationHelper } from 'utils/helpers/paginationHelper'
import Button from 'components/atoms/Button'
import Pagination from 'components/molecules/Pagination'
import NoContent from 'components/molecules/NoContent'
import { Box, CircularProgress } from '@mui/material'
import { LoteManualService } from 'src/services/faturamentoPrestadorApi/lote-manual'
import { ILoteManualDTO, IPageLoteManualDTO } from 'src/services/faturamentoPrestadorApi/lote-manual/types'
import { useRouter } from 'next/router'
import { StatusCobrancaEnum } from 'src/services/faturamentoPrestadorApi/lote-manual/enuns'

export type lotChecked = {
    index: number
    checked: boolean
    numberLote: string
}
type ListaEmConstrucaoProps = {
    competenciaSelecionada: IPrestadorAnaliseQuery
    carregarSituacoes: any
    refreshList: boolean
    filtroNumeroLote: string
    lotes: IPageLoteManualDTO
    setLotes?: any
    loadingLotes: boolean
    setLoadingLotes: React.Dispatch<React.SetStateAction<boolean>>
}

export type itemsProps = {
    component: React.ReactNode
}

const ListaEmConstrucao = ({
    competenciaSelecionada,
    carregarSituacoes,
    refreshList,
    filtroNumeroLote,
    lotes,
    setLotes,
    loadingLotes,
    setLoadingLotes
}: ListaEmConstrucaoProps) => {
    const { addToast } = useToast()
    const { prestadorVinculado } = useAuth()

    const [checkedAll, setCheckedAll] = useState(false)
    const [lotsCheckeds, setLotsCheckeds] = useState<lotChecked[]>([])
    // const [pageLotes, setPageLotes] = useState<IPageLote>()
    const [pagination, setPagination] = useState<IPagination>()
    const [numberPage, setNumberPage] = useState(0)

    const route = useRouter()

    // useEffect(() => {
    //     initLotsCheckeds(lotes?.content)
    //     // eslint-disable-next-line react-hooks/exhaustive-deps
    // }, [lotes])

    // const initLotsCheckeds = (data: ILoteCobrancaDTO[]) => {
    //     const lotsCheckeds: lotChecked[] = []
    //     data?.forEach((item, index) => {
    //         lotsCheckeds.push({
    //             index,
    //             checked: false,
    //             numberLote: item?.loteCobrancaId?.toString()
    //         })
    //     })
    //     setLotsCheckeds(lotsCheckeds)
    // }

    // function checkAll() {
    //     setLotsCheckeds(lotsCheckeds.map((item) => ({ ...item, checked: !checkedAll })))
    // }

    // const carregarLotes = useCallback(
    //     (page?: number, filtroNumeroLote?: string) => {
    //         const getProps: IGetCobrancaProps = {
    //             size: 10,
    //             page: page || 0
    //         }
    //         setLoadingLotes(true)
    //         CobrancaServices.getLotes(
    //             competenciaSelecionada?.competencia,
    //             prestadorVinculado?.uuid,
    //             'PROCESSANDO',
    //             filtroNumeroLote?.length > 0 ? { ...getProps, filtroNumeroLote } : getProps
    //         )
    //             .then(({ data }) => {
    //                 setLotes(data)

    //                 const objectPagination = PaginationHelper.parserPagination<ILoteCobrancaDTO>(data, setNumberPage)
    //                 setPagination(objectPagination)
    //             })
    //             .finally(() => setLoadingLotes(false))
    //     },
    //     [competenciaSelecionada]
    // )

    // useEffect(() => {
    //     carregarLotes()
    //     console.log(filtroNumeroLote)
    // }, [filtroNumeroLote])

    // useEffect(() => {
    //     // carregarLotes(numberPage, filtroNumeroLote)
    // }, [numberPage, refreshList])

    // useEffect(() => {
    //     if (!lotes) return

    //     setCheckedAll(lotsCheckeds.filter((i) => i.checked).length === lotes?.content.length)
    // }, [lotsCheckeds, lotes])

    // useEffect(() => {
    //     if (!refresh) return

    //     carregarLotes()
    //     carregarSituacoes()
    //     setRefresh(false)
    // }, [refresh])

    // const handleClickCancelarLotes = () => {
    //     const cancelarLotes = async () => {
    //         let errosOcorridos = false
    //         await Promise.all(
    //             lotsCheckeds
    //                 ?.filter((i) => i.checked)
    //                 .map(async (lote) => {
    //                     try {
    //                         await CobrancaServices.patchCancelamento(lote.numberLote)
    //                     } catch {
    //                         errosOcorridos = true
    //                     }
    //                 })
    //         )

    //         if (!errosOcorridos) {
    //             addToast({ title: 'Lotes cancelados com sucesso', type: 'success' })
    //             carregarLotes(numberPage)
    //         } else {
    //             addToast({ title: 'Ocorreu erro ao cancelar o lote. Tente novamente.', type: 'error' })
    //         }
    //     }

    //     cancelarLotes()

    //     setTimeout(() => {
    //         if (carregarSituacoes) carregarSituacoes()
    //     }, 1000)
    // }
    const statusCobranca = ['AGUARDANDO_ANALISE', 'ANALISADA']

    const getLotesEmConstrucao = () => {
        LoteManualService.getLoteEmConstrucao({
            prestadorId: prestadorVinculado?.uuid,
            // competencia: competenciaSelecionada?.competencia,
            statusCobranca: statusCobranca,
            page: numberPage,
            size: 10
        })
            .then(({ data }) => {
                setLotes(data)
                const objectPagination = PaginationHelper.parserPagination(data, setNumberPage)
                setPagination(objectPagination)
            })
            .finally(() => setLoadingLotes(false))
    }

    useEffect(() => {
        if (prestadorVinculado?.uuid) {
            getLotesEmConstrucao()
        }
    }, [numberPage, prestadorVinculado])

    return (
        <>
            {loadingLotes ? (
                <Box sx={{ display: 'flex', minHeight: 218, alignItems: 'center', justifyContent: 'center' }}>
                    <CircularProgress sx={{ color: '#2B45D4' }} />
                </Box>
            ) : (
                <S.Wrapper>
                    {!lotes || lotes?.content?.length === 0 ? (
                        <NoContent title="No momento não existe nenhum lote em construção" path="/parametros/regras-de-excludencia/nova-regra" />
                    ) : (
                        <>
                            <div className="contentInput">
                                <div className="contentNumber">
                                    {/* <span className="pageNumber">1-10 de 248</span> */}
                                    {/* <span className="pageNumber">
                                        {pagination?.paginaAtual * pagination?.linhasPorPagina + 1} -{' '}
                                        {lotes?.content?.length + pagination?.paginaAtual * pagination?.linhasPorPagina}
                                        {' de '}
                                        {pagination?.totalRegistros}
                                    </span> */}
                                    {/* <ReactSVG src="/faturamento/assets/icons/dot.svg" /> */}
                                </div>
                            </div>

                            {/* {lotsCheckeds?.some((i) => i.checked) ? (
                                <S.ContentCancelLotes onClick={handleClickCancelarLotes}>
                                    <S.ButtonWrapper>
                                        <Button typeButton="ghost" themeButton="octopus">
                                            Cancelar lotes selecionados
                                        </Button>
                                    </S.ButtonWrapper>
                                </S.ContentCancelLotes>
                            ) : (
                                <S.ContentCancelLotes style={{ cursor: 'default' }}>
                                    <S.ButtonWrapper>
                                        <Button typeButton="ghost" themeButton="octopus" disabled>
                                            Cancelar lotes selecionados
                                        </Button>
                                    </S.ButtonWrapper>
                                </S.ContentCancelLotes>
                            )} */}

                            <S.Table>
                                <tr>
                                    <th className="checkLotLabel">Numero do lote prestador</th>
                                    <th>Tipo do lote</th>
                                    <th>Usuário</th>
                                    <th>Status</th>
                                </tr>

                                {lotes?.content?.map((lote, index: number) => (
                                    <S.TabContent
                                        key={index}
                                        checked={lotsCheckeds[index]?.checked}
                                        onClick={() => {
                                            route.push(
                                                `/prestador/medico/cobranca/novo-lote?loteId=${lote?.uuid}&competencia=${competenciaSelecionada?.competencia}`
                                            )
                                        }}
                                    >
                                        <td className="checkLot">
                                            {/* <input
                                                value={lote?.numeroLote}
                                                type="checkbox"
                                                id={lote?.numeroLote?.toString()}
                                                onChange={() => {
                                                    const newLotsCheckeds = [...lotsCheckeds]

                                                    if (!newLotsCheckeds) return

                                                    if (!newLotsCheckeds[index]) return

                                                    newLotsCheckeds[index].checked = !newLotsCheckeds[index]?.checked
                                                    setLotsCheckeds(newLotsCheckeds)
                                                }}
                                                checked={lotsCheckeds[index]?.checked}
                                            />
                                            {lote.numeroLote} */}
                                            {lote.numeroLotePrestador || '-'}
                                        </td>
                                        <td>{lote.tipoLote || '-'}</td>
                                        <td>{lote.nomeUsuarioResponsavel || '-'}</td>
                                        <td>{lote.statusCobranca || '-'}</td>
                                        {/* <td>
                                <ReactSVG src="/faturamento/assets/icons/ic-trash.svg" />
                            </td> */}
                                    </S.TabContent>
                                ))}
                            </S.Table>

                            <div style={{ marginTop: '40px' }}>
                                <Pagination
                                    totalPage={pagination?.totalPaginas}
                                    totalRegister={pagination?.totalRegistros}
                                    actualPage={pagination?.paginaAtual}
                                    setNumberPage={(page) => setNumberPage(page)}
                                />
                            </div>
                        </>
                    )}
                </S.Wrapper>
            )}
        </>
    )
}

export default ListaEmConstrucao
