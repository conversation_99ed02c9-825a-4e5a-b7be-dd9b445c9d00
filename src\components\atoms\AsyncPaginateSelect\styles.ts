import styled, { css } from 'styled-components'

type WrapperProps = {
    isFocused?: boolean
    focus?: boolean
    disabled?: boolean
}

export const Wrapper = styled.div<WrapperProps>`
    ${({ theme, isFocused, focus, disabled }) => css`
        position: relative;
        background-color: #ffffff;
        width: 100%;

        opacity: ${disabled ? 0.5 : 1};

        label {
            font-size: 1.6rem;

            position: absolute;
            top: 17px;
            left: 13px;

            padding: 0 5px;
            transition: 0.2s;

            color: ${focus ? theme.colors.primary['500'] : theme.colors.black[56]};

            span {
                color: red;
            }

            ${isFocused &&
            css`
                top: -10px;
                left: 12px;
                font-size: 1.4rem;

                z-index: 1;
                background-color: #ffffff;
                padding: 0 0.4rem;

                /* color: ${theme.colors.primary['500']}; */
                background: linear-gradient(transparent 41%, #fff 41%, #fff 59%);
            `}
        }
    `}
`
