import { useEffect, useState } from 'react'
import * as S from './styles'
import SpinnerLoading from 'components/molecules/SpinnerLoading'
import { RegistrosProcessamentoContasService } from 'src/services/processadorRegrasApi/RegistrosProcessamentoContas'
import { useToast } from 'src/hooks/toast'
import { IGetRegistroProcessamentoLoteDto } from 'src/services/processadorRegrasApi/RegistrosProcessamentoContas/types'
import { IPagination } from 'types/pagination'
import { PaginationHelper } from 'utils/helpers/paginationHelper'
import Pagination from 'components/molecules/Pagination'
import { Table } from 'components/molecules/Table'
import NavTabs from 'components/molecules/NavTabs'
import { SituacaoProcessamentoLoteEnum } from 'src/services/processadorRegrasApi/RegistrosProcessamentoContas/enums'
import { SituacaoLote, TipoEnvioLabel } from 'types/common/enums'
import { useAuth } from 'src/hooks/auth'
import { IPageResult } from 'types/common/pagination'
import { TipoLoteShow } from 'src/templates/FaturamentoPrestador/LotesTemplate/enuns'
import moment from 'moment'
import PageTitle from 'components/molecules/PageTitle'
import { PrestadorService } from 'src/services/faturamentoOperadoraApi/prestadorServices'
import { IPrestadorQuery } from 'types/faturamentoOperadora/provider'
import { maskCNPJ } from 'utils/masks/formatCNPJ'
import maskCpf from 'utils/masks/formatCPF'
import SearchBar from 'components/molecules/SearchBar'
import { ReactSVG } from 'react-svg'
import { DateUtils } from 'utils/dateUtils'

const PAGE_SIZE = 8

type ProcessamentoContasPrestadorTemplateProps = {
    prestadorId: string
    competencia: string
    situacao: SituacaoProcessamentoLoteEnum | SituacaoLote
}
const ProcessamentoContasPrestadorTemplate = ({ prestadorId, competencia, situacao }: ProcessamentoContasPrestadorTemplateProps) => {
    const [loadingDados, setLoadingDados] = useState(true)
    const [dadosPrestador, setDadosPrestador] = useState<IPrestadorQuery>()
    const [lotesPage, setLotesPage] = useState<IPageResult<IGetRegistroProcessamentoLoteDto>>()
    const [currentPageNumber, setCurrentPageNumber] = useState(0)
    const [filtroIdentificadorLote, setFiltroIdentificadorLote] = useState<number>(null)
    const [pagination, setPagination] = useState<IPagination>()

    const { isOwner } = useAuth()

    const { addToast } = useToast()

    const loadDados = ({
        competencia,
        prestadorId,
        identificadorLote,
        situacao,
        pageNumber = 0
    }: {
        competencia: string
        prestadorId: string
        identificadorLote?: number
        situacao?: SituacaoLote | SituacaoProcessamentoLoteEnum
        pageNumber?: number
    }) => {
        setLoadingDados(true)
        Promise.all([
            RegistrosProcessamentoContasService.getRegistrosProcessamento({
                competencia,
                prestadorId,
                identificadorLote,
                situacaoLote: [SituacaoLote.RECUSADO, SituacaoLote.CANCELADO].includes(situacao as SituacaoLote)
                    ? (situacao as SituacaoLote)
                    : undefined,
                situacaoProcessamento: [
                    SituacaoProcessamentoLoteEnum.AGENDADO,
                    SituacaoProcessamentoLoteEnum.EM_PROCESSAMENTO,
                    SituacaoProcessamentoLoteEnum.FALHA
                ].includes(situacao as SituacaoProcessamentoLoteEnum)
                    ? (situacao as SituacaoProcessamentoLoteEnum)
                    : undefined,
                page: pageNumber,
                size: PAGE_SIZE
            })
                .then(async ({ data }) => {
                    setLotesPage(data)
                    setPagination(PaginationHelper.parserPagination<IGetRegistroProcessamentoLoteDto>(data, (page) => setCurrentPageNumber(page)))
                })
                .catch((err) => {
                    addToast({
                        title: 'Ocorreu um erro ao tentar carregar os registros de processamento do prestador',
                        type: 'error',
                        duration: 5000
                    })
                }),
            !dadosPrestador &&
                PrestadorService.getByID(prestadorId)
                    .then(({ data }) => setDadosPrestador(data))
                    .catch(() => undefined)
        ]).then(() => setLoadingDados(false))
    }

    const showTagDuracaoLoteAberto = (dataEnvio: string) => {
        return (
            [SituacaoProcessamentoLoteEnum.AGENDADO, SituacaoProcessamentoLoteEnum.EM_PROCESSAMENTO, SituacaoProcessamentoLoteEnum.FALHA].includes(
                situacao as SituacaoProcessamentoLoteEnum
            ) && moment().diff(moment(dataEnvio), 'minutes', false) >= RegistrosProcessamentoContasService.tempoBaseConsiderarLoteEmAberto
        )
    }

    useEffect(() => {
        competencia && prestadorId && loadDados({ competencia, prestadorId, situacao })
    }, [competencia, prestadorId])

    useEffect(() => {
        competencia && prestadorId && loadDados({ competencia, prestadorId, situacao, identificadorLote: filtroIdentificadorLote })
    }, [filtroIdentificadorLote])

    return (
        <S.Container>
            <S.Header>
                {dadosPrestador?.tipoPessoa === 'PESSOA_JURIDICA' ? (
                    <>
                        <PageTitle title={dadosPrestador?.nomeFantasia} />
                        <p className="subtitle">
                            CNPJ: <b>{maskCNPJ(dadosPrestador?.cnpj || '') || '---'}</b>
                        </p>
                    </>
                ) : (
                    <>
                        <PageTitle title={dadosPrestador?.nomeCompleto} />
                        <p className="subtitle">
                            CPF: <b>{maskCpf(dadosPrestador?.cpf || '') || '---'}</b>
                        </p>
                    </>
                )}
            </S.Header>

            <S.Content>
                <SearchBar
                    placeholder="Buscar número do lote"
                    type="number"
                    value={String(filtroIdentificadorLote || '')}
                    handleOnSearch={() => loadDados({ competencia, prestadorId, situacao, identificadorLote: filtroIdentificadorLote })}
                    handleOnClose={() => {
                        setFiltroIdentificadorLote(null)
                        loadDados({ competencia, prestadorId, situacao })
                    }}
                    handleOnChange={(e) => {
                        const search = Number(e?.target?.value) || null
                        if (search === undefined) {
                            loadDados({ competencia, prestadorId, situacao })
                        }
                        setFiltroIdentificadorLote(search)
                    }}
                />
                <NavTabs
                    variant="type_3"
                    tabs={[
                        {
                            label:
                                {
                                    [SituacaoProcessamentoLoteEnum.FALHA]: 'Lotes com falhas',
                                    [SituacaoProcessamentoLoteEnum.AGENDADO]: 'Lotes agendados',
                                    [SituacaoProcessamentoLoteEnum.EM_PROCESSAMENTO]: 'Lotes processando',
                                    [SituacaoLote.RECUSADO]: 'Lotes recusados',
                                    [SituacaoLote.CANCELADO]: 'Lotes cancelados'
                                }[situacao] || 'Lotes',
                            counter: lotesPage?.totalElements
                        }
                    ]}
                />

                {loadingDados ? (
                    <SpinnerLoading />
                ) : (
                    <Table.Root>
                        <Table.Body>
                            {lotesPage?.content?.map((element, index) => (
                                <Table.Card
                                    style={{
                                        display: 'grid',
                                        gridTemplateColumns: '1fr 1fr 1fr 2fr',
                                        gap: '4rem',
                                        height: 'fit-content',
                                        minHeight: '7.2rem',
                                        padding: '1.6rem 0',
                                        backgroundColor: situacao === SituacaoProcessamentoLoteEnum.FALHA ? 'rgba(178, 18, 6, 0.08)' : '#F6F6F9'
                                    }}
                                    key={index}
                                >
                                    <Table.Item.TitleSubTitle title={`Lote ${element?.identificadorLote}`} />
                                    <Table.Item.TitleSubTitle
                                        titleStyles={{ fontSize: '1.2rem', lineHeight: '1.6rem', fontWeight: '400', color: '#3E4E65' }}
                                        title="Tipo do lote"
                                        subTitle={TipoLoteShow[element?.tipoLote] || element?.tipoLote}
                                    />
                                    <Table.Item.TitleSubTitle
                                        titleStyles={{ fontSize: '1.2rem', lineHeight: '1.6rem', fontWeight: '400', color: '#3E4E65' }}
                                        title="Tipo de envio"
                                        subTitle={TipoEnvioLabel[element?.tipoEnvio] || element?.tipoEnvio}
                                    />
                                    <S.DataEnvio style={{ marginRight: 'auto' }}>
                                        <Table.Item.TitleSubTitle
                                            titleStyles={{ fontSize: '1.2rem', lineHeight: '1.6rem', fontWeight: '400', color: '#3E4E65' }}
                                            title="Data e hora de envio"
                                            subTitle={moment(element?.dataEnvio).format('DD/MM/YYYY [-] HH:mm')}
                                        />
                                        {showTagDuracaoLoteAberto(element?.dataEnvio) && (
                                            <S.TagDuracaoLoteAberto>
                                                <ReactSVG src="/faturamento/assets/icons/iconWarning.svg" wrapper="div" className="tag-icon" />
                                                <p>Em aberto há {DateUtils.formatDuracao(new Date(element?.dataEnvio))}</p>
                                            </S.TagDuracaoLoteAberto>
                                        )}
                                    </S.DataEnvio>
                                </Table.Card>
                            ))}
                            {!lotesPage?.content?.length && <S.EmptyListMessage>Não há dados nesta seção</S.EmptyListMessage>}
                        </Table.Body>
                    </Table.Root>
                )}
                <S.WrapperPagination>
                    <Pagination
                        totalPage={pagination?.totalPaginas}
                        totalRegister={pagination?.totalRegistros}
                        actualPage={pagination?.paginaAtual}
                        setNumberPage={(pageNumber) => {
                            setCurrentPageNumber(pageNumber)
                            loadDados({
                                competencia,
                                prestadorId,
                                situacao,
                                pageNumber
                            })
                        }}
                    />
                    {!loadingDados && (
                        <p>
                            {pagination?.paginaAtual * pagination?.linhasPorPagina + 1} -{' '}
                            {lotesPage?.content?.length + pagination?.paginaAtual * pagination?.linhasPorPagina}
                            {' de '}
                            {pagination?.totalRegistros}
                        </p>
                    )}
                </S.WrapperPagination>
            </S.Content>
        </S.Container>
    )
}

export default ProcessamentoContasPrestadorTemplate
