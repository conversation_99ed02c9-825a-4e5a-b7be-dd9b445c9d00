import { useEffect, useState } from 'react'
import * as S from './styles'
import SpinnerLoading from 'components/molecules/SpinnerLoading'
import { useToast } from 'src/hooks/toast'
import { RegistrosProcessamentoContasService } from 'src/services/processadorRegrasApi/RegistrosProcessamentoContas'
import { IProcessamentoContasQuantitativoResumo } from 'src/services/processadorRegrasApi/RegistrosProcessamentoContas/types'
import { ReactSVG } from 'react-svg'
import CardToolTipComponent from 'components/molecules/TooltipInfo'
import Button from 'components/atoms/Button'
import ModalMaisIndicadores from './ModalMaisIndicadores'
import { useAuth } from 'src/hooks/auth'

type DadosQuantitativosProps = {
    competencia: string
    isCompetenciaAtual: boolean
    isCompetenciaAberta: boolean
    dadosQuantitativos: IProcessamentoContasQuantitativoResumo
    loadingDadosQuantitativos: boolean
    onRefreshDadosQuantitativosPainel: () => void
}
const DadosQuantitativos = ({
    competencia,
    isCompetenciaAtual,
    isCompetenciaAberta,
    dadosQuantitativos,
    loadingDadosQuantitativos,
    onRefreshDadosQuantitativosPainel
}: DadosQuantitativosProps) => {
    const [sincronizandoLotesEmAnalise, setSincronizandoLotesEmAnalise] = useState(false)
    const [redistribuindoLotesAgendados, setRedistribuindoLotesAgendados] = useState(false)
    const [showModalMaisIndicadores, setShowModalMaisIndicadores] = useState(false)
    const [permitirRedistribuirLotesAgendados, setPermitirRedistribuirLotesAgendados] = useState(false)

    const { addToast } = useToast()

    const { isOwner } = useAuth()

    const sincronizarLotesEmAnalise = (competencia: string) => {
        setSincronizandoLotesEmAnalise(true)
        RegistrosProcessamentoContasService.sincronizarAnaliseContas({
            competencia
        })
            .then(() => {
                onRefreshDadosQuantitativosPainel()
            })
            .catch((err) => {
                addToast({
                    title: 'Ocorreu um erro ao tentar sincronizar os lotes em análise',
                    type: 'error',
                    duration: 5000
                })
            })
            .finally(() => setSincronizandoLotesEmAnalise(false))
    }

    const redistribuirLotesAgendados = (competencia: string) => {
        setRedistribuindoLotesAgendados(true)
        RegistrosProcessamentoContasService.redistribuirLotesAgendados({
            competencia
        })
            .then(() => {
                onRefreshDadosQuantitativosPainel()
            })
            .catch((err) => {
                addToast({
                    title: 'Ocorreu um erro ao tentar redistribuir lotes agendados',
                    type: 'error',
                    duration: 5000
                })
            })
            .finally(() => setRedistribuindoLotesAgendados(false))
    }

    useEffect(() => {
        competencia &&
            RegistrosProcessamentoContasService.getPermiteRedistribuirLotesAgendados({ competencia }).then(({ data }) =>
                setPermitirRedistribuirLotesAgendados(data)
            )
    }, [competencia])

    return (
        <S.Container>
            {loadingDadosQuantitativos ? (
                <SpinnerLoading containerStyles={{ height: '20.8rem' }} />
            ) : (
                <S.FlexColumn>
                    <S.FlexRow>
                        <S.CardWithIcon cardTheme="green">
                            <S.CardIcon cardTheme="green">
                                <ReactSVG src="/faturamento/assets/icons/approve.svg" wrapper="div" />
                            </S.CardIcon>
                            <div className="card-data">
                                <p className="label">Lotes processados</p>
                                <p className="value">{dadosQuantitativos?.quantidadeLotesProcessados}</p>
                            </div>
                        </S.CardWithIcon>
                        <S.FlexColumn>
                            <S.HorizontalCard cardTheme="grey">
                                <div className="card-data">
                                    <p className="label">Lotes em análise de contas</p>
                                    <p className="value">{dadosQuantitativos?.quantidadeLotesEmAnalise}</p>
                                </div>
                            </S.HorizontalCard>
                            <S.HorizontalCard cardTheme="grey">
                                <div className="card-data">
                                    <p className="label">Lotes cancelados pelo prestador</p>
                                    <p className="value">{dadosQuantitativos?.quantidadeLotesCancelados}</p>
                                </div>
                            </S.HorizontalCard>
                        </S.FlexColumn>
                        <S.CardDefault cardTheme="grey" style={{ width: 'fit-content', padding: '1.6rem 2.4rem' }}>
                            <div className="card-data">
                                <p className="label">Lotes recusados</p>
                                <p className="value">{dadosQuantitativos?.quantidadeLotesRecusados}</p>
                            </div>
                        </S.CardDefault>
                        <S.CardDefault cardTheme="grey" style={{ width: 'fit-content', padding: '1.6rem 2.4rem' }}>
                            <div className="card-data">
                                <p className="label">Lotes não sincronizados</p>
                                <p className="value">{dadosQuantitativos?.quantidadeLotesNaoSicronizados}</p>
                            </div>
                            <S.CardInfoIcon style={{ marginLeft: 'auto', marginBottom: '2.4rem' }}>
                                <CardToolTipComponent
                                    title={'O que são lotes não sincronizados?'}
                                    description={
                                        'Lotes não sincronizados são lotes processados, que estão em análise, mas não chegaram no de serviço análise de contas.'
                                    }
                                />
                            </S.CardInfoIcon>
                            {isOwner && (
                                <Button
                                    typeButton="text"
                                    themeButton="primary"
                                    onClick={() => sincronizarLotesEmAnalise(competencia)}
                                    disabled={
                                        dadosQuantitativos?.quantidadeLotesNaoSicronizados <= 0 ||
                                        sincronizandoLotesEmAnalise ||
                                        !isCompetenciaAtual ||
                                        !isCompetenciaAberta
                                    }
                                >
                                    Sincronizar
                                </Button>
                            )}
                        </S.CardDefault>
                    </S.FlexRow>
                    <S.FlexRow>
                        <S.CardWithIcon cardTheme="grey">
                            <S.CardIcon cardTheme="grey">
                                <ReactSVG src="/faturamento/assets/icons/reload.svg" wrapper="div" />
                            </S.CardIcon>
                            <div className="card-data">
                                <p className="label">Lotes agendados</p>
                                <p className="value">{dadosQuantitativos?.quantidadeLotesAgendados}</p>
                            </div>
                            <S.CardInfoIcon style={{ marginLeft: 'auto', marginBottom: '3.2rem' }}>
                                <CardToolTipComponent
                                    title={'O que são lotes agendados?'}
                                    description={'Os lotes agendados ainda estão na fila, aguardando para serem processados pelo sistema.'}
                                />
                            </S.CardInfoIcon>
                            {isOwner && (
                                <Button
                                    typeButton="text"
                                    themeButton="primary"
                                    onClick={() => redistribuirLotesAgendados(competencia)}
                                    disabled={
                                        redistribuindoLotesAgendados ||
                                        !permitirRedistribuirLotesAgendados ||
                                        !isCompetenciaAtual ||
                                        !isCompetenciaAberta
                                    }
                                >
                                    Redistribuir
                                </Button>
                            )}
                        </S.CardWithIcon>
                        <S.CardWithIcon cardTheme="grey">
                            <S.CardIcon cardTheme="grey">
                                <ReactSVG src="/faturamento/assets/icons/reload.svg" wrapper="div" />
                            </S.CardIcon>
                            <div className="card-data">
                                <p className="label">Lotes em processamento</p>
                                <p className="value">{dadosQuantitativos?.quantidadeLotesProcessando}</p>
                            </div>
                        </S.CardWithIcon>
                        <S.CardWithIcon cardTheme="pink">
                            <S.CardIcon cardTheme="pink">
                                <ReactSVG src="/faturamento/assets/icons/block.svg" wrapper="div" />
                            </S.CardIcon>
                            <div className="card-data">
                                <p className="label">Lotes com falhas</p>
                                <p className="value">{dadosQuantitativos?.quantidadeLotesComFalha}</p>
                            </div>
                        </S.CardWithIcon>
                        <S.CardWithIcon cardTheme="yellow">
                            <S.CardIcon cardTheme="yellow">
                                <ReactSVG src="/faturamento/assets/icons/warning.svg" wrapper="div" />
                            </S.CardIcon>
                            <div className="card-data">
                                <p className="label">Lotes em aberto</p>
                                <p className="value">
                                    {dadosQuantitativos?.lotesEmAbertoAcimaTempoBase}{' '}
                                    {!!dadosQuantitativos?.lotesEmAbertoAcimaTempoBase && (
                                        <span className="value-description">
                                            {` (${RegistrosProcessamentoContasService.tempoBaseConsiderarLoteEmAberto}min+)`}
                                        </span>
                                    )}
                                </p>
                            </div>
                            <S.CardInfoIcon style={{ marginLeft: 'auto', marginBottom: '3.2rem' }}>
                                <CardToolTipComponent
                                    title={'O que são lotes em aberto?'}
                                    description={
                                        'Os lotes em aberto são lotes que estão agendados, em processamento, ou falharam há mais tempo que o normal.'
                                    }
                                />
                            </S.CardInfoIcon>
                        </S.CardWithIcon>
                    </S.FlexRow>
                    <S.FlexRow>
                        <Button
                            iconLeft="/faturamento/assets/icons/blue-plus.svg"
                            typeButton="text"
                            themeButton="primary"
                            onClick={() => setShowModalMaisIndicadores(true)}
                            style={{ width: 'fit-content', margin: '0 auto' }}
                        >
                            Mais indicadores
                        </Button>
                    </S.FlexRow>
                </S.FlexColumn>
            )}
            {showModalMaisIndicadores && (
                <ModalMaisIndicadores
                    isOpen={showModalMaisIndicadores}
                    competencia={competencia}
                    dadosQuantitativos={dadosQuantitativos}
                    loadingDadosQuantitativos={loadingDadosQuantitativos}
                    onRefreshDadosQuantitativosPainel={onRefreshDadosQuantitativosPainel}
                    onClose={() => setShowModalMaisIndicadores(false)}
                />
            )}
        </S.Container>
    )
}

export default DadosQuantitativos
