import Input from 'components/atoms/Input'
import Select from 'components/molecules/Select'
import React, { useEffect, useState } from 'react'
import { ReactSVG } from 'react-svg'
import { useToast } from 'src/hooks/toast'
import { AnaliseContasItemGuiaService } from 'src/services/analiseContasApi/analiseContasItemGuiaServices'
import { IItemGuiaDetalhesDTO } from 'types/analiseContas/itemGuia'
import ModalGlosa from '../GlosaModal'
import HistoricoItensModal from '../HistoricoItensModal'
import InfoItensModal from '../InfoItensModal'
import { IGlosarGuiaParcialQuery, IMotivoGlosaQuery } from 'types/analiseContas/glosarGuia'
import MemoriaCalculoModal from '../MemoriaCalculoModal'
import * as S from './styles'
import { IResumoHistoricoCalculoQuery } from 'types/analiseContas/historicoCalculo'
import { IMemoriaCalculoItemQuery } from 'types/analiseContas/memoriaCalculo'
import { IGuiaItensQuery } from 'types/analiseContas/guia'
import { NumberUtils } from 'utils/numberUtils'

const porcentagens = [
    {
        value: '100%',
        label: '100%'
    },
    {
        value: '70%',
        label: '70%'
    },
    {
        value: '50%',
        label: '50%'
    },
    {
        value: '30%',
        label: '30%'
    },
    {
        value: '20%',
        label: '20%'
    }
]

const CardItemGuia = ({ item, index }: { item: IGuiaItensQuery; index: number }) => {
    const [quantidadeApurada, setQuantidadeApurada] = useState(0)
    const [valorApurado, setValorApurado] = useState(0)
    const [detalhes, setDetalhes] = useState<IItemGuiaDetalhesDTO>()
    const [isModalOpen, setIsModalOpen] = useState(false)
    const [isModalGlosaOpen, setIsModalGlosaOpen] = useState(false)
    const [isModalMemoryOpen, setIsModalMemoryOpen] = useState(false)
    const [isModalHistoryOpen, setIsModalHistoryOpen] = useState(false)
    const [motivosItem, setMotivosItem] = useState<IMotivoGlosaQuery[]>([])
    const [historicoCalculo, setHistoricoCalculo] = useState<IResumoHistoricoCalculoQuery>({ revisoes: [] })
    const [memoriaCalculo, setMemoriaCalculo] = useState<IMemoriaCalculoItemQuery>()

    const [checkOk, setCheckOk] = useState<string>('OK')

    const { addToast } = useToast()

    const handleDetalhes = (id) => {
        setIsModalOpen(true)
        setIsModalOpen(true)
        AnaliseContasItemGuiaService.getItemGuiaDetalhes(id)
            .then(({ data }) => {
                setDetalhes(data)
            })
            .catch(() => {
                addToast({
                    title: 'Ocorreu um erro ao carregar detalhes do item.',
                    type: 'error',
                    duration: 3000
                })
            })
        // AnaliseContasItemGuiaService.getItemGuiaHistoricoCalculo(id)
        //     .then(({ data }) => {
        //         setHistoricoCalculo(data)
        //     })
        //     .catch(() => {
        //         addToast({
        //             title: 'Ocorreu um erro ao carregar o histórico de cálculo do item.',
        //             type: 'error',
        //             duration: 3000
        //         })
        //     })
        AnaliseContasItemGuiaService.getItemGuiaMemoriaCalculo(id)
            .then(({ data }) => {
                setMemoriaCalculo(data)
            })
            .catch(() => {
                addToast({
                    title: 'Ocorreu um erro ao carregar a memória de cálculo do item.',
                    type: 'error',
                    duration: 3000
                })
            })
    }

    useEffect(() => {
        if (motivosItem?.length === 0) {
            setCheckOk('OK')
            return
        }

        setCheckOk('EDICAO')

        return

        const data: IGlosarGuiaParcialQuery = {
            itemGuiaId: item.codigoItem,
            quantidadeApurada: item.quantidadeApresentada,
            valorApurado: item.valorApurado,
            motivosGlosa: motivosItem
        }

        // if (!guiasGlosadas?.some((e) => e.itemGuiaId === data.itemGuiaId)) {
        //     setGuiasGlosadas((itens) => [...itens, data])
        //     return
        // } else {
        //     const newArray = guiasGlosadas.map((e, index) => {
        //         if (e.itemGuiaId === data.itemGuiaId) {
        //             return data
        //         } else {
        //             return e
        //         }
        //     })
        //     setGuiasGlosadas(newArray)
        //     return
        // }

        setCheckOk('')
    }, [motivosItem])

    const handleClickGlosarItem = () => {
        const data: IGlosarGuiaParcialQuery = {
            itemGuiaId: item.codigoItem,
            quantidadeApurada: quantidadeApurada,
            valorApurado: valorApurado,
            motivosGlosa: motivosItem
        }

        console.log(data)
    }

    useEffect(() => {
        if (!item?.situacao) return

        if (item.situacao === 'APRESENTADO') {
            setCheckOk('OK')
        } else if (item.situacao === 'ANALISADO' || item.situacao === 'AUDITADO') {
            setCheckOk('GLOSADO')
        } else {
            setCheckOk('OK')
        }
    }, [item.situacao])

    return (
        <>
            <S.CardItemGuia key={index}>
                <S.GlosaContainer>
                    <S.ContentItem>
                        <div
                            style={{
                                display: 'flex',
                                alignItems: 'center'
                            }}
                        >
                            {checkOk === 'OK' ? (
                                <S.StyledCheckbox>
                                    <img src="/faturamento/assets/icons/sucess.svg" />
                                </S.StyledCheckbox>
                            ) : null}
                            {checkOk === 'EDICAO' ? (
                                <S.StyledCheckbox>
                                    <img src="/faturamento/assets/icons/iconWarningYellow.svg" />
                                </S.StyledCheckbox>
                            ) : null}
                            {checkOk === 'GLOSADO' ? (
                                <S.StyledCheckbox>
                                    <img src="/faturamento/assets/icons/iconWarningRed.svg" />
                                </S.StyledCheckbox>
                            ) : null}
                        </div>
                        {item.descricaoItem}
                    </S.ContentItem>
                    <div style={{ display: 'flex' }} onClick={() => handleDetalhes(item.id)}>
                        <ReactSVG src="/faturamento/assets/icons/info.svg" />
                        <p
                            style={{
                                paddingLeft: '7px',
                                fontSize: '14px',
                                color: '#2b45d4',
                                cursor: 'pointer'
                            }}
                        >
                            detalhes
                        </p>
                    </div>
                </S.GlosaContainer>

                <S.GlosaContent>
                    <S.ContainerInputs>
                        <div className="row">
                            <S.CardResult>
                                <p>Qtd. apresentada</p>
                                <span>{item.quantidadeApresentada}</span>
                            </S.CardResult>

                            <S.CardResult>
                                <p>Valor apresentado</p>
                                <span>{NumberUtils.maskMoney(item.valorApresentado)}</span>
                            </S.CardResult>

                            <S.CardResult>
                                <p>Tipo</p>
                                <span>{item.tipo}</span>
                            </S.CardResult>
                        </div>
                        <div className="row" style={{ paddingTop: '16px' }}>
                            <Input
                                isDefault="default"
                                label="Qtd. apurada"
                                placeholder=""
                                type="number"
                                value={quantidadeApurada.toString()}
                                initialValue="0"
                                handleOnChange={(qtde: string) => setQuantidadeApurada(parseInt(qtde))}
                            />
                            <Input
                                isDefault="default"
                                label="Valor apurado"
                                placeholder=""
                                type="text"
                                mask={'monetary'}
                                value={NumberUtils.maskMoney(valorApurado)}
                                initialValue={NumberUtils.maskMoney(item.valorApurado)}
                                handleOnChange={(vlr: string) => setValorApurado(NumberUtils.unMaskMoney(vlr))}
                            />
                            <Select label="Percentual" isDisabled={true} defaultValue={porcentagens[0]} options={porcentagens} />
                            <Input disabled={true} isDefault="default" label="Valor glosado" placeholder={NumberUtils.maskMoney(item.valorGlosado)} />
                            <Input
                                isDefault="default"
                                label="Total a pagar"
                                placeholder=""
                                type="text"
                                mask={'monetary'}
                                disabled={true}
                                initialValue={NumberUtils.maskMoney(item.valorApurado)}
                                value={NumberUtils.maskMoney(valorApurado)}
                            />
                            <S.GlosaButton
                                typeButton={'ghost'}
                                themeButton={'gray'}
                                style={{ width: '15%', marginLeft: 'auto' }}
                                onClick={() => setIsModalGlosaOpen(true)}
                                isEdit={motivosItem.length === 0 ? false : true}
                            >
                                {motivosItem.length < 1 ? (
                                    'Motivos de glosa'
                                ) : (
                                    <div>
                                        <span className="icon" style={{ marginRight: '8px' }}>
                                            {motivosItem.length}
                                        </span>
                                        <span>Editar glosa</span>
                                    </div>
                                )}
                            </S.GlosaButton>
                            <S.GlosaButton
                                typeButton={'ghost'}
                                themeButton={'gray'}
                                disabled={motivosItem?.length === 0}
                                style={{ width: '12%', marginLeft: 'auto' }}
                                onClick={() => handleClickGlosarItem()}
                                isEdit={motivosItem.length === 0 ? false : true}
                            >
                                Glosar Item
                            </S.GlosaButton>
                        </div>
                    </S.ContainerInputs>
                </S.GlosaContent>
            </S.CardItemGuia>

            <InfoItensModal
                detalhes={detalhes}
                isModalOpen={isModalOpen}
                setIsModalOpen={setIsModalOpen}
                setIsModalHistoryOpen={setIsModalHistoryOpen}
                setIsModalMemoryOpen={setIsModalMemoryOpen}
            />

            <HistoricoItensModal
                data={historicoCalculo}
                isModalHistoryOpen={isModalHistoryOpen}
                setIsModalHistoryOpen={setIsModalHistoryOpen}
                setIsModalOpen={setIsModalOpen}
            />
            <ModalGlosa
                isModalGlosaOpen={isModalGlosaOpen}
                setIsModalGlosaOpen={setIsModalGlosaOpen}
                motivosItem={motivosItem}
                setMotivosItem={setMotivosItem}
            />
            <MemoriaCalculoModal
                data={memoriaCalculo}
                isModalMemoryOpen={isModalMemoryOpen}
                setIsModalMemoryOpen={setIsModalMemoryOpen}
                setIsModalOpen={setIsModalOpen}
            />
        </>
    )
}

export default CardItemGuia
