import styled, { DefaultTheme, css, keyframes } from 'styled-components'

export const WrapperModal = styled.div`
    ${({ theme }) => css`
        display: flex;
        flex-direction: column;
        gap: ${theme.spacings.xsmall};

        h1 {
            font-size: 28px;
            line-height: ${theme.spacings.large};
            font-weight: ${theme.font.semiBold};
            color: ${theme.colors.black[88]};
        }

        p {
            font-size: ${theme.font.sizes.body};
            line-height: ${theme.spacings.small};
            font-weight: ${theme.font.normal};
            color: ${theme.colors.black[56]};
        }

        ul {
            margin-left: 16px;
        }
        li {
            font-size: ${theme.font.sizes.body};
            line-height: ${theme.spacings.small};
            font-weight: ${theme.font.normal};
            color: ${theme.colors.black[56]};
        }
    `}
`
export const WrapperInputs = styled.div`
    ${({ theme }) => css`
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: ${theme.spacings.xsmall};
        margin-top: ${theme.spacings.xsmall};
    `}
`
export const WrapperButtonsModal = styled.div`
    ${({ theme }) => css`
        display: flex;
        justify-content: flex-end;
        gap: ${theme.spacings.xsmall};
        margin-top: ${theme.spacings.xsmall};

        button {
            width: fit-content;
            cursor: pointer;
        }
    `}
`
