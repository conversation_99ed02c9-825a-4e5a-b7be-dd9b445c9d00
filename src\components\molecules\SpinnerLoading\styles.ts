import styled from 'styled-components'
import theme from 'styles/theme'

export const Container = styled.div`
    display: grid;
    place-items: center;
`

export const Spinner = styled.div`
    width: 4.8rem;
    height: 4.8rem;
    animation: spin 1s linear infinite;
    border: 0.4rem solid ${theme.colors.primary.transparent[16]};
    background-color: transparent;
    border-radius: 50%;
    position: relative;
    border-right-color: ${theme.colors.primary[500]};

    @keyframes spin {
        100% {
            transform: rotate(360deg);
        }
    }
`
