import React, { useEffect, useState } from 'react'
import * as S from './styles'
import SearchBar from 'components/molecules/SearchBar'
import { Box, CircularProgress, Select, Button as ButtonMui, TextField } from '@mui/material'
import SimpleSelect from 'components/molecules/SimpleSelect'
import Badge from 'components/atoms/Badge'
import AsyncSimpleSelect from 'components/molecules/AsyncSimpleSelect'
import { OptionsType } from 'src/templates/FaturamentoPrestador/LotesTemplate/types'
import NoContent from 'components/molecules/NoContent'
import TablePaginationRecurso from 'src/templates/FaturamentoPrestador/LotesTemplate/organisms/TablePaginationRecurso'
import { IPagination } from 'types/pagination'
import ModalGenericComponent from 'components/organisms/ModalGeneric'
import { RecursoGlosaOperadoraGuia } from 'src/services/recurso-glosa-operadora/guia'
import { useToast } from 'src/hooks/toast'
import { useDebounce } from 'utils/useDebounce'
import { IGetTableContentDto } from 'src/services/recurso-glosa-operadora/guia/type'
import ListDetailContent from '../ListDetailContent'
import { PaginationHelper } from 'utils/helpers/paginationHelper'
import ModalIndeferirAllItens from '../ModalIndeferirAllItens'

export enum EnumStatus {
    GLOSADO = 'GLOSADO',
    RECURSADO = 'RECURSADO'
}

enum EnumItemStatus {
    AGUARDANDO_ANALISE = 'AGUARDANDO_ANALISE',
    DEFERIDO = 'DEFERIDO',
    DEFERIDO_PARCIAL = 'DEFERIDO_PARCIAL',
    INDEFERIDO = 'INDEFERIDO'
}

type GuideInformationTableProps = {
    tabName: string
    uuid: string
    refresh: boolean
    setRefresh: any
}

type IFieldValues = {
    codigo: string
    motivoGlosa: IOptionsValues
    status: IOptionsValues
}

type IOptionsValues = {
    label: string
    value: string
}

export type SelectedValues = {
    codigo: string
    descricao: string
    dropDownComponent: any
    item: string
    itemGuiaRecursoId: string
    status: string
    statusAnaliseItemRecurso: string
}

const GuideInformationTable = ({ tabName, uuid, setRefresh, refresh }: GuideInformationTableProps) => {
    const [loadingItemGuides, setLoadingItemGuides] = useState(false)
    const [itemSelecionados, setItemSelecionados] = useState<SelectedValues[]>([])
    const [itemPagination, setItemPagination] = useState<IPagination>()
    const [dataContent, setDataContent] = useState<IGetTableContentDto>()
    const [numberPage, setNumberPage] = useState(0)
    const { addToast } = useToast()
    const [fieldValues, setFieldValues] = useState<IFieldValues>()

    const [isOpen, setIsOpen] = useState({
        deferir: false,
        indeferir: false,
        indeferirAllItens: false,
        desfazer: false
    })

    const [filter, setFilter] = useState<string>()
    const debouncedValue = useDebounce<string>(filter, 500)

    const LotesTitles = [
        { label: 'Item', value: 'item' },
        { label: 'Status', value: 'status' }
    ]

    function getMotivos(termo: string) {
        const options: OptionsType[] = []

        RecursoGlosaOperadoraGuia.getMotivoGlosaGuia(uuid, {
            page: 0,
            size: 6,
            filtro: termo
        })
            .then(({ data }) => {
                data?.content?.forEach((item) => {
                    options.push({
                        label: item?.codigo + ' - ' + item?.descricao,
                        value: item?.uuid
                    })
                })
            })
            .catch((err) => {
                console.error(err)
            })
        return new Promise<any[]>((resolve) => {
            setTimeout(() => {
                resolve(options)
            }, 1000)
        }).catch((err) => {
            console.error(err)
        })
    }

    useEffect(() => {
        getItemGuia()
    }, [numberPage, debouncedValue, fieldValues, tabName, refresh])

    const getItemGuia = () => {
        setLoadingItemGuides(true)

        RecursoGlosaOperadoraGuia.getTableContent(uuid, {
            page: numberPage,
            size: 5,
            tipoItemGuia: tabName ? tabName : null,
            codigoItem: filter ? filter : null,
            codigoMotivoGlosa: fieldValues?.motivoGlosa?.value ? fieldValues?.motivoGlosa?.value : null,
            statusAnaliseItemRecurso: fieldValues?.status?.value ? fieldValues?.status?.value : null
        })
            .then(({ data }) => {
                setDataContent(data)
                setItemPagination(PaginationHelper.parserPagination(data as any, setNumberPage))
            })
            .catch(({ response }) => {
                addToast({
                    title: response?.data?.message,
                    type: 'error',
                    duration: 3000
                })
            })
            .finally(() => setLoadingItemGuides(false))
    }

    const handleClickToPatchDeferirItens = () => {
        RecursoGlosaOperadoraGuia?.patchDeferirItens({
            guiaRecursoId: uuid,
            tipoItemGuia: tabName,
            itensRecursosIds: itemSelecionados?.map((item) => item?.itemGuiaRecursoId)
        })
            .then(() => {
                addToast({
                    title: 'Itens deferidos com sucesso!',
                    type: 'success',
                    duration: 3000
                })

                setRefresh(!refresh)
                setItemSelecionados([])
                //
            })
            .catch(({ response }) => {
                addToast({
                    title: response?.data?.message,
                    type: 'error',
                    duration: 3000
                })
            })
            .finally(() => {
                setIsOpen({
                    ...isOpen,
                    deferir: false
                })
            })
    }

    const handleClickToPatchDesfazerItens = () => {
        RecursoGlosaOperadoraGuia?.patchDesfazerItens({
            guiaRecursoId: uuid,
            tipoItemGuia: tabName,
            itensRecursosIds: itemSelecionados?.map((item) => item?.itemGuiaRecursoId)
        })
            .then(() => {
                addToast({
                    title: 'Itens desfeitos com sucesso!',
                    type: 'success',
                    duration: 3000
                })

                setRefresh(!refresh)
                setItemSelecionados([])
            })
            .catch(({ response }) => {
                addToast({
                    title: response?.data?.message,
                    type: 'error',
                    duration: 3000
                })
            })
    }

    useEffect(() => {
        console.log('itemSelecionados', itemSelecionados)
    }, [itemSelecionados])

    return (
        <>
            <S.TopContent>
                <SearchBar
                    value={filter}
                    placeholder="Procurar por código"
                    handleOnSearch={() => {
                        setNumberPage(0)
                        getItemGuia()
                    }}
                    type="number"
                    handleOnClose={() => setFilter('')}
                    handleOnChange={(e) => {
                        if (e?.target?.value === '') {
                            setFilter('')
                        }
                        setFilter(e?.target?.value)
                        setNumberPage(0)
                    }}
                />

                <AsyncSimpleSelect
                    isClearable
                    label="Motivos de glosa"
                    defaultValue={{ label: 'Selecionar', value: null }}
                    value={fieldValues?.motivoGlosa}
                    loadOptions={getMotivos}
                    onChange={(e: any) => {
                        setFieldValues({ ...fieldValues, motivoGlosa: e })
                    }}
                />
                <S.SelectContainer>
                    <SimpleSelect
                        label="Status"
                        isClearable
                        defaultValue={{ label: 'Todos', value: null }}
                        value={fieldValues?.status}
                        onChange={(e: any) => {
                            setFieldValues({ ...fieldValues, status: e })
                        }}
                        options={[
                            { label: 'Aguardando Análise', value: 'AGUARDANDO_ANALISE' },
                            { label: 'Deferido', value: 'DEFERIDO' },
                            { label: 'Deferido Parcial', value: 'DEFERIDO_PARCIAL' },
                            { label: 'Indeferido', value: 'INDEFERIDO ' }
                        ]}
                    />
                </S.SelectContainer>
            </S.TopContent>

            <S.CenterContent>
                <>
                    {loadingItemGuides ? (
                        <Box sx={{ display: 'flex', width: '100%', justifyContent: 'center', alignItems: 'center', height: '30vh' }}>
                            <CircularProgress />
                        </Box>
                    ) : (
                        <>
                            {dataContent?.content?.length > 0 ? (
                                <>
                                    <TablePaginationRecurso
                                        selectIdField={'itemGuiaRecursoId'}
                                        titles={LotesTitles}
                                        values={dataContent?.content?.map((item) => {
                                            return {
                                                ...item,
                                                item: (
                                                    <S.Item>
                                                        {item?.codigo} - {item?.descricao}
                                                    </S.Item>
                                                ),
                                                status: (
                                                    <>
                                                        {item?.statusAnaliseItemRecurso === EnumItemStatus?.AGUARDANDO_ANALISE && (
                                                            <Badge
                                                                background={
                                                                    item?.statusAnaliseItemRecurso === EnumItemStatus?.AGUARDANDO_ANALISE && '#f7eace'
                                                                }
                                                                color={
                                                                    item?.statusAnaliseItemRecurso === EnumItemStatus?.AGUARDANDO_ANALISE && '#e59500'
                                                                }
                                                                text={'Aguardando análise'}
                                                                style={{ padding: '0px 8px' }}
                                                                fontSize="14px"
                                                            />
                                                        )}

                                                        {item?.statusAnaliseItemRecurso === EnumItemStatus?.DEFERIDO_PARCIAL && (
                                                            <Badge
                                                                background={
                                                                    item?.statusAnaliseItemRecurso === EnumItemStatus?.DEFERIDO_PARCIAL && '#CFE6F6'
                                                                }
                                                                color={
                                                                    item?.statusAnaliseItemRecurso === EnumItemStatus?.DEFERIDO_PARCIAL && '#0079b8'
                                                                }
                                                                text={'Deferido parcial'}
                                                                style={{ padding: '0px 8px' }}
                                                                fontSize="14px"
                                                            />
                                                        )}

                                                        {item?.statusAnaliseItemRecurso === EnumItemStatus?.INDEFERIDO && (
                                                            <Badge
                                                                background={
                                                                    item?.statusAnaliseItemRecurso === EnumItemStatus?.INDEFERIDO && '#F6DBDD'
                                                                }
                                                                color={item?.statusAnaliseItemRecurso === EnumItemStatus?.INDEFERIDO && '#DB3C31'}
                                                                text={'Indeferido'}
                                                                style={{ padding: '0px 8px' }}
                                                                fontSize="14px"
                                                            />
                                                        )}

                                                        {item?.statusAnaliseItemRecurso === EnumItemStatus?.DEFERIDO && (
                                                            <Badge
                                                                background={item?.statusAnaliseItemRecurso === EnumItemStatus?.DEFERIDO && '#d7ebda'}
                                                                color={item?.statusAnaliseItemRecurso === EnumItemStatus?.DEFERIDO && '#34a643'}
                                                                text={'Deferido'}
                                                                style={{ padding: '0px 8px' }}
                                                                fontSize="14px"
                                                            />
                                                        )}
                                                    </>
                                                ),
                                                dropDownComponent: (
                                                    <ListDetailContent
                                                        item={item}
                                                        refresh={refresh}
                                                        setRefresh={setRefresh}
                                                        itemId={item?.itemGuiaRecursoId}
                                                    />
                                                )
                                            }
                                        })}
                                        pagination={itemPagination}
                                        customGridStyles="8fr 1.2fr 0.1fr"
                                        enableSelect
                                        enableDropdown
                                        handleSelect={setItemSelecionados}
                                        checkSelected={itemSelecionados}
                                        selectedAllAction
                                        selectedComponent={
                                            <>
                                                <ButtonMui
                                                    color="neutral"
                                                    variant="outlined"
                                                    size="small"
                                                    onClick={() => setIsOpen({ ...isOpen, deferir: true })}
                                                >
                                                    Deferir
                                                </ButtonMui>
                                                <ButtonMui
                                                    color="neutral"
                                                    variant="outlined"
                                                    size="small"
                                                    onClick={() => setIsOpen({ ...isOpen, indeferir: true })}
                                                >
                                                    Indeferir
                                                </ButtonMui>
                                                <ButtonMui color="neutral" variant="outlined" size="small" onClick={handleClickToPatchDesfazerItens}>
                                                    Desfazer
                                                </ButtonMui>
                                            </>
                                        }
                                    />
                                </>
                            ) : (
                                <>
                                    <NoContent
                                        title="No momento não existe nenhuma guia aguardando recurso de glosa"
                                        path="/parametros/regras-de-excludencia/nova-regra"
                                    />
                                </>
                            )}
                        </>
                    )}
                </>
                {/* MODAL  DEFERIR*/}
                <ModalGenericComponent
                    open={isOpen?.deferir}
                    setOpenModal={() =>
                        setIsOpen({
                            ...isOpen,
                            deferir: !isOpen?.deferir
                        })
                    }
                    widthModal="720px"
                    title="Deferir os itens selecionados?"
                    cancelButton="Cancelar"
                    actionButton="Deferir"
                    colorButton={'success'}
                    // disabledButton={Boolean(!recursoJustificativaRecurso)}
                    actionFunction={handleClickToPatchDeferirItens}
                >
                    <S.Text>
                        Essa ação irá deferir o valor recursado dos itens selecionados. Quaisquer status ou dados informados anteriormente pela
                        análise serão sobrescrita.
                    </S.Text>
                </ModalGenericComponent>

                {/* MODAL INDEFERIR EM MASSA */}
                <ModalIndeferirAllItens
                    uuid={uuid}
                    tabName={tabName}
                    itensRecursosIds={itemSelecionados?.map((item) => item?.itemGuiaRecursoId)}
                    openModal={isOpen?.indeferirAllItens}
                    setCloseModal={() =>
                        setIsOpen({
                            ...isOpen,
                            indeferirAllItens: false
                        })
                    }
                    handleIndeferir={() => {
                        setRefresh(!refresh)
                        setIsOpen({
                            ...isOpen,
                            indeferirAllItens: false
                        })
                    }}
                    setItemSelecionados={setItemSelecionados}
                />

                {/* MODAL  INDEFERIR*/}
                <ModalGenericComponent
                    open={isOpen?.indeferir}
                    setOpenModal={() =>
                        setIsOpen({
                            ...isOpen,
                            indeferir: !isOpen?.indeferir
                        })
                    }
                    widthModal="720px"
                    title="Indeferir os itens selecionados?"
                    cancelButton="Cancelar"
                    actionButton="Indeferir"
                    colorButton={'error'}
                    // disabledButton={Boolean(!recursoJustificativaRecurso)}
                    actionFunction={() =>
                        setIsOpen({
                            ...isOpen,
                            indeferirAllItens: !isOpen?.indeferirAllItens,
                            indeferir: false
                        })
                    }
                >
                    <S.Text>
                        Essa ação irá indeferir os itens selecionados. Quaisquer status ou dados informados anteriormente pela análise serão
                        sobrescrita.
                    </S.Text>
                </ModalGenericComponent>
            </S.CenterContent>
        </>
    )
}

export default GuideInformationTable
