import { GetServerSideProps } from 'next'
import React from 'react'
import RegraAddProcessadorTemplate from 'src/templates/FaturamentoOperadora/ParametrosFaturamentoTemplates/RegraProcessador/RegraProcessadorCriarTemplate'

const AlterarRegra = ({ id }) => {
    return <RegraAddProcessadorTemplate id={id} />
}
export const getServerSideProps: GetServerSideProps = async (ctx) => {
    return {
        props: {
            id: ctx.query?.id
        }
    }
}

export default AlterarRegra
