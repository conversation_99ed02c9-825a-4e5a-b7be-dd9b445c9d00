import { Autocomplete, FormControl, TextField } from '@mui/material'
import Button from 'components/atoms/Button'
import { TitleSection } from 'components/atoms/TitleSection/styles'
import AsyncSimpleSelect from 'components/molecules/AsyncSimpleSelect'
import ItemSwitch from 'components/molecules/ItemSwitch'
import Layout from 'components/molecules/Layout'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import { ReactSVG } from 'react-svg'
import { useToast } from 'src/hooks/toast'
import { ProxyCredenciamento } from 'src/services/processadorRegrasApi/Proxy - Credenciamento Api'
import { Regras } from 'src/services/processadorRegrasApi/regras'
import { regraAssociadaInasEnum, regraAssociadaPVEnum } from 'src/services/processadorRegrasApi/regras/enuns'
import { defaultSelectOption, selectProps } from 'types/common/select'
import { regraAssociadaOptions } from './mock'
import * as S from './styles'
import { PrestadorService } from 'src/services/faturamentoOperadoraApi/prestadorServices'
import AsyncPaginateSelect from 'components/atoms/AsyncPaginateSelect'
import { parserLoadOption } from 'components/atoms/AsyncPaginateSelect/functions'

export interface IRegraFields {
    descricao: string
    regraAssosciada: regraAssociadaPVEnum | regraAssociadaInasEnum
    situacao: boolean
}
interface props {
    id?: string
}
const RegraAddProcessadorTemplate = ({ id }: props) => {
    const router = useRouter()
    const { addToast } = useToast()
    const [regraFields, setRegraFields] = useState<IRegraFields>({
        descricao: '',
        regraAssosciada: null,
        situacao: true
    })
    const [prestadorOption, setPrestadorOption] = useState<selectProps>(null)
    const [prestadoresList, setPrestadoresList] = useState<selectProps[]>([])

    useEffect(() => console.log('prestadoresList', prestadoresList), [prestadoresList])

    const addProvider = () => {
        if (prestadoresList?.some(({ value }) => value === prestadorOption.value)) {
            addToast({
                title: 'Prestador duplicado!',
                duration: 3000,
                type: 'error'
            })
            return
        }

        if (id) {
            Regras.postPrestadoresById(id, [
                {
                    prestadorId: prestadorOption?.value,
                    cnpj: prestadorOption?.label.split(' - ')[0],
                    nomeFantasia: prestadorOption?.label.split(' - ')[1]
                }
            ])
                .then(({ data }) => {
                    const parsePrestadores: selectProps[] = data?.map((item) => {
                        return {
                            label: `${item?.cnpj} - ${item?.nomeFantasia}`,
                            value: item?.prestadorId
                        }
                    })
                    setPrestadoresList((prev) => [...prev, ...parsePrestadores])
                })
                .catch(({ response }) => {
                    addToast({
                        title: response?.data?.message,
                        type: 'error',
                        duration: 4000
                    })
                })
        } else {
            setPrestadoresList([
                ...(prestadoresList || []),
                {
                    label: prestadorOption.label,
                    value: prestadorOption.value
                }
            ])
        }

        setPrestadorOption(null)
    }

    const removeProvider = (prstadorUuid: string) => {
        if (id) {
            Regras.deletePrestadoresById(id, [
                {
                    prestadorId: prstadorUuid
                }
            ])
                .then(() => {
                    getPrestadoresDispensados()
                })
                .catch(({ response }) => {
                    addToast({
                        title: response?.data?.message,
                        type: 'error',
                        duration: 4000
                    })
                })
        } else {
            setPrestadoresList(prestadoresList.filter((provider) => provider?.value !== prstadorUuid))
        }
    }

    async function loadPrestador(filter: string) {
        setPrestadorOption({ label: '', value: '' } as selectProps)
        // TODO: VOLTAR PARA O PROXY
        return PrestadorService.getPrestador({
            page: 0,
            size: 20,
            // cpfCnpj: filter
            nomeCompletoNomeFantasia: filter
        }).then(({ data }) => {
            return data?.content?.map((prestadorOption) => {
                return {
                    value: prestadorOption?.uuid,
                    label:
                        prestadorOption?.cnpj != null
                            ? `${prestadorOption?.cnpj} - ${prestadorOption?.nomeFantasia}`
                            : `${prestadorOption?.cpf} - ${prestadorOption?.nomeFantasia}`
                }
            })
        })
    }

    function mudarSituacao() {
        Regras.patch(id)
            .then(() => {
                setRegraFields((prev) => ({ ...prev, situacao: !prev?.situacao }))
            })
            .catch(({ response }) => {
                addToast({
                    title: response?.data?.message,
                    type: 'error',
                    duration: 4000
                })
            })
    }

    function addRule() {
        Regras.post({
            descricao: regraFields?.descricao,
            regraAssociada: regraFields?.regraAssosciada,
            situacao: regraFields?.situacao,
            prestadoresDispensados:
                prestadoresList?.length > 0
                    ? prestadoresList?.map((item) => {
                          const prestador = {
                              prestadorId: item?.value
                          }
                          return prestador
                      })
                    : null
        })
            .then(() => {
                router.replace('/parametros/processador-de-regras')
            })
            .catch(({ response }) => {
                addToast({
                    title: response?.data?.message,
                    type: 'error',
                    duration: 4000
                })
            })
    }

    function getRegra() {
        Regras.getRegraById(id)
            .then(({ data }) => {
                setRegraFields({
                    descricao: data?.descricao,
                    regraAssosciada: data?.regraAssociada,
                    situacao: data?.situacao
                })
            })
            .catch(({ response }) => {
                addToast({
                    title: response?.data?.message,
                    type: 'error',
                    duration: 4000
                })
            })
    }

    function getPrestadoresDispensados() {
        Regras.getPrestadoresById(id)
            .then(({ data }) => {
                const parsePrestadores: selectProps[] = data?.content?.map((item) => {
                    return {
                        label: `${item?.cnpj} - ${item?.nomeFantasia}`,
                        value: item?.prestadorId
                    }
                })
                setPrestadoresList(parsePrestadores)
            })
            .catch(({ response }) => {
                addToast({
                    title: response?.data?.message,
                    type: 'error',
                    duration: 4000
                })
            })
        //
    }
    const loadRegras = async (text, prev, { page }) => {
        // TODO: FILTRO DE PESQUISA
        return Regras.getRegrasDisponiveis({ identificadorOuDescricao: text, size: 10, page }).then(({ data }) => {
            return parserLoadOption(data, 'identificador', 'descricao')
        })
    }

    useEffect(() => {
        if (id) {
            getRegra()
            getPrestadoresDispensados()
        }
    }, [id])

    return (
        <Layout isLoggedIn={true} title={id ? 'Editar regra' : 'Nova regra'}>
            <S.Container>
                <TitleSection>Parâmetros</TitleSection>
                <S.FormInput
                    onSubmit={(event) => {
                        event.preventDefault()
                    }}
                >
                    <S.ContainerInputs>
                        <div className="row">
                            <div style={{ marginLeft: 'auto' }}>
                                <ItemSwitch
                                    text="Ativo"
                                    disabled={!id}
                                    isOn={regraFields?.situacao}
                                    handleToggle={() => {
                                        mudarSituacao()
                                    }}
                                />
                            </div>
                        </div>
                        <div className="row">
                            {id ? (
                                <TextField
                                    disabled
                                    label="Regra associada"
                                    value={regraFields?.regraAssosciada || ''}
                                    fullWidth
                                    onChange={({ target }) => {
                                        setRegraFields((prev) => ({ ...prev, descricao: target.value }))
                                    }}
                                />
                            ) : (
                                <FormControl>
                                    <AsyncPaginateSelect
                                        label="Regra associada"
                                        loadOptions={loadRegras}
                                        value={{ label: regraFields?.regraAssosciada, value: regraFields?.descricao }}
                                        onChange={(value) => {
                                            setRegraFields((prev) => ({
                                                ...prev,
                                                regraAssosciada: value?.label,
                                                descricao: value?.value
                                            }))
                                        }}
                                    />
                                </FormControl>
                            )}

                            <TextField
                                disabled={!!id}
                                required={!id}
                                label="Descrição"
                                value={regraFields?.descricao}
                                fullWidth
                                onChange={({ target }) => {
                                    setRegraFields((prev) => ({ ...prev, descricao: target.value }))
                                }}
                            />
                        </div>

                        <div className="row">
                            <AsyncSimpleSelect
                                defaultValue={prestadorOption}
                                value={prestadorOption}
                                onChange={(e) => {
                                    setPrestadorOption(e)
                                }}
                                label="Prestador isento"
                                loadOptions={loadPrestador}
                                defaultOptions={true}
                                isClearable
                            />
                            <Button
                                iconLeft="/regulacao/assets/icons/plus-white.svg"
                                themeButton="secondary"
                                style={{
                                    width: 'fit-content'
                                }}
                                onClick={addProvider}
                                disabled={!prestadorOption?.value}
                            >
                                Adicionar
                            </Button>
                        </div>
                        <div className="row">
                            {prestadoresList?.length > 0 && (
                                <S.ProvidersList>
                                    {prestadoresList?.map((item, index) => (
                                        <S.ProviderItem key={index}>
                                            <p>{item?.label?.split(' - ')[1]}</p>
                                            <ReactSVG
                                                src={'/faturamento/assets/icons/close.svg'}
                                                className="closeButton"
                                                wrapper="div"
                                                onClick={() => removeProvider(item?.value)}
                                            />
                                        </S.ProviderItem>
                                    ))}
                                </S.ProvidersList>
                            )}
                        </div>
                    </S.ContainerInputs>
                </S.FormInput>
            </S.Container>
            <S.ButtonContainer>
                {!id && (
                    <Button
                        disabled={!regraFields?.regraAssosciada}
                        themeButton={'warning'}
                        style={{ width: '20%', marginTop: '40px' }}
                        iconLeft={'/faturamento/assets/icons/plus.svg'}
                        onClick={() => addRule()}
                    >
                        Criar regra
                    </Button>
                )}
            </S.ButtonContainer>
        </Layout>
    )
}

export default RegraAddProcessadorTemplate
