import { useState, useEffect, useMemo, useCallback } from 'react'
import * as S from './styles'
import CardSliderCompetencias from './organisms/CardSliderCompetencias'
import { IGetDetalhesCompetenciaProps, IPrestadorAnaliseQuery } from 'types/cobrancaPrestador/prestadorAnalise'
import { DateUtils } from 'utils/dateUtils'
import { useAuth } from 'src/hooks/auth'
import { CobrancaServices } from 'src/services/faturamentoPrestadorApi/cobrancaServices'
import { SituacaoPrestadorAnalise, TipoFaturamento } from 'types/common/enums'
import { IGetPropsCalendarioEnvioProps } from 'types/cobrancaPrestador/competenciaEnvio'
import Alert from 'components/atoms/Alert'
import NavTabs, { Tab } from 'components/molecules/NavTabs'
import moment from 'moment'
import React from 'react'
import { Demonstrativo } from 'src/services/contasAPagarApi/demonstrativo-controller'
import { IGetDadosGerais } from 'src/services/contasAPagarApi/demonstrativo-controller/types'
import { ContasAPagar } from 'src/services/faturamentoPrestadorApi/contas-apagar-controller'
import { GeracaoRecursoGlosa } from 'src/services/faturamentoPrestadorApi/geracao-recurso-glosa'
import { useToast } from 'src/hooks/toast'

import ContentLoteMedico from './organisms/TabLoteMedico'
import { Competencia } from 'types/common/competencia'

const SimulacaoTemplate = () => {
    const currentYear = new Date().getFullYear()
    const { addToast } = useToast()

    const { prestadorVinculado } = useAuth()

    const [competenciaSelecionada, setCompetenciaSelecionada] = useState<IPrestadorAnaliseQuery>()
    const [competencias, setCompetencias] = useState<IPrestadorAnaliseQuery[]>([])
    const [competenciaDisponivel, setCompetenciaDisponivel] = useState<any>([])
    const [anoExercicio, setAnoExercicio] = useState<number>()
    const [anos, setAnos] = useState([])
    const [alertVisible, setAlertVisible] = useState(false)
    const [forceUpdateCompetencias, setForceUpdateCompetencias] = useState('initial')
    const [forceUpdateDemonstrativo, setForceUpdateDemonstrativo] = useState('initial')
    const [loadingCompetencias, setLoadingCompetencias] = useState(false)
    const [enviarNotaFiscal, setEnviarNotaFiscal] = React.useState(false)
    const [demonstrativo, setDemonstrativo] = React.useState<IGetDadosGerais>()
    const [motivoReprovacao, setMotivoReprovacao] = React.useState<string>()

    const initialValueSelect = useMemo(() => {
        return anoExercicio && { label: anoExercicio.toString(), value: anoExercicio.toString() }
    }, [anoExercicio])

    // const initialValueSelect = useMemo(() => anoExercicio && { label: anoExercicio.toString(), value: anoExercicio.toString() }, [anoExercicio])
    const anosExercicio = useMemo(() => anos?.map(({ ano }) => ({ label: ano.toString(), value: ano.toString() })), [anos])
    const [bannerRecurso, setBannerRecurso] = useState<{
        demonstrativoGerado: boolean
        quantidadeDiasRestantes: number
        bannerRecursoVisualizado: boolean
    }>()

    const getCompetentecias = () => {
        const props: IGetDetalhesCompetenciaProps = {
            exercicio: parseInt(initialValueSelect?.value),
            idPrestador: prestadorVinculado?.uuid,
            tipoFaturamento: TipoFaturamento.NORMAL
        }

        const propsCalendario: IGetPropsCalendarioEnvioProps = {
            modulo: 'MEDICO'
        }

        if (initialValueSelect?.value && prestadorVinculado?.uuid) {
            CobrancaServices.getDetalhesCompetencia(props).then(({ data }) => {
                if (data?.length === 0) {
                    setAlertVisible(true)
                    buscarCompetenciasDisponiveis()
                    setLoadingCompetencias(false)
                    setCompetencias(data)
                } else {
                    const sortCompetencias = data?.sort((a, b) => moment(a.competencia).diff(b.competencia))
                    const selecionado = sortCompetencias[sortCompetencias.length - 1]

                    CobrancaServices.getCompetenciasEnvio(propsCalendario)
                        .then(({ data }) => {
                            const competencias = data
                                ?.map((item) => {
                                    const resp: IPrestadorAnaliseQuery = {
                                        competencia: item.competencia,
                                        situacao: SituacaoPrestadorAnalise.EMPTY,
                                        prestadorId: prestadorVinculado?.uuid
                                    }
                                    return resp
                                })

                                .filter((competencia) => !sortCompetencias?.map((item) => item.competencia).includes(competencia.competencia))

                            const allCompetencias = [...sortCompetencias, ...competencias]?.sort((a, b) => moment(a.competencia).diff(b.competencia))

                            // TODO: ANALISAR LÓGICA

                            const newAllCompetencias = allCompetencias?.filter(
                                (item) => item?.competencia?.split('-')[0]?.toString() === anoExercicio?.toString()
                            )

                            //

                            setCompetencias(newAllCompetencias)
                        })
                        .catch(() => {
                            addToast({ type: 'error', title: 'Erro ao buscar todos as competencias' })
                            setCompetencias(sortCompetencias)
                        })

                    const competenciaLocal = getCompetenciaIfNotExpired()

                    if (competenciaLocal) {
                        setCompetenciaSelecionada(competenciaLocal)
                    } else {
                        setCompetenciaSelecionada(selecionado)
                    }

                    setAlertVisible(false)
                    setLoadingCompetencias(false)
                }
            })
        }
    }

    function getInfoBanner() {
        GeracaoRecursoGlosa.getBannerRecurso({ competencia: competenciaSelecionada?.competencia })
            .then(({ data }) => {
                setBannerRecurso(data)
            })
            .catch((err) => undefined)
    }

    function postBannerVisualizacao() {
        GeracaoRecursoGlosa.postBannerRecurso({ competencia: competenciaSelecionada?.competencia })
            .then(({ data }) => {
                getInfoBanner()
            })
            .catch((err) => undefined)
    }

    const forceUpdate = () => {
        setForceUpdateCompetencias('update')
    }

    useEffect(() => {
        if (initialValueSelect?.value && prestadorVinculado?.uuid) {
            getCompetentecias()
            Demonstrativo.getDadoGeraisByCompetencia(competenciaSelecionada?.competencia)
                .then(({ data }) => {
                    setDemonstrativo(data)
                    if (data?.dadosFinanceiros?.statusDemostrativo === 'NF_REPROVADA') {
                        ContasAPagar.getNotaFiscalUltima({ demonstrativoUUID: data.demonstrativoUUID }).then(({ data }) => {
                            setMotivoReprovacao(data?.motivoReprovacao)
                        })
                    } else {
                        setMotivoReprovacao(undefined)
                    }
                })
                .catch((err) => undefined)
        }
    }, [initialValueSelect])

    useEffect(() => {
        if (forceUpdateCompetencias && forceUpdateCompetencias !== 'initial') {
            setLoadingCompetencias(true)
            setTimeout(() => {
                getCompetentecias()
            }, 10000)
        }
    }, [forceUpdateCompetencias])

    useEffect(() => {
        if (prestadorVinculado?.uuid) {
            CobrancaServices.getExercicios(prestadorVinculado?.uuid)
                .then(({ data }) => {
                    const exercicios = data?.sort((a, b) => b.ano - a.ano)

                    setAnos(exercicios)
                    setAnoExercicio(exercicios?.find((e) => e.ano === currentYear)?.ano || exercicios?.[0]?.ano)
                })
                .catch((err) => undefined)
        }
    }, [prestadorVinculado])

    const onChangeAnoExercicio = useCallback(
        (ano) => {
            // resetValues()
            setAnoExercicio(ano)
        },
        [setAnoExercicio]
    )

    useEffect(() => {
        if (prestadorVinculado?.uuid) {
            Demonstrativo.getDadoGeraisByCompetencia(competenciaSelecionada?.competencia)
                .then(({ data }) => {
                    setDemonstrativo(data)
                    if (data?.dadosFinanceiros?.statusDemostrativo === 'NF_REPROVADA') {
                        ContasAPagar.getNotaFiscalUltima({ demonstrativoUUID: data.demonstrativoUUID }).then(({ data }) => {
                            setMotivoReprovacao(data?.motivoReprovacao)
                        })
                    } else {
                        setMotivoReprovacao(undefined)
                    }
                })
                .catch(() => setDemonstrativo(undefined))
        }
        getInfoBanner()
    }, [competenciaSelecionada])

    useEffect(() => {
        if (forceUpdateDemonstrativo !== 'initial') {
            Demonstrativo.getDadoGeraisByCompetencia(competenciaSelecionada?.competencia)
                .then(({ data }) => {
                    setDemonstrativo(data)
                    if (data?.dadosFinanceiros?.statusDemostrativo === 'NF_REPROVADA') {
                        ContasAPagar.getNotaFiscalUltima({ demonstrativoUUID: data.demonstrativoUUID }).then(({ data }) => {
                            setMotivoReprovacao(data?.motivoReprovacao)
                        })
                    } else {
                        setMotivoReprovacao(undefined)
                    }
                })
                .catch(() => setDemonstrativo(undefined))

            setForceUpdateDemonstrativo('initial')
        }
    }, [forceUpdateDemonstrativo])

    const CB_forceUpdateDemonstrativo = () => {
        setForceUpdateDemonstrativo('update')
    }

    const parseCompetencias = (novaCompetencia?: IPrestadorAnaliseQuery[]) => {
        if (novaCompetencia) {
            return novaCompetencia?.map((item) => {
                return { month: DateUtils.getMonthName(item?.competencia), competencia: item?.competencia, status: item?.situacao?.toString() }
            })
        } else {
            return competencias?.map((item) => {
                return { month: DateUtils.getMonthName(item?.competencia), competencia: item?.competencia, status: item?.situacao?.toString() }
            })
        }
    }

    const buscarCompetenciasDisponiveis = () => {
        // let modulo = tipoCobranca
        //     .normalize('NFD')
        //     .replace(/[\u0300-\u036f]/g, '')
        //     .toUpperCase()
        let modulo: 'MEDICO' | 'ODONTO' | 'RECURSO'

        const props = {
            modulo: 'MEDICO',
            idPrestador: prestadorVinculado?.uuid || '',
            competencia: new Date().toISOString().split('T')[0]
        }

        CobrancaServices.getCompetenciasDisponiveis(props)
            .then(({ data }) => {
                if (data?.length === 0) {
                    setCompetenciaDisponivel([])
                } else {
                    const sortCompetencias = data?.sort((a, b) => moment(a.competencia).diff(b.competencia))
                    const selecionado = sortCompetencias[sortCompetencias?.length - 1]

                    setCompetenciaSelecionada(selecionado)
                }
            })
            .catch((err) => undefined)
    }

    const handleSetCompetencia = (competencia: Competencia) => {
        setCompetenciaSelecionada({ competencia: competencia?.competencia, situacao: competencia?.status as SituacaoPrestadorAnalise })
        forceUpdate()
        // Obtém o ano atual
        const currentYear = new Date().getFullYear()

        // Salva competencia no localStorage com uma data de expiração de 12 horas somente se anoExercicio for igual ao ano atual
        if (anoExercicio === currentYear) {
            const expirationDate = new Date()
            expirationDate.setHours(expirationDate.getHours() + 12) // Adiciona 12 horas à data atual
            const storedData = {
                competenciaValue: competencia,
                expiration: expirationDate.toISOString()
            }
            localStorage.setItem('@cobranca-competenciaData', JSON.stringify(storedData))
        }
    }

    const getCompetenciaIfNotExpired = () => {
        const storedData = localStorage.getItem('@cobranca-competenciaData')

        if (storedData) {
            const { competenciaValue, expiration } = JSON.parse(storedData)
            const currentDate = new Date()

            if (new Date(expiration) > currentDate) {
                // A data de expiração ainda não foi atingida, retorna o valor de competencia
                return competenciaValue
            } else {
                // A data de expiração foi atingida, remove competenciaData do localStorage
                localStorage.removeItem('@cobranca-competenciaData')
            }
        }

        return null
    }

    return (
        <S.Container>
            <S.Title>Simulador de Envio de XML</S.Title>

            <CardSliderCompetencias
                data={parseCompetencias()}
                dateFilter={anosExercicio}
                loading={loadingCompetencias}
                valueSelect={initialValueSelect}
                onChangeAnoExercicio={onChangeAnoExercicio}
                competenciaSelecionada={competenciaSelecionada}
                setCompetenciaSelecionada={handleSetCompetencia}
            />

            <ContentLoteMedico
                competenciaDisponivel={competenciaDisponivel}
                competenciaSelecionada={competenciaSelecionada}
                setForceUpdateCompetencias={forceUpdate}
            />
        </S.Container>
    )
}

export default SimulacaoTemplate
