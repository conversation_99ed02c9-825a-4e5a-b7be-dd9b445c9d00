import { HTMLAttributes } from 'react'

import { ReactSVG } from 'react-svg'
// import { CaraterAtendimentoEnum } from 'src/services/auditoria-services/censo-hospitalar/enums';
import { Badge, Wrapper } from './styles'

interface IBadge {
    type: CaraterAtendimentoEnum
}

export enum CaraterAtendimentoEnum {
    'Urgência/Emergência' = 'URGENCIA',
    'Eletivo' = 'ELETIVO'
}

const TableBadgeCarater = ({ type, ...rest }: IBadge & HTMLAttributes<HTMLDivElement>) => {
    const Icon = {
        [CaraterAtendimentoEnum?.['Urgência/Emergência']]: 'assets/icons/mai-ic-urg.svg',
        [CaraterAtendimentoEnum.Eletivo]: 'assets/icons/mai-ic-ele.svg'
    }

    const text = {
        [CaraterAtendimentoEnum?.['Urgência/Emergência']]: 'URGENCIA',
        [CaraterAtendimentoEnum.Eletivo]: 'Eletivo'
    }

    return (
        <Wrapper {...rest}>
            <Badge type={type}>
                <ReactSVG src={Icon[CaraterAtendimentoEnum?.[type]]} />
                {text[CaraterAtendimentoEnum?.[type]]}
            </Badge>
        </Wrapper>
    )
}

export default TableBadgeCarater
