import * as S from './styles'
import Button from 'components/atoms/Button'
import Modal from 'components/atoms/Modal'
import GlossReasonsList from 'components/molecules/GlossReasonsList'
import { getOr } from 'utils/fp'
import { NumberUtils } from 'utils/numberUtils'
import { useEffect, useMemo, useState } from 'react'
import { StringUtils } from 'utils/stringUtils'
import { getCurrentClient } from 'utils/parseAssets'
import { grauParticipacaoOptions, statusOptions, viaAcessoOptions } from '../CardItemGuia/utils'
import { ReactSVG } from 'react-svg'
import { IItemGuiaDetalhesDTO, IMotivosDeGlosa } from 'types/analiseContas/guia'
import { DateUtils } from 'utils/dateUtils'

type InfoItensModalProps = {
    detalhes: IItemGuiaDetalhesDTO
    calculoGlosa: IItemGuiaDetalhesDTO
    isModalOpen: boolean
    setIsModalOpen: (value: React.SetStateAction<boolean>) => void
}

const InfoItensModal = ({ detalhes, isModalOpen, setIsModalOpen, calculoGlosa }: InfoItensModalProps) => {
    const [sectionsController, setSectionsController] = useState({ calculo: true, motivoGlosaTec: false, motivoGlosaAdm: false })
    const client = useMemo(() => getCurrentClient(), [])
    const viaAcesso = useMemo(() => viaAcessoOptions.find((opt) => opt.value === calculoGlosa?.viaAcesso), [calculoGlosa])
    const grauParticipacao = useMemo(() => grauParticipacaoOptions.find((opt) => opt.value === calculoGlosa?.grauParticipacao), [calculoGlosa])

    return (
        <Modal isOpen={isModalOpen} onClose={() => setIsModalOpen(!isModalOpen)} style={{ width: '720px', paddingTop: 0, paddingBottom: 16 }}>
            <S.ModalContainer>
                <S.Row>
                    <S.Column>
                        <S.ModalTitle>Detalhes do item</S.ModalTitle>
                        <S.ModalSubtitle>{calculoGlosa?.descricao}</S.ModalSubtitle>
                    </S.Column>
                </S.Row>

                <S.Row>
                    <S.Column>
                        <S.Label>Código{client !== 'planserv' ? ' TUSS' : ''}</S.Label>
                        <S.Value>{calculoGlosa?.codigoTuss}</S.Value>
                    </S.Column>
                    <S.Column>
                        <S.Label>Tabela{client !== 'planserv' ? ' TUSS' : ''}</S.Label>
                        <S.Value>{calculoGlosa?.tabelaTuss}</S.Value>
                    </S.Column>
                    <S.Column>
                        <S.Label>Data</S.Label>
                        <S.Value>{calculoGlosa?.dataRealizacao}</S.Value>
                    </S.Column>
                </S.Row>

                <S.Row>
                    <S.Column>
                        {calculoGlosa?.grauParticipacao && (
                            <>
                                <S.Label>Grau de participação</S.Label>
                                <S.Value>{`${getOr(grauParticipacao, 'codigo', '')} - ${getOr(grauParticipacao, 'descricao', '')}`}</S.Value>
                            </>
                        )}
                    </S.Column>
                    <S.Column>
                        <S.Label>Via de acesso</S.Label>
                        <S.Value>{`${getOr(viaAcesso, 'label', '')}`}</S.Value>
                    </S.Column>
                    <S.Column>
                        <S.Label>Horário especial</S.Label>
                        <S.Value>{calculoGlosa?.horarioEspecial ? 'Sim' : 'Não'}</S.Value>
                    </S.Column>
                </S.Row>

                <S.Row>
                    <S.Column>
                        <S.Label>Quantidade apresentada</S.Label>
                        <S.Value>{calculoGlosa?.calculosDeGlosa?.quantidadeApresentada}</S.Value>
                    </S.Column>
                    <S.Column>
                        <S.Label>Valor unitário apresentado</S.Label>
                        <S.Value>{NumberUtils.currencyMask(calculoGlosa?.calculosDeGlosa?.valorUnitarioApresentado)}</S.Value>
                    </S.Column>
                    <S.Column>
                        <S.Label>Total apresentado</S.Label>
                        <S.Value>{NumberUtils.currencyMask(calculoGlosa?.calculosDeGlosa?.totalApresentado)}</S.Value>
                    </S.Column>
                </S.Row>

                {detalhes?.motivosDeGlosaTecnica?.length > 0 && (
                    <>
                        <S.Row marginBottom="16px">
                            <S.SectionTitle>Motivos de glosa técnica</S.SectionTitle>
                            <div
                                onClick={() => setSectionsController({ ...sectionsController, motivoGlosaTec: !sectionsController?.motivoGlosaTec })}
                                style={{ cursor: 'pointer' }}
                            >
                                <ReactSVG
                                    src={
                                        sectionsController?.motivoGlosaTec
                                            ? '/faturamento/assets/icons/arrow-up.svg'
                                            : '/faturamento/assets/icons/arrow-down.svg'
                                    }
                                />
                            </div>
                        </S.Row>

                        {sectionsController?.motivoGlosaTec && (
                            <>
                                {detalhes?.motivosDeGlosaTecnica?.map((item, index) => (
                                    <S.MotivoWrapper key={index}>
                                        <span style={{ fontSize: '12px' }}>
                                            Alterado por {item?.nomeUsuario} em {DateUtils.parseDateDDMMYYYY(item?.dataAtualizacao, true)}
                                        </span>

                                        <p>
                                            {item?.codigo} - {item?.descricao}
                                        </p>
                                        {item?.justificativa && <span>{item?.justificativa}</span>}
                                    </S.MotivoWrapper>
                                ))}
                            </>
                        )}
                    </>
                )}

                {detalhes?.motivosDeGlosaAdministrativa?.length > 0 && (
                    <>
                        <S.Row marginBottom="16px">
                            <S.SectionTitle>Motivos de glosa administrativa</S.SectionTitle>
                            <div
                                onClick={() => setSectionsController({ ...sectionsController, motivoGlosaAdm: !sectionsController?.motivoGlosaAdm })}
                                style={{ cursor: 'pointer' }}
                            >
                                <ReactSVG
                                    src={
                                        sectionsController?.motivoGlosaAdm
                                            ? '/faturamento/assets/icons/arrow-up.svg'
                                            : '/faturamento/assets/icons/arrow-down.svg'
                                    }
                                />
                            </div>
                        </S.Row>

                        {sectionsController?.motivoGlosaAdm && (
                            <>
                                {detalhes?.motivosDeGlosaAdministrativa?.map((item, index) => (
                                    <S.MotivoWrapper key={index}>
                                        <span style={{ fontSize: '12px' }}>
                                            Alterado por {item?.nomeUsuario} em {DateUtils.parseDateDDMMYYYY(item?.dataAtualizacao, true)}
                                        </span>

                                        <p>
                                            {item?.codigo} - {item?.descricao}
                                        </p>
                                        {item?.justificativa && <span>{item?.justificativa}</span>}
                                    </S.MotivoWrapper>
                                ))}
                            </>
                        )}
                    </>
                )}

                <S.Row marginBottom="16px" marginTop="24px">
                    <S.SectionTitle>Cálculo</S.SectionTitle>
                    <div
                        onClick={() => setSectionsController({ ...sectionsController, calculo: !sectionsController?.calculo })}
                        style={{ cursor: 'pointer' }}
                    >
                        <ReactSVG
                            src={sectionsController?.calculo ? '/faturamento/assets/icons/arrow-up.svg' : '/faturamento/assets/icons/arrow-down.svg'}
                        />
                    </div>
                </S.Row>
                {sectionsController?.calculo && (
                    <>
                        <S.ReasonItem>
                            <S.Column>
                                <S.Row marginBottom="8px">
                                    <p className="bold">Valor unitário apurado</p>
                                    <p className="bold">{NumberUtils.currencyMask(calculoGlosa?.calculosDeGlosa?.valorUnitarioApurado)}</p>
                                </S.Row>

                                <S.Row marginBottom="8px">
                                    <p className="regular">Valor do item</p>
                                    {/* <p className="regular">{calculoGlosa?.calculosDeGlosa?.valor}</p> */}
                                </S.Row>
                                <S.Row marginBottom="8px">
                                    <p className="regular">Grau de participação</p>
                                    <p className="regular">100%</p>
                                </S.Row>
                                <S.Row marginBottom="8px">
                                    <p className="regular">Via de acesso</p>
                                    <p className="regular">100%</p>
                                </S.Row>
                            </S.Column>
                        </S.ReasonItem>

                        <S.ReasonItem>
                            <S.Column>
                                <S.Row marginBottom="8px">
                                    <p className="bold">{client !== 'planserv' ? 'Valor anterior' : 'Total a pagar'}</p>
                                    <p className="bold">{NumberUtils.currencyMask(calculoGlosa?.calculosDeGlosa?.totalApresentado)}</p>
                                </S.Row>
                                <S.Row marginBottom="8px">
                                    <p className="regular">Quantidade apresentada</p>
                                    <p className="regular">{calculoGlosa?.calculosDeGlosa?.quantidadeApresentada}</p>
                                </S.Row>
                                <S.Row marginBottom="8px">
                                    <p className="regular">Quantidade glosada</p>
                                    <p className="regular">{calculoGlosa?.calculosDeGlosa?.quantidadeAGlosar}</p>
                                    {/* <p className="regular">
                                        {calculoGlosa?.quantidadeGlosada > 0 && '-'} {calculoGlosa?.calculosDeGlosa?.quantidadeGlosada}
                                    </p> */}
                                </S.Row>
                                <S.Row marginBottom="8px">
                                    <p className="regular">Valor unitário apurado</p>
                                    <p className="regular">x {NumberUtils.currencyMask(calculoGlosa?.calculosDeGlosa?.valorUnitarioApurado)}</p>
                                </S.Row>
                                <S.Row marginBottom="8px">
                                    <p className="regular">Grau de participação</p>
                                    <p className="regular">{/* {calculoGlosa?.grauParticipacao} */}x 100%</p>
                                </S.Row>
                                <S.Row marginBottom="8px">
                                    <p className="regular">Via de acesso</p>
                                    <p className="regular">x {'--'}%</p>
                                </S.Row>
                                <S.Row marginBottom="8px">
                                    <p className="regular">Horário especial (Acréscimo de 30%)</p>
                                    <p className="regular">{calculoGlosa?.horarioEspecial && 'x 130%'}</p>
                                </S.Row>
                            </S.Column>
                        </S.ReasonItem>

                        <S.ReasonItem>
                            <S.Column>
                                <S.Row marginBottom="8px">
                                    <p className="bold">Valor glosado</p>
                                    <p className="bold">{NumberUtils.currencyMask(calculoGlosa?.calculosDeGlosa?.valorGlosado)}</p>
                                </S.Row>
                                <S.Row marginBottom="8px">
                                    <p className="regular">Total apresentado</p>
                                    <p className="regular">{NumberUtils.currencyMask(calculoGlosa?.calculosDeGlosa?.totalApresentado)}</p>
                                </S.Row>
                                <S.Row marginBottom="8px">
                                    <p className="regular">Total a pagar</p>
                                    <p className="regular">{NumberUtils.currencyMask(calculoGlosa?.calculosDeGlosa?.totalAPagar)}</p>
                                </S.Row>
                            </S.Column>
                        </S.ReasonItem>
                    </>
                )}
            </S.ModalContainer>

            <S.Row justifyContent="flex-end" marginBottom="0" marginTop="32px">
                <Button typeButton="ghost" themeButton="gray" style={{ width: 'max-content' }} onClick={() => setIsModalOpen(false)}>
                    Fechar
                </Button>
            </S.Row>
        </Modal>
    )
}

export default InfoItensModal
