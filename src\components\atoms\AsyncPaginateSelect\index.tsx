import React, { useState } from 'react'
import theme from 'styles/theme'
import { ActionMeta, CSSObjectWithLabel, GroupBase, components } from 'react-select'
import * as S from './styles'
import { FilterOptionOption } from 'react-select/dist/declarations/src/filters'
import { AsyncPaginate, LoadOptions } from 'react-select-async-paginate'
import { IElements, IRetorno } from './functions'
interface AsyncSimpleSelectProps<T = any> {
    defaultValue?: IElements<T, T[keyof T], T[keyof T]>
    onChange?: (newValue: IElements<T, T[keyof T], T[keyof T]>, actionMeta: ActionMeta<T>) => void
    loadOptions: (value: string, prev: T, { page }: { page: number }) => Promise<IRetorno<T, T[keyof T], T[keyof T]>>
    value?: IElements<T, T[keyof T], T[keyof T]>
    label?: string
    required?: boolean
    isClearable?: boolean
    disabled?: boolean
    defaultOptions?: boolean
    isMulti?: boolean
    filterOption?: (option: FilterOptionOption<T>, inputValue: string) => boolean
    pageSize?: number
    debounceTimeout?: number
    additional?: AdditionalPage
    isSearchable?: boolean
    error?: boolean
}

interface AdditionalPage {
    page: number
}

export default function AsyncPaginateSelect({
    loadOptions,
    value,
    defaultValue,
    onChange,
    label,
    isClearable,
    required,
    disabled,
    isMulti,
    filterOption,
    error,
    defaultOptions = true,
    pageSize = 10,
    debounceTimeout = 1500,
    additional = { page: 0 },
    isSearchable = true,
    ...rest
}: AsyncSimpleSelectProps) {
    const [isFocused, setIsFocused] = useState(false)

    const { NoOptionsMessage } = components
    const noOptionsMessage: typeof NoOptionsMessage = (props) => {
        return (
            <NoOptionsMessage {...props}>
                <span className="custom-css-class">Sem opções</span>
            </NoOptionsMessage>
        )
    }

    return (
        <S.Wrapper isFocused={true} focus={isFocused} disabled={disabled}>
            <label style={error ? { color: '#f00' } : {}}>
                {label}
                {required && <span> *</span>}
            </label>
            <AsyncPaginate
                {...rest}
                styles={{
                    control: (provider, state): CSSObjectWithLabel => ({
                        display: 'flex',
                        flexDirection: 'row',
                        borderRadius: '0.4rem',
                        fontSize: '16px',
                        height: '5.6rem',
                        padding: '0 .6rem',
                        border: error ? '2px solid' : state.isFocused ? '2px solid' : '1px solid',
                        borderColor: error ? `#f00` : state.isFocused ? `${theme.colors.primary['500']}` : `${theme.colors.black[16]}`
                    }),
                    menu: (provider): CSSObjectWithLabel => ({
                        ...provider,
                        zIndex: '5'
                    })
                }}
                instanceId="postType"
                components={{ NoOptionsMessage: noOptionsMessage }}
                placeholder={''}
                defaultValue={defaultValue}
                value={value}
                onChange={onChange}
                loadOptions={
                    loadOptions as unknown as LoadOptions<
                        any,
                        GroupBase<any>,
                        {
                            page: number
                        }
                    >
                }
                isClearable={isClearable}
                isMulti={isMulti}
                onFocus={() => {
                    setIsFocused(true)
                }}
                isDisabled={disabled}
                onBlur={() => {
                    setIsFocused(false)
                }}
                debounceTimeout={debounceTimeout}
                maxMenuHeight={20 * pageSize}
                pageSize={pageSize}
                additional={additional}
                isSearchable={isSearchable}
                defaultOptions={defaultOptions}
                filterOption={filterOption}
            />
        </S.Wrapper>
    )
}
