import { ICalendarioHistoricoAlteracaoDTO } from 'src/services/analiseContasApi/Calendario/types'

export const historyData: ICalendarioHistoricoAlteracaoDTO[] = [
    {
        dataHoraAlteracao: '06/12/2021 16:32:10',
        nomeUsuario: '<PERSON> Euclides da Costa',
        tipoAlteracao: 'Deletou uma regra',
        entidade: ''
    },
    {
        dataHoraAlteracao: '06/12/2021 16:32:10',
        nomeUsuario: '<PERSON> E<PERSON>lide<PERSON>',
        tipoAlteracao: 'Incluiu uma nova regra',
        entidade: ''
    },
    {
        dataHoraAlteracao: '06/12/2021 16:32:10',
        nomeUsuario: '<PERSON>',
        tipoAlteracao: 'Ativou uma regra',
        entidade: ''
    },
    {
        dataHoraAlteracao: '06/12/2021 16:32:10',
        nomeUsuario: '<PERSON>',
        tipoAlteracao: 'Desativou uma regra',
        entidade: ''
    }
]
