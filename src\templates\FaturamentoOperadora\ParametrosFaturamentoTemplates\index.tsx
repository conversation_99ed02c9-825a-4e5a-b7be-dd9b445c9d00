import React from 'react'
import Layout from 'components/molecules/Layout'
import CardSubMenu from 'components/molecules/CardSubMenu'
import * as S from './styles'

const ParametrosFaturamentoTemplates = () => {
    return (
        <S.Container>
            <S.CardFlex>
                {/* <CardSubMenu
                    style={{ width: '296px' }}
                    titleStyle={{ fontSize: '16px' }}
                    title="Regras de Excludências"
                    image="/faturamento/assets/icons/list.svg"
                    // description="Lorem Ipsulon Lorem Ipsulon Lorem Ipsulon Lorem Ipsulon Lorem Ipsulon"
                    link="parametros/regras-de-excludencia"
                /> */}
                <CardSubMenu
                    style={{ width: '296px' }}
                    titleStyle={{ fontSize: '16px' }}
                    title="Processador de regras"
                    image="/faturamento/assets/icons/list.svg"
                    // description="Lorem Ipsulon Lorem Ipsulon Lorem Ipsulon Lorem Ipsulon Lorem Ipsulon"
                    link="parametros/processador-de-regras"
                />
            </S.CardFlex>
        </S.Container>
    )
}

export default ParametrosFaturamentoTemplates
