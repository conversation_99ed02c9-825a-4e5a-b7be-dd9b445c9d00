import * as S from './styles'

import React, { useEffect, useState, useCallback } from 'react'
import { useRouter } from 'next/router'
import Item from 'components/atoms/Item'
import Modal from 'components/atoms/Modal'
import Button from 'components/atoms/Button'
import SearchBar from 'components/molecules/SearchBar'
import TitleSection from 'components/atoms/TitleSection'
import ButtonFilter from 'components/molecules/ButtonFilter'
import ContentDataInfo from 'components/molecules/ShowDataConfirm'
import DividerSectionCard from 'components/atoms/DividerSectionCard'
import Pagination from 'components/molecules/Pagination'
import { IPagination } from 'types/common/pagination'
import { AnaliseContasService } from 'src/services/analiseContasApi/analiseContasServices'
import { NumberUtils } from 'utils/numberUtils'
import { IGetPropsGuiaLoteAnalise, IGuiaLoteQuery, IPageGuiaLote } from 'types/analiseContas/guiasLote'
import { useToast } from 'src/hooks/toast'
import { ILoteValoresQuery } from 'types/analiseContas/lotes'
import { ILoteDTO } from 'types/analiseContas/lote'
import { PaginationHelper } from 'utils/helpers/paginationHelper'
import { DateUtils } from 'utils/dateUtils'
import { SituacaoGuiaQuery } from 'types/analiseContas/guia'
import { capitalize, getMessageErrorFromApiResponse } from 'utils/stringUtils'
import { IGlossReason } from 'components/molecules/ModalAddGlossReasons/gloss-reason'
import NoContent from 'components/molecules/NoContent'
import ModalAddGlossReasons from 'components/molecules/ModalAddGlossReasons'
import TableRowIconButton from 'components/atoms/TableRowIconButton'
import Badge from 'components/atoms/Badge'
import { ReactSVG } from 'react-svg'
import NewBadge from 'components/molecules/NewBadge'
import { Button as ButtonMui, Checkbox, FormControl, FormControlLabel, InputLabel, MenuItem, Select } from '@mui/material'
import { ISortPage } from 'types/pagination'
import dynamic from 'next/dynamic'

const AnimatedLoadingLottie = dynamic(() => import('components/atoms/AnimatedLoadingLottie'), { ssr: false })

export interface IResumoLoteProps {
    idLote: any
    competencia: string
}

export type guiaChecked = {
    index: number
    checked: boolean
    guia: string
}

type newFilterType = {
    tipoProcesso: 'TODOS' | 'REGULAR' | 'LIMINAR' | 'EXCEPCIONALIDADE'
    guiasSemGlosa: boolean
    guiasComGlosa: boolean
}

const ResumoLoteTemplate = ({ idLote, competencia }: IResumoLoteProps) => {
    const { addToast } = useToast()

    const route = useRouter()
    const idLotes = idLote.split()

    const [status, setStatus] = useState('EM_ANALISE_TECNICA')
    const [isModalGlosarLoteOpen, setIsModalGlosarLoteOpen] = useState(false)
    const [isModalGlosarGuiaOpen, setIsModalGlosarGuiaOpen] = useState(false)
    const [isModalReabrirOpen, setIsModalReabrirOpen] = useState(false)
    const [resumolote, setResumoLote] = useState<ILoteValoresQuery>()
    const [situacoes, setSituacoes] = useState<SituacaoGuiaQuery[]>([])
    const [filterFields, setFilterFields] = useState([])
    const [lote, setLote] = useState<ILoteDTO>()

    const [pageGuias, setPageGuias] = useState<IPageGuiaLote>()
    const [pagination, setPagination] = useState<IPagination>()
    const [numberPage, setNumberPage] = useState<number>(0)
    const [filtro, setFiltro] = useState<string>()
    const [showFilter, setShowFilter] = useState(false)
    const [newFilter, setNewFilter] = useState<newFilterType>({
        tipoProcesso: 'TODOS',
        guiasComGlosa: false,
        guiasSemGlosa: false
    })

    const [checkedAll, setCheckedAll] = useState(false)
    const [guiaChecked, setGuiaChecked] = useState<guiaChecked[]>([])
    const [guiasChecadas, setGuiasChecadas] = useState<IGuiaLoteQuery[]>()
    const [isOpenConfirmarFinalizarLote, setIsOpenConfirmarFinalizarLote] = useState(false)
    const [loadingGuides, setLoadingGuides] = useState(false)
    const [lotDocumentExists, setLotDocumentExists] = useState(false)
    const [lotFinishInProgress, setLotFinishInProgress] = useState(false)
    const [reabrirGuiasInProgress, setReabrirGuiasInProgress] = useState(false)
    const [reabrirLoteInProgress, setReabrirLoteInProgress] = useState(false)

    const carregarGuias = useCallback(
        (page?: number, filter?: string, newFilter?: newFilterType) => {
            if (!status) return
            const getProps: IGetPropsGuiaLoteAnalise & ISortPage = {
                idLote: idLote,
                filtroNumeroGuia: filter,
                tipoProcesso: newFilter?.tipoProcesso === 'TODOS' ? null : newFilter?.tipoProcesso,
                guiasSemGlosa: newFilter?.guiasSemGlosa,
                guiasComGlosa: newFilter?.guiasComGlosa,
                situacao: status,
                size: 10,
                page: page || 0
            }

            setLoadingGuides(true)

            AnaliseContasService.getGuiasLote(getProps)
                .then(({ data }) => {
                    setPageGuias(data)
                    const objectPagination = PaginationHelper.parserPagination<IGuiaLoteQuery>(data, setNumberPage)
                    setPagination(objectPagination)

                    setNumberPage(objectPagination.paginaAtual)
                })
                .catch(() => {
                    addToast({ type: 'error', title: 'Erro ao buscar guias', duration: 4000 })
                })
                .finally(() => setLoadingGuides(false))
        },
        [status]
    )

    useEffect(() => {
        !loadingGuides && carregarGuias(numberPage, filtro, newFilter)
    }, [numberPage])

    useEffect(() => {
        setFiltro('')
        !loadingGuides && carregarGuias()
    }, [status])

    const loadPageData = useCallback(() => {
        carregarGuias()

        AnaliseContasService.getResumoLote(idLote, competencia)
            .then(({ data }) => {
                setResumoLote(data)
            })
            .catch(() => {
                addToast({ type: 'error', title: 'Erro ao buscar o resumo do lote' })
            })
        AnaliseContasService.getSituacoesGuiasLote(idLote)
            .then(({ data }) => setSituacoes(data))
            .catch(() => {
                addToast({ type: 'error', title: 'Erro ao buscar as situações das guias do lote' })
            })
        AnaliseContasService.getLote(idLote)
            .then(({ data }) => setLote(data))
            .catch(() => {
                addToast({ type: 'error', title: 'Erro ao buscar o lote' })
            })
    }, [addToast, setSituacoes, setResumoLote, carregarGuias])

    useEffect(loadPageData, [])

    const getItemSituacao = (situacao: string) => {
        return situacoes?.some((i) => i.valor === situacao) ? situacoes?.find((i) => i.valor === situacao) : { porcentagem: 0, quantidadeDeGuias: 0 }
    }

    useEffect(() => {
        if (!situacoes) return

        const { porcentagem: percEmAnaliseTec, quantidadeDeGuias: qtdAnaliseTec } = getItemSituacao('ABERTA_EM_ANALISE_TECNICA')
        const { porcentagem: percEmAnaliseAdm, quantidadeDeGuias: qtdAnaliseAdm } = getItemSituacao('ABERTA_EM_ANALISE_ADM')
        const { porcentagem: percEmAnalisada, quantidadeDeGuias: qtdAnalisada } = getItemSituacao('ANALISADA')

        setFilterFields([
            { name: 'Análise técnica', value: `${qtdAnaliseTec || '0'} - (${percEmAnaliseTec || '0'}%)`, situacao: 'EM_ANALISE_TECNICA' },
            { name: 'Análise administrativa', value: `${qtdAnaliseAdm || '0'} - (${percEmAnaliseAdm || '0'}%)`, situacao: 'EM_ANALISE_ADM' },
            {
                name: 'Analisadas',
                value: `${qtdAnalisada || '0'} - (${percEmAnalisada || '0'}%)`,
                situacao: 'FECHADA'
            }
        ])
    }, [situacoes])

    const handleFinalizar = useCallback(() => {
        setLotFinishInProgress(true)

        AnaliseContasService.patchFinalizarLote(idLote)
            .then(() => {
                addToast({ type: 'success', title: 'Lote finalizado com sucesso' })
                setIsOpenConfirmarFinalizarLote(false)
                loadPageData()
            })
            .catch(() => {
                addToast({ type: 'error', title: 'Erro ao finalizar lote' })
            })
            .finally(() => {
                setLotFinishInProgress(false)
            })
    }, [addToast, idLote, route, loadPageData])

    const handleGlosarLote = useCallback(
        (reasons: IGlossReason[]) => {
            const data = {
                lotes: idLotes,
                motivosGlosa: reasons.map((r) => ({ motivoGlosa: r.motivoGlosaUUID, justificativaGlosa: r.justificativaGlosa }))
            }
            return AnaliseContasService.patchGlosarLotes(data)
                .then(() => {
                    addToast({ type: 'success', title: 'Lote glosado com sucesso' })
                    setIsModalGlosarLoteOpen(false)
                    loadPageData()
                })
                .catch(() => {
                    addToast({ type: 'error', title: 'Erro ao glosar lote' })
                })
        },
        [idLotes, addToast, setIsModalGlosarLoteOpen, loadPageData]
    )

    const handlePesquisar = () => {
        carregarGuias(0, filtro, newFilter)
    }

    const handleReabrir = useCallback(() => {
        const props = {
            competencia: competencia,
            prestadorId: lote?.prestadorId
        }

        setReabrirLoteInProgress(true)

        AnaliseContasService.patchReabrirLote(idLotes, props)
            .then(() => {
                addToast({ type: 'success', title: 'Lote reaberto com sucesso' })
                loadPageData()
            })
            .catch(() => {
                addToast({ type: 'error', title: 'Erro ao reabrir lote' })
            })
            .finally(() => {
                setReabrirLoteInProgress(false)
            })
    }, [competencia, lote, idLotes, addToast, loadPageData])

    const handleGlosarGuia = useCallback(
        (reasons: IGlossReason[]) => {
            const data = reasons.map((r) => ({ justificativaGlosa: r.justificativaGlosa, motivoGlosa: r.motivoGlosaUUID }))
            const guiasSelecionadas = guiaChecked.filter((item) => item.checked).map((i) => i.guia)

            let request: Promise<any>

            if (guiasSelecionadas.length === 1) {
                request = AnaliseContasService.patchGlosarGuiaTotal(guiasSelecionadas[0].toString(), data)
            } else {
                request = AnaliseContasService.patchGlosarGuiasTotal({
                    guias: guiasSelecionadas,
                    motivosGlosa: data
                })
            }

            return request
                .then(() => {
                    addToast({ type: 'success', title: 'Guias glosadas com sucesso' })
                    setIsModalGlosarGuiaOpen(false)
                    loadPageData()
                })
                .catch(() => {
                    addToast({ type: 'error', title: 'Erro ao glosar guia' })
                })
        },
        [loadPageData, setIsModalGlosarGuiaOpen, addToast, guiaChecked]
    )

    const handleOnClosePesquisa = () => {
        setFiltro('')
        carregarGuias(numberPage, '', newFilter)
    }

    useEffect(() => {
        initGuiaCheckeds(pageGuias?.content)
        setPageGuias(pageGuias)
    }, [pageGuias])

    const initGuiaCheckeds = (data) => {
        const guiasCheckeds: guiaChecked[] = []
        data?.forEach((item, index) => {
            guiasCheckeds.push({
                index,
                checked: false,
                guia: item?.idGuia?.toString()
            })
        })
        setGuiaChecked(guiasCheckeds)
    }

    useEffect(() => {
        if (!pageGuias?.content) return

        setCheckedAll(guiaChecked.filter((i) => i.checked).length === pageGuias?.content?.length)
    }, [guiaChecked, pageGuias])

    useEffect(() => {
        setGuiasChecadas(
            pageGuias?.content?.filter((guia, index) => {
                if (guiaChecked[index]?.checked && guiaChecked[index]) return guia
            })
        )
    }, [guiaChecked])

    function checkAll() {
        setGuiaChecked(guiaChecked.map((item) => ({ ...item, checked: !checkedAll })))
    }

    const handleReabrirGuias = useCallback(() => {
        const data = guiaChecked.filter((item) => item.checked).map((item) => item.guia)

        setReabrirGuiasInProgress(true)

        AnaliseContasService.patchReabrirGuias(data)
            .then(() => {
                addToast({ type: 'success', title: 'Guia(s) reaberta(s) com sucesso' })
                loadPageData()
            })
            .catch((err) => {
                addToast({
                    type: 'error',
                    title: 'Erro ao reabrir a(s) guia(s)',
                    description: getMessageErrorFromApiResponse(err)
                })
            })
            .finally(() => {
                setIsModalReabrirOpen(false)
                setReabrirGuiasInProgress(false)
            })
    }, [loadPageData, setIsModalReabrirOpen, guiaChecked])

    const handleDownloadLotDocument = useCallback((idLote: string) => {
        AnaliseContasService.getDocumentoLoteDownload(idLote, 'DOCUMENTACAO_DIVERSA')
            .then((response) => {
                const fileName = response.headers['content-disposition'].split('filename=')[1]
                const url = window.URL.createObjectURL(response.data)
                const link = document.createElement('a')
                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
            })
            .catch((err) => {
                addToast({
                    type: 'error',
                    title: 'Erro ao tentar baixar o documento',
                    duration: 5000,
                    description: getMessageErrorFromApiResponse(err)
                })
            })
    }, [])

    const checkIfLotDocumentExists = useCallback(async (idLote: string) => {
        try {
            await AnaliseContasService.getDocumentoLoteDownload(idLote, 'DOCUMENTACAO_DIVERSA')
            setLotDocumentExists(true)
        } catch (err) {
            setLotDocumentExists(false)
        }
    }, [])

    useEffect(() => {
        idLote && checkIfLotDocumentExists(idLote)
    }, [idLote])

    useEffect(() => {
        setNewFilter({ tipoProcesso: 'TODOS', guiasComGlosa: false, guiasSemGlosa: false })
    }, [status])

    return (
        <S.Content>
            <S.MainHeader>
                <S.TitleContainer>
                    <S.Title>{`Lote - ${lote?.numeroLote || ''}`}</S.Title>
                    <S.SubTitle>{capitalize(lote?.nomePrestador)}</S.SubTitle>
                </S.TitleContainer>
                <S.ActionsContainer>
                    {lote?.situacao == 'FECHADO' ? (
                        <Button
                            typeButton="text"
                            themeButton="ihealth"
                            onClick={handleReabrir}
                            style={{ width: 'fit-content' }}
                            iconLeft="/faturamento/assets/icons/refresh.svg"
                            disabled={reabrirLoteInProgress}
                        >
                            Reabrir
                        </Button>
                    ) : lote?.situacao === 'EM ANALISE' ? (
                        <>
                            <Button
                                typeButton="text"
                                themeButton="ihealth"
                                style={{ width: 'fit-content', whiteSpace: 'nowrap' }}
                                onClick={() => setIsModalGlosarLoteOpen(true)}
                            >
                                Glosar lote
                            </Button>
                            <Button
                                themeButton="warning"
                                onClick={() => setIsOpenConfirmarFinalizarLote(true)}
                                disabled={situacoes?.find((i) => i.valor === 'ANALISADA')?.porcentagem < 100}
                            >
                                Fechar lote
                            </Button>
                        </>
                    ) : null}
                </S.ActionsContainer>
            </S.MainHeader>
            <DividerSectionCard>
                <TitleSection>Informações gerais</TitleSection>
                <S.Header>
                    <S.ContentData>
                        <ContentDataInfo
                            background="grey"
                            standart="expanded"
                            className="center-item"
                            label="Mês de competência"
                            value={DateUtils.getMonthFullName(competencia)}
                        />
                        <ContentDataInfo className="center-item" label="Tipo do lote" value={lote?.tipoLote} background="grey" standart="expanded" />
                        <ContentDataInfo
                            background="grey"
                            label="Documentos"
                            standart="expanded"
                            className="center-item"
                            value={lotDocumentExists ? 'Baixar' : '---'}
                            themeColor={lotDocumentExists ? 'blue' : 'default'}
                            onClick={() => lotDocumentExists && handleDownloadLotDocument(idLote)}
                        />
                    </S.ContentData>
                </S.Header>
                <TitleSection style={{ paddingTop: '24px' }}>Valores</TitleSection>
                <S.Header>
                    <S.ContentData>
                        <ContentDataInfo
                            className="center-item"
                            label={'Apresentado'}
                            value={NumberUtils.maskMoney(resumolote?.valorApresentado)}
                            background={'grey'}
                            standart={'expanded'}
                        />
                        <ContentDataInfo
                            className="center-item"
                            label={'Apurado'}
                            value={NumberUtils.maskMoney(resumolote?.valorApurado)}
                            background={'grey'}
                            standart={'expanded'}
                        />
                        <ContentDataInfo
                            className="center-item"
                            label={'Glosado'}
                            value={NumberUtils.maskMoney(resumolote?.valorGlosado)}
                            background={'grey'}
                            standart={'expanded'}
                        />
                    </S.ContentData>
                </S.Header>
                {/* <TitleSection style={{ paddingTop: '24px' }}>Nota</TitleSection>
                <S.Header>
                    <S.ContentData>
                        <ContentDataInfo
                            className="center-item"
                            label={'Número'}
                            value={!resumolote?.numeroNotaFiscal ? 'Não enviado' : resumolote?.numeroNotaFiscal}
                            background={'grey'}
                            standart={'expanded'}
                        />
                        <ContentDataInfo
                            className="center-item"
                            label={'Valor'}
                            value={!resumolote?.valorNotaFiscal ? 'Não enviado' : NumberUtils.maskMoney(resumolote?.valorNotaFiscal)}
                            background={'grey'}
                            standart={'expanded'}
                        />
                        <ContentDataInfo
                            className="center-item"
                            label={'Arquivo'}
                            themeColor={!resumolote?.nomeArquivoNotaFiscal ? 'default' : 'blue'}
                            value={!resumolote?.nomeArquivoNotaFiscal ? 'Não enviado' : resumolote?.nomeArquivoNotaFiscal}
                            background={'grey'}
                            standart={'expanded'}
                        />
                        <ContentDataInfo
                            className="center-item"
                            label={'Número da caixa'}
                            value={!resumolote?.numeroCaixa ? 'Número não encontrado' : resumolote?.numeroCaixa}
                            background={'grey'}
                            standart={'expanded'}
                        />
                    </S.ContentData>
                </S.Header> */}
            </DividerSectionCard>
            <DividerSectionCard dividerContent={true}>
                <S.HeaderList>
                    <SearchBar
                        value={filtro || ''}
                        handleOnSearch={handlePesquisar}
                        handleOnClose={handleOnClosePesquisa}
                        placeholder="Procure pelo número da guia"
                        handleOnChange={(e) => {
                            setFiltro(e?.target?.value)
                            if (e?.target?.value === '') {
                                return handleOnClosePesquisa()
                            }
                        }}
                    />
                    <S.FilterButtonWrapper onClick={() => setShowFilter(!showFilter)}>
                        <ReactSVG src="/faturamento/assets/icons/filter.svg" />
                        <p>Filtros</p>
                    </S.FilterButtonWrapper>
                </S.HeaderList>
                {showFilter && (
                    <S.FilterSelectWrapper>
                        <div className="row1">
                            <FormControl>
                                <InputLabel>Tipo de processo</InputLabel>
                                <Select
                                    defaultValue="TODOS"
                                    value={newFilter?.tipoProcesso}
                                    onChange={(e) => {
                                        setNewFilter({
                                            ...newFilter,
                                            tipoProcesso: e.target.value as 'TODOS' | 'REGULAR' | 'LIMINAR' | 'EXCEPCIONALIDADE'
                                        })
                                    }}
                                >
                                    <MenuItem value="TODOS">Todos</MenuItem>
                                    <MenuItem value="REGULAR">Regular</MenuItem>
                                    <MenuItem value="LIMINAR">Liminar</MenuItem>
                                    <MenuItem value="EXCEPCIONALIDADE">Excepcionalidade</MenuItem>
                                </Select>
                            </FormControl>
                            <div>
                                <FormControlLabel
                                    control={
                                        <Checkbox
                                            checked={newFilter?.guiasSemGlosa}
                                            onChange={({ target }) => {
                                                setNewFilter({ ...newFilter, guiasComGlosa: false, guiasSemGlosa: target.checked })
                                            }}
                                        />
                                    }
                                    label={<S.LabelCheckbox>Guias sem glosa</S.LabelCheckbox>}
                                />
                                <FormControlLabel
                                    control={
                                        <Checkbox
                                            checked={newFilter?.guiasComGlosa}
                                            onChange={({ target }) => {
                                                setNewFilter({ ...newFilter, guiasComGlosa: target.checked, guiasSemGlosa: false })
                                            }}
                                        />
                                    }
                                    label={<S.LabelCheckbox>Guias com glosa</S.LabelCheckbox>}
                                />
                            </div>
                        </div>
                        <div className="row2">
                            <ButtonMui
                                color="neutral"
                                variant="text"
                                onClick={() => setNewFilter({ tipoProcesso: 'TODOS', guiasComGlosa: false, guiasSemGlosa: false })}
                            >
                                Limpar
                            </ButtonMui>
                            <ButtonMui
                                color="primary"
                                variant="outlined"
                                onClick={() => {
                                    handlePesquisar()
                                }}
                            >
                                Filtrar
                            </ButtonMui>
                        </div>
                    </S.FilterSelectWrapper>
                )}

                <ButtonFilter setStatus={setStatus} filter={true} data={filterFields} disable={loadingGuides} setNumberPage={setNumberPage} />

                {loadingGuides ? (
                    <AnimatedLoadingLottie style={{ width: '240px', margin: 'auto' }} />
                ) : pageGuias?.content?.length === 0 ? (
                    <div style={{ marginTop: '20px' }}>
                        <NoContent title="Não há guias por enquanto" />
                    </div>
                ) : (
                    <>
                        <S.ContentItemResult>
                            {lote?.situacao == 'PENDENTE' ? null : (
                                <S.CheckBoxContainer>
                                    <input
                                        type="checkbox"
                                        checked={checkedAll}
                                        onChange={() => {
                                            checkAll()
                                        }}
                                    />
                                </S.CheckBoxContainer>
                            )}

                            {guiaChecked?.some((i) => i.checked) && ['EM_ANALISE_TECNICA', 'EM_ANALISE_ADM'].includes(status) ? (
                                <Button
                                    typeButton="ghost"
                                    themeButton="gray"
                                    style={{ whiteSpace: 'nowrap' }}
                                    onClick={() => setIsModalGlosarGuiaOpen(true)}
                                >
                                    Glosar guias
                                </Button>
                            ) : guiaChecked?.some((i) => i.checked) && status === 'FECHADA' ? (
                                <S.GuidesCheckedsAction>
                                    <span className="lotsCheckedsQtd">
                                        {`${guiaChecked.filter((i) => i.checked).length} ${
                                            guiaChecked.filter((i) => i.checked).length === 1 ? 'Selecionada' : 'Selecionadas'
                                        }`}
                                    </span>
                                    <Button
                                        typeButton="ghost"
                                        themeButton="gray"
                                        style={{ whiteSpace: 'nowrap' }}
                                        onClick={() => setIsModalReabrirOpen(true)}
                                    >
                                        Reabrir guias
                                    </Button>
                                </S.GuidesCheckedsAction>
                            ) : (
                                <>
                                    <Item>
                                        <p>Guias</p>
                                    </Item>
                                    <Item>
                                        <p>Beneficiário</p>
                                    </Item>
                                    <Item>
                                        <p>Tipo de processo</p>
                                    </Item>
                                    <Item>
                                        <p>Apresentado</p>
                                    </Item>
                                    <Item>
                                        <p>Glosado</p>
                                    </Item>
                                    <Item>
                                        <p>Apurado</p>
                                    </Item>
                                    <Item>
                                        <p>{''}</p>
                                    </Item>
                                </>
                            )}
                        </S.ContentItemResult>

                        {pageGuias?.content?.map((obj, index) => (
                            <S.ContentItemResultGlosa key={index} checked={guiaChecked[index]?.checked}>
                                <Item>
                                    <S.CheckBoxContainer>
                                        <input
                                            type="checkbox"
                                            checked={guiaChecked[index]?.checked}
                                            onChange={() => {
                                                const newGuiaCheckeds = [...guiaChecked]

                                                if (!newGuiaCheckeds) return

                                                if (!newGuiaCheckeds[index]) return

                                                newGuiaCheckeds[index].checked = !newGuiaCheckeds[index]?.checked
                                                setGuiaChecked(newGuiaCheckeds)
                                            }}
                                        />
                                    </S.CheckBoxContainer>
                                </Item>

                                <Item>
                                    <span>{obj.numeroGuia}</span>
                                </Item>
                                <Item>
                                    <span>{capitalize(obj.nomeBeneficiario)}</span>
                                </Item>
                                <Item>
                                    {obj.tipoProcesso && <NewBadge type={obj.tipoProcesso} />}

                                    {/* <NewBadge type={'EXCEPCIONALIDADE'} /> */}
                                </Item>
                                <Item>
                                    <span>{NumberUtils.maskMoney(obj.valorApresentado)}</span>
                                </Item>
                                <Item>
                                    <span>{NumberUtils.maskMoney(obj.valorGlosado)}</span>
                                </Item>
                                <Item>
                                    <span>{NumberUtils.maskMoney(obj.valorApurado)}</span>
                                </Item>
                                <Item>
                                    <S.IconsGroupWrapper>
                                        {status === 'FECHADA' && (
                                            <TableRowIconButton
                                                title="Reabrir"
                                                style={{ marginLeft: 'auto' }}
                                                onClick={() => {
                                                    setGuiaChecked([
                                                        {
                                                            index: index,
                                                            checked: true,
                                                            guia: obj.idGuia
                                                        }
                                                    ])
                                                    setIsModalReabrirOpen(true)
                                                }}
                                                icon="/faturamento/assets/icons/refresh.svg"
                                            />
                                        )}
                                        <TableRowIconButton
                                            title="Ver detalhes"
                                            style={{ marginLeft: 'auto' }}
                                            icon="/faturamento/assets/icons/mai-ic-visible-true.svg"
                                            onClick={() => route.push(`/processamento-contas/analise-contas/resumo-guia/${obj?.idGuia}`)}
                                        />
                                    </S.IconsGroupWrapper>
                                </Item>
                            </S.ContentItemResultGlosa>
                        ))}

                        <S.PaginationContainer>
                            <Pagination
                                totalPage={pagination?.totalPaginas}
                                actualPage={pagination?.paginaAtual}
                                setNumberPage={pagination?.setNumberPage}
                                totalRegister={pagination?.totalRegistros}
                            />
                            <S.Page>
                                <span className="pageNumber">
                                    {pagination?.paginaAtual * pagination?.linhasPorPagina + 1} -{' '}
                                    {pageGuias?.content?.length + pagination?.paginaAtual * pagination?.linhasPorPagina}
                                    {' de '}
                                    {pagination?.totalRegistros}
                                </span>
                            </S.Page>
                        </S.PaginationContainer>
                    </>
                )}
            </DividerSectionCard>

            <ModalAddGlossReasons
                isOpen={isModalGlosarLoteOpen}
                onClose={() => {
                    setIsModalGlosarLoteOpen(!isModalGlosarLoteOpen)
                }}
                onConfirm={handleGlosarLote}
            />
            <ModalAddGlossReasons
                isOpen={isModalGlosarGuiaOpen}
                onClose={() => {
                    setIsModalGlosarGuiaOpen(!isModalGlosarGuiaOpen)
                }}
                onConfirm={handleGlosarGuia}
            />

            <Modal
                isOpen={isModalReabrirOpen}
                style={{ width: '35vw', padding: '24px' }}
                onClose={() => {
                    setIsModalReabrirOpen(!isModalReabrirOpen)
                }}
            >
                <S.ModalContainer>
                    <S.ModalTitle>Reabrir guia(s)</S.ModalTitle>

                    <p>Deseja reabrir a(s) guia(s) selecionada(s)?</p>
                    <S.ContainerButtons>
                        <Button
                            typeButton="text"
                            themeButton="gray"
                            style={{ width: 'fit-content' }}
                            onClick={() => {
                                setIsModalReabrirOpen(false)
                            }}
                        >
                            Cancelar
                        </Button>
                        <Button
                            themeButton="warning"
                            className="btn-cancelar-guia"
                            style={{ width: 'fit-content' }}
                            onClick={() => {
                                handleReabrirGuias()
                            }}
                            disabled={reabrirGuiasInProgress}
                        >
                            Confirmar
                        </Button>
                    </S.ContainerButtons>
                </S.ModalContainer>
            </Modal>

            <Modal
                isOpen={isOpenConfirmarFinalizarLote}
                style={{ width: '50vw', padding: '24px' }}
                onClose={() => {
                    setIsOpenConfirmarFinalizarLote(!isOpenConfirmarFinalizarLote)
                }}
            >
                <S.ModalContainer>
                    <S.ModalTitle>Fechar o lote - {lote?.numeroLote}?</S.ModalTitle>
                    <p>Ao fechar o lote, ainda será possível reabrir o lote se a competência e o lote não estiverem fechadas.</p>
                    <S.ContainerButtons style={{ marginTop: '16px' }}>
                        <Button
                            typeButton="text"
                            style={{
                                backgroundColor: 'transparent',
                                borderColor: 'gray',
                                width: '35%',
                                color: '#0000008f'
                            }}
                            onClick={() => setIsOpenConfirmarFinalizarLote(false)}
                        >
                            Cancelar
                        </Button>
                        <Button
                            className="btn-cancelar-guia"
                            themeButton="warning"
                            style={{
                                width: '30%'
                            }}
                            disabled={lotFinishInProgress}
                            onClick={handleFinalizar}
                        >
                            Fechar
                        </Button>
                    </S.ContainerButtons>
                </S.ModalContainer>
            </Modal>
        </S.Content>
    )
}

export default ResumoLoteTemplate
