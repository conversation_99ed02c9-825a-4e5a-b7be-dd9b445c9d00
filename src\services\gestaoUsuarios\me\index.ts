import { ScopeResponseDTO, ProfileResponseDTO, ContextResponseDTO, ResourceResponseDTO, MeResponseDTO } from 'types/me'

import { apiGestao } from 'src/services/apis/gestaoUsuarios'
import { ObjectUtils } from 'utils/objectUtils'

const baseUrl = '/me'

export class Me {
    static async get(props?: any) {
        const params = ObjectUtils.propsToParams(props)
        const url = props ? `${baseUrl}?${params}` : baseUrl
        return apiGestao.get<MeResponseDTO>(url)
    }

    static async getScope(scopeName: string) {
        return await apiGestao.get<ScopeResponseDTO[]>(`${baseUrl}/${scopeName}`)
    }

    static async getProfile() {
        return await apiGestao.get<ProfileResponseDTO>(`${baseUrl}/profile`)
    }

    static async getContext() {
        return await apiGestao.get<ContextResponseDTO[]>(`${baseUrl}/context`)
    }

    static async getScopesByContextName(contextName: string) {
        return await apiGestao.get<ScopeResponseDTO[]>(`${baseUrl}/context/${contextName}`)
    }

    static async getResourcesByScopeName(scopeName: string) {
        return await apiGestao.get<ResourceResponseDTO[]>(`${baseUrl}/scope/${scopeName}`)
    }

    // static async createBasicInfo(data: PrestadorPFForm) {
    // 	return credenciamento.post<PrestadorPFForm>(`${baseUrl}/pessoa-fisica/dados-basicos`, data);
    // }

    // static async createESocial(idPrestador: number, data: ESocialForm) {
    // 	return credenciamento.post<AxiosResponse<ESocialForm>>(
    // 		`${baseUrl}/${idPrestador}/pessoa-fisica/esocial`,
    // 		data,
    // 	);
    // }

    // static async createServices(idPrestador: number, data: ServicoPFForm) {
    // 	return credenciamento.post<AxiosResponse<ServicoPFForm>>(
    // 		`${baseUrl}/${idPrestador}/servico`,
    // 		data,
    // 	);
    // }

    // static async createCertification(
    // 	idPrestador: number,
    // 	data: IPrestadorEtapaQuatroForm,
    // ): Promise<AxiosResponse<IPrestadorDTO>> {
    // 	return credenciamento.post(`${baseUrl}/${idPrestador}/certificacoes`, data);
    // }
}
