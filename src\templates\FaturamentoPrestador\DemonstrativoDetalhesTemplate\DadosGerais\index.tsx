// import { Button } from '@mui/material'
import SectionContainer from 'components/atoms/SectionContainer'
import { useEffect, useState } from 'react'
import { ReactSVG } from 'react-svg'
// import SectionDadosCobranca from '../../CobrancaTemplates/organisms/SectionDadosCobranca'
import moment from 'moment'
import { SituacaoCompeteciaContasAPagar } from 'src/enum/tipo-nota-fiscal.enum'
import { IGetDadosGerais } from 'src/services/contasAPagarApi/demonstrativo-controller/types'
import { maskMon } from 'utils/masks'
import { NumberUtils } from 'utils/numberUtils'
import CancelarDemonstrativoModal from '../../ModaisDemonstrativos/ModalCancelarDemonstrativos'
import ModalDadosFinanceirosGenerico from '../../ModaisDemonstrativos/ModalDadosFinanceirosGenerico'
import DetalhamentoDescontoModal from '../../ModaisDemonstrativos/ModalDetalhamentoDesconto'
import * as S from './styles'

type DadosGeraisProps = {
    demonstrativo?: IGetDadosGerais
}

export enum EnumModalType {
    OUTROS_DESCONTOS = 'OUTROS_DESCONTOS',
    VALOR_BRUTO = 'VALOR_BRUTO',
    VALOR_IR = 'VALOR_IR',
    VALOR_ISS = 'VALOR_ISS',
    VALOR_LIQUIDO = 'VALOR_LIQUIDO'
}

const DadosGerais = ({ demonstrativo }: DadosGeraisProps) => {
    const [openCancelModal, setOpenCancelModal] = useState(false)
    const [openDescontoModal, setOpenDescontoModal] = useState(false)
    const [openModalFinanceiro, setOpenModalFinanceiro] = useState<EnumModalType | null>(null)
    const [demonstrativoData, setDemonstrativoData] = useState<IGetDadosGerais>(demonstrativo)

    function verificaNovaBaseIR() {
        if (demonstrativoData?.dadosConta?.baseCalculoIRDemaisServicos === null || demonstrativoData?.dadosConta?.baseCalculoIRReduzida === null) {
            return false
        } else {
            return true
        }
    }

    function getDetalhesFinanceiros(tipo: EnumModalType) {
        const dadosFinanceiros = demonstrativoData?.dadosFinanceiros

        switch (tipo) {
            case EnumModalType.VALOR_BRUTO:
                return dadosFinanceiros?.valorBrutoDetalhes || []
            case EnumModalType.VALOR_IR:
                return dadosFinanceiros?.valorRetencaoIRDetalhes || []
            case EnumModalType.VALOR_ISS:
                return dadosFinanceiros?.valorRetencaoISSDetalhes || []
            case EnumModalType.VALOR_LIQUIDO:
                return dadosFinanceiros?.valorLiquidoDetalhes || []
            default:
                return []
        }
    }

    useEffect(() => setDemonstrativoData(demonstrativo), [demonstrativo])
    return (
        <>
            <SectionContainer title="Contas">
                <S.Row0>
                    <S.CardInfo>
                        <h3>Produção</h3>
                        <p>{NumberUtils.maskMoney(demonstrativoData?.dadosConta?.valorProducao)}</p>
                    </S.CardInfo>
                    <S.CardInfo>
                        <h3>Glosado</h3>
                        <p>{NumberUtils.maskMoney(demonstrativoData?.dadosConta?.valorGlosa)}</p>
                    </S.CardInfo>
                    <S.CardInfo>
                        <h3>Teto</h3>
                        <p>{NumberUtils.maskMoney(demonstrativoData?.dadosConta?.valorTeto)}</p>
                    </S.CardInfo>
                    <S.CardInfo>
                        <h3>Extrateto</h3>
                        <p>{NumberUtils.maskMoney(demonstrativoData?.dadosConta?.valorExtraTeto)}</p>
                    </S.CardInfo>
                </S.Row0>
                <S.Row0>
                    <S.CardInfo>
                        <h3>Extrateto acumulado</h3>
                        <p>{NumberUtils.maskMoney(demonstrativoData?.dadosConta?.valorExtraTetoAcumulado)}</p>
                    </S.CardInfo>
                    <S.CardInfo>
                        <h3>Liberações de competências anteriores</h3>
                        <p>{NumberUtils.maskMoney(demonstrativoData?.dadosConta?.valorLiberadoCompetenciasAnteriores)}</p>
                    </S.CardInfo>
                </S.Row0>
                <S.Row0>
                    {verificaNovaBaseIR() && (
                        <>
                            <S.CardInfo>
                                <h3>Base de cálculo - IR Demais serviços - 4,8%</h3>
                                <p>{maskMon(demonstrativoData?.dadosConta?.baseCalculoIRDemaisServicos?.toFixed(2))}</p>
                            </S.CardInfo>
                            <S.CardInfo>
                                <h3>Base de cálculo - IR Reduzido - 1,2%</h3>
                                <p>{maskMon(demonstrativoData?.dadosConta?.baseCalculoIRReduzida?.toFixed(2))}</p>
                            </S.CardInfo>
                        </>
                    )}

                    <S.CardInfo>
                        <h3 className="title_destaque">Total aprovado para pagamento</h3>
                        <p>{maskMon(demonstrativoData?.dadosConta?.valorAprovadoPagamento?.toFixed(2))}</p>
                    </S.CardInfo>
                </S.Row0>
            </SectionContainer>

            <SectionContainer title="Financeiro">
                <S.Row0>
                    <S.CardInfo>
                        <h3>Código - Prestador</h3>
                        <p className="align_left">{demonstrativoData?.dadosFinanceiros?.codigoEPrestador}</p>
                    </S.CardInfo>
                </S.Row0>
                <S.Row0>
                    <S.CardInfo>
                        <h3>CNPJ</h3>
                        <p className="align_left">{demonstrativoData?.dadosFinanceiros?.cnpj}</p>
                    </S.CardInfo>
                    <S.CardInfo>
                        <h3>Município sede do prestador</h3>
                        <p className="align_left">-</p>
                    </S.CardInfo>
                    <S.CardInfo>
                        <h3>Competência do pagamento</h3>
                        <p className="align_left">{demonstrativoData?.dadosFinanceiros?.competencia.slice(0, 7).split('-').reverse().join('/')}</p>
                    </S.CardInfo>
                </S.Row0>
                {!(
                    demonstrativoData?.dadosFinanceiros?.statusDemostrativo === 'GERADO' ||
                    demonstrativoData?.dadosFinanceiros?.statusDemostrativo === 'CANCELADO' ||
                    demonstrativoData?.dadosFinanceiros?.statusDemostrativo === 'AGUARDANDO_ENVIO_NF' ||
                    demonstrativoData?.dadosFinanceiros?.statusDemostrativo === 'AGUARDANDO_APROVACAO_NF'
                ) && (
                    <>
                        <S.Row0>
                            <S.CardInfo>
                                <h3>Número do LIQ</h3>
                                <p className="align_left">{demonstrativo?.dadosFinanceiros?.numeroLIQ ?? '-'}</p>
                            </S.CardInfo>
                            <S.CardInfo>
                                <h3>Número da NOB</h3>
                                <p className="align_left">{demonstrativo?.dadosFinanceiros?.numeroNOB ?? '-'}</p>
                            </S.CardInfo>
                            <S.CardInfo style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: '10px' }}>
                                <S.CardInfo style={{ background: 'transparent' }}>
                                    <h3 style={{ display: 'flex', gap: '4px', alignItems: 'center' }}>
                                        Valor Bruto
                                        <S.InfoIconWrapper
                                            onClick={() => setOpenModalFinanceiro(EnumModalType.VALOR_BRUTO)}
                                            style={{ marginLeft: '8px' }}
                                        >
                                            <ReactSVG src="/faturamento/assets/icons/mai-ic-info.mono.svg" />
                                        </S.InfoIconWrapper>
                                    </h3>
                                    <p>{maskMon(demonstrativo?.dadosFinanceiros?.valorBruto?.toFixed(2)) ?? '-'}</p>
                                </S.CardInfo>
                            </S.CardInfo>
                        </S.Row0>
                        <S.Row0>
                            <S.CardInfoWrapper>
                                <S.CardInfo style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: '10px' }}>
                                    <S.CardInfo style={{ background: 'transparent' }}>
                                        <h3 style={{ display: 'flex', gap: '4px', alignItems: 'center' }}>
                                            Valor de retenção do IR
                                            <S.InfoIconWrapper
                                                onClick={() => setOpenModalFinanceiro(EnumModalType.VALOR_IR)}
                                                style={{ marginLeft: '8px' }}
                                            >
                                                <ReactSVG src="/faturamento/assets/icons/mai-ic-info.mono.svg" />
                                            </S.InfoIconWrapper>
                                        </h3>
                                        <p className="align_left">{maskMon(demonstrativo?.dadosFinanceiros?.valorRetencaoIR?.toFixed(2))}</p>
                                    </S.CardInfo>
                                </S.CardInfo>
                                {verificaNovaBaseIR() && <span>Cálculo = (IR - Demais serviços) + (IR - Reduzida)</span>}
                            </S.CardInfoWrapper>
                            <S.CardInfo style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: '10px' }}>
                                <S.CardInfo style={{ background: 'transparent' }}>
                                    <h3 style={{ display: 'flex', gap: '4px', alignItems: 'center' }}>
                                        Valor de retenção do ISS
                                        <S.InfoIconWrapper
                                            onClick={() => setOpenModalFinanceiro(EnumModalType.VALOR_ISS)}
                                            style={{ marginLeft: '8px' }}
                                        >
                                            <ReactSVG src="/faturamento/assets/icons/mai-ic-info.mono.svg" />
                                        </S.InfoIconWrapper>
                                    </h3>
                                    <p>{maskMon(demonstrativo?.dadosFinanceiros?.valorRetencaoISS?.toFixed(2)) ?? '-'}</p>
                                </S.CardInfo>
                            </S.CardInfo>

                            <S.CardInfo style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: '10px' }}>
                                <S.CardInfo style={{ background: 'transparent' }}>
                                    <h3 style={{ display: 'flex', gap: '4px' }}>
                                        Outros descontos
                                        <S.InfoIconWrapper onClick={() => setOpenDescontoModal(true)} style={{ marginLeft: '8px' }}>
                                            <ReactSVG src="/faturamento/assets/icons/mai-ic-info.mono.svg" />
                                        </S.InfoIconWrapper>
                                    </h3>
                                    <p>{maskMon(demonstrativo?.dadosFinanceiros?.valorOutrosDescontos?.toFixed(2)) ?? '-'}</p>
                                </S.CardInfo>
                            </S.CardInfo>
                        </S.Row0>
                        <S.Row0>
                            <S.CardInfo style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: '10px' }}>
                                <S.CardInfo style={{ background: 'transparent' }}>
                                    <h3 style={{ display: 'flex', gap: '4px', alignItems: 'center' }}>
                                        Valor líquido
                                        <S.InfoIconWrapper
                                            onClick={() => setOpenModalFinanceiro(EnumModalType.VALOR_LIQUIDO)}
                                            style={{ marginLeft: '8px' }}
                                        >
                                            <ReactSVG src="/faturamento/assets/icons/mai-ic-info.mono.svg" />
                                        </S.InfoIconWrapper>
                                    </h3>
                                    <p>{maskMon(demonstrativo?.dadosFinanceiros?.valorLiquido?.toFixed(2)) ?? '-'}</p>
                                </S.CardInfo>
                            </S.CardInfo>
                        </S.Row0>
                    </>
                )}
                <S.Row0>
                    <S.CardInfo>
                        <h3>Exercícios</h3>
                        <p className="align_left">{demonstrativoData?.dadosFinanceiros?.exercicios || '-'}</p>
                    </S.CardInfo>
                    <S.CardInfo style={{ background: '#E8EBFD' }}>
                        <h3 className="blue_title">Situação do Pagamento</h3>
                        <p className="align_left">
                            {demonstrativoData?.dadosFinanceiros?.statusDemostrativo
                                ? SituacaoCompeteciaContasAPagar[demonstrativoData?.dadosFinanceiros?.statusDemostrativo]
                                : '-'}
                        </p>
                    </S.CardInfo>

                    {demonstrativoData?.dadosFinanceiros?.dataConfirmacaoPagamento && (
                        <S.CardInfo style={{ background: '#E8EBFD' }}>
                            <h3 className="blue_title">Data de confimação de pagamento</h3>
                            <p className="align_left">
                                {moment(demonstrativoData?.dadosFinanceiros?.dataConfirmacaoPagamento).format('DD/MM/YYYY') ?? '-'}
                            </p>
                        </S.CardInfo>
                    )}
                </S.Row0>
                {Boolean(
                    demonstrativoData?.dadosFinanceiros?.statusDemostrativo === 'GERADO' ||
                        demonstrativoData?.dadosFinanceiros?.statusDemostrativo === 'CANCELADO' ||
                        demonstrativoData?.dadosFinanceiros?.statusDemostrativo === 'AGUARDANDO_ENVIO_NF' ||
                        demonstrativoData?.dadosFinanceiros?.statusDemostrativo === 'AGUARDANDO_APROVACAO_NF'
                ) && (
                    <S.InfoWrapper>
                        <ReactSVG src="/faturamento/assets/icons/mai-ic-info.mono.svg" />
                        <p>O valor líquido será calculado quando a nota fiscal for aprovada.</p>
                    </S.InfoWrapper>
                )}
            </SectionContainer>
            <SectionContainer title="Dados bancários">
                <S.Row0>
                    <S.CardInfo>
                        <h3>Número do banco</h3>
                        <p className="align_left">{demonstrativoData?.dadosBancarios?.numeroBanco ?? '-'}</p>
                    </S.CardInfo>
                    <S.CardInfo>
                        <h3>Banco</h3>
                        <p className="align_left">{demonstrativoData?.dadosBancarios?.nomeBanco ?? '-'}</p>
                    </S.CardInfo>
                    <S.CardInfo>
                        <h3>Agência</h3>
                        <p className="align_left">{demonstrativoData?.dadosBancarios?.numeroAgencia ?? '-'}</p>
                    </S.CardInfo>
                    <S.CardInfo>
                        <h3>Conta Corrente</h3>
                        <p className="align_left">{demonstrativoData?.dadosBancarios?.numeroContaCorrente ?? '-'}</p>
                    </S.CardInfo>
                </S.Row0>
            </SectionContainer>
            <S.ActionButtons>
                <S.InfoIconWrapper>
                    {/* <Button
                        iconLeft={'/faturamento/assets/icons/listMany.svg'}
                        typeButton="ghost"
                        onClick={() => {
                            console.log('')
                        }}
                    >
                        Imprimir Demostrativo
                    </Button> */}
                </S.InfoIconWrapper>
            </S.ActionButtons>
            <CancelarDemonstrativoModal openModal={openCancelModal} setOpenModal={setOpenCancelModal} />
            <DetalhamentoDescontoModal
                openModal={openDescontoModal}
                setOpenModal={setOpenDescontoModal}
                dadosFinanceiros={demonstrativoData?.dadosFinanceiros}
            />
            {openModalFinanceiro && (
                <ModalDadosFinanceirosGenerico
                    openModal={!!openModalFinanceiro}
                    setOpenModal={() => setOpenModalFinanceiro(null)}
                    dadosFinanceiros={getDetalhesFinanceiros(openModalFinanceiro)}
                    titulo={'Detalhamento'}
                />
            )}
        </>
    )
}

export default DadosGerais
