import * as S from './styles'
import moment from 'moment'
import React, { useCallback, useEffect, useState } from 'react'
import { IPagination } from 'types/common/pagination'
import { IPrestadorAnaliseQuery } from 'types/cobrancaPrestador/prestadorAnalise'
import { ILoteRecusaQuery, IPageLoteRecusa } from 'types/cobrancaPrestador/visaoRecusa'
import { useAuth } from 'src/hooks/auth'
import { CobrancaServices, IGetCobrancaProps } from 'src/services/faturamentoPrestadorApi/cobrancaServices'
import { capitalize } from 'utils/stringUtils'
import { PaginationHelper } from 'utils/helpers/paginationHelper'
import DropLote from 'components/molecules/DropLote'
import NoContent from 'components/molecules/NoContent'
import Pagination from 'components/molecules/Pagination'
import ButtonDropLots from 'components/molecules/Buttons/ButtonDownloadLots'
import { ILoteCobrancaDTO, IPageLote } from 'types/cobrancaPrestador/loteCobranca'
import { Box, CircularProgress } from '@mui/material'

type LotesRecusadosProps = {
    competenciaSelecionada: IPrestadorAnaliseQuery
    searchLote: string
    forceUpdate: boolean
    refreshList: boolean
    setLotes?: any
    lotes?: IPageLote
    loadingLotes: boolean
    setLoadingLotes: React.Dispatch<React.SetStateAction<boolean>>
    situacao: string
}

export enum TipoLoteEnum {
    CONSULTA = 'Consulta',
    HONORARIO = 'Honorário',
    SPSADT = 'SP-SADT',
    TRATAMENTO_ODONTOLOGICO = 'Tratamento Odontológico',
    RECURSO_GLOSA = 'Recurso Glosa',
    RESUMO_INTERNACAO = 'Resumo Internação',
    VALIDADO = 'Validado'
}

function createDropLot(item: ILoteCobrancaDTO) {
    return [
        {
            component: (
                <div>
                    <p>{item?.numeroLote}</p>
                </div>
            )
        },
        {
            component: (
                <div>
                    <p>{TipoLoteEnum[item?.tipoLote]}</p>
                </div>
            )
        },
        {
            component: (
                <div>
                    <p>{moment(item?.dataEnvio).format('DD/MM/YYYY [-] HH:mm')}</p>
                </div>
            )
        },
        {
            component: (
                <div>
                    <p>{item?.dataRecusa ? moment(item?.dataRecusa).format('DD/MM/YYYY [-] HH:mm') : '---'}</p>
                </div>
            )
        },
        {
            component: (
                <div>
                    <p>{item?.tipoEnvio === 'ELETRONICO' ? 'Eletrônico' : capitalize(item?.tipoEnvio)}</p>
                </div>
            )
        }
    ]
}

const handleDownloadRelatorioErrosPdf = (idLote) => {
    CobrancaServices.getRelatorioErrosPdf(idLote).then((response) => {
        console.log(response.headers)
        const fileName = response.headers['content-disposition'].split('filename=')[1] ?? 'download.pdf'
        const url = window.URL.createObjectURL(response.data)
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', fileName) //or any other extension
        document.body.appendChild(link)
        link.click()
    })
}

const ListaLotesRecusados = ({
    competenciaSelecionada,
    searchLote,
    forceUpdate,
    setLotes,
    lotes,
    refreshList,
    loadingLotes,
    setLoadingLotes,
    situacao
}: LotesRecusadosProps) => {
    const { prestadorVinculado } = useAuth()

    const [pageLotes, setPageLotes] = useState<IPageLoteRecusa>()
    const [pagination, setPagination] = useState<IPagination>()
    const [numberPage, setNumberPage] = useState(0)

    const labels: string[] = ['Lote', 'Tipo', 'Data de envio', 'Data de processamento', 'Envio']

    useEffect(() => {
        if (prestadorVinculado?.uuid) carregarLotes(searchLote, numberPage, prestadorVinculado?.uuid, situacao)
    }, [numberPage, refreshList, prestadorVinculado?.uuid, situacao])

    const carregarLotes = useCallback((filter: string, page: number, uuid: string, situacao) => {
        const getProps: IGetCobrancaProps = {
            size: 5,
            page: page || 0
        }
        setLoadingLotes(true)
        CobrancaServices.getLotesSimulacao(competenciaSelecionada?.competencia, uuid, situacao, getProps)
            .then(({ data }) => {
                setLotes(data)

                const objectPagination = PaginationHelper.parserPagination<ILoteCobrancaDTO>(data, setNumberPage)
                setPagination(objectPagination)
            })
            .finally(() => setLoadingLotes(false))
    }, [])

    return (
        <>
            {loadingLotes ? (
                <Box sx={{ display: 'flex', minHeight: 218, alignItems: 'center', justifyContent: 'center' }}>
                    <CircularProgress sx={{ color: '#2B45D4' }} />
                </Box>
            ) : (
                <S.Wrapper>
                    {!lotes || lotes?.content.length === 0 ? (
                        <NoContent title="No momento não existe nenhum lote recusado" path="/parametros/regras-de-excludencia/nova-regra" />
                    ) : (
                        <>
                            <div className="contentInput">
                                <div className="contentNumber">
                                    <span className="pageNumber">
                                        {pagination?.paginaAtual * pagination?.linhasPorPagina + 1} -{' '}
                                        {lotes?.content?.length + pagination?.paginaAtual * pagination?.linhasPorPagina}
                                        {' de '}
                                        {pagination?.totalRegistros}
                                    </span>
                                </div>
                            </div>

                            <S.HeaderLabel>
                                {labels.map((item, index) => (
                                    <div key={index}>
                                        <p>{item}</p>
                                    </div>
                                ))}
                            </S.HeaderLabel>

                            {lotes?.content?.map((lot, index) => {
                                return (
                                    <DropLote items={createDropLot(lot)} key={index}>
                                        <S.ContentDropLot>
                                            <p>Relatório de erros</p>
                                            <S.RowButtons>
                                                <ButtonDropLots icon={'/faturamento/assets/icons/download.svg'}>
                                                    <span onClick={() => handleDownloadRelatorioErrosPdf(lot?.loteCobrancaId)}>Baixar PDF</span>
                                                </ButtonDropLots>
                                            </S.RowButtons>
                                        </S.ContentDropLot>
                                    </DropLote>
                                )
                            })}
                            <div style={{ marginTop: '40px' }}>
                                <Pagination
                                    totalPage={pagination?.totalPaginas}
                                    totalRegister={pagination?.totalRegistros}
                                    actualPage={pagination?.paginaAtual}
                                    setNumberPage={(page) => setNumberPage(page)}
                                />
                            </div>
                        </>
                    )}
                </S.Wrapper>
            )}
        </>
    )
}

export default ListaLotesRecusados
