import { AxiosResponse } from 'axios'
import { ICompetenciaQuery, IValidaEncerramentoCompetencia } from 'types/analiseContas/competencia'
import { IExercicioQuery } from 'types/analiseContas/exercicio'
import { IGuiaInfoGeraisQuery, IItemGuiaDetalhesDTO, IMensagens, IPageGuiaItens, SituacaoGuiaQuery } from 'types/analiseContas/guia'
import { IGetPropsGuiaLoteAnalise, IPageGuiaLote } from 'types/analiseContas/guiasLote'
import { ILoteDTO } from 'types/analiseContas/lote'
import { IAnaliseContasLoteGlosaCommand, ILoteGlosaQuery } from 'types/analiseContas/loteGlosar'
import { ILotePendenciaCommand, ILotePendenciaQuery } from 'types/analiseContas/lotePendencia'
import { ILotePrestadorQuery } from 'types/analiseContas/lotePrestador'
import { IGetPropsLoteReabrir } from 'types/analiseContas/loteReabrir'
import { IGetPropsLote, ILoteValoresQuery, IPageLotes } from 'types/analiseContas/lotes'
import { IGetMotivoGlosaProps, IPageMotivoGlosa } from 'types/analiseContas/motivoGlosa'
import { IGetPropsPrestador, IPagePrestador, IQuantidadePrestadoresSituacaoQuery } from 'types/analiseContas/prestador'
import { IResumoQuery } from 'types/analiseContas/resumo'
import { IResumoPrestadorQuery } from 'types/analiseContas/resumoPrestador'
import { IGetPropsItemGuiaAnalise } from 'types/auditoria/itemGuia'
import { IPageResult, ISortPage } from 'types/pagination'
import { ObjectUtils } from 'utils/objectUtils'
import {
    IGlosarGuiaParcialCommand,
    IGlosarGuiaParcialQuery,
    IGlosarGuiasTotalForm,
    IGlosarGuiaTotalCommand,
    IGlosarGuiaTotalQuery
} from '../../types/analiseContas/glosarGuia'
import { apiAuditoriaMain } from '../apis/api_auditoria'
import { apiAnaliseContas } from '../apis/apiAnaliseContas'
import { TipoLoteGuiaEnum, ViaAcessoEnum } from '../faturamentoPrestadorApi/geracao-recurso-glosa/enuns'

const baseUrl = '/analise-administrativa'

export class AnaliseContasService {
    static async enviarMensagem({ idItem, mensagem, props }: { idItem: string; mensagem: string; props: ISortPage }) {
        const params = ObjectUtils.removeEmptyParams(props)
        return apiAnaliseContas.post<IPageResult<IMensagens>>(`/itens-guia/${idItem}/comentario?${params}`, { comentario: mensagem })
    }

    static async getMensagens(idItem, props: ISortPage) {
        const params = ObjectUtils.removeEmptyParams(props)
        return apiAnaliseContas.get<IPageResult<IMensagens>>(`/itens-guia/${idItem}/comentario?${params}`)
    }

    static async getCompetencias(exercicio: number): Promise<AxiosResponse<ICompetenciaQuery[]>> {
        const url = `${baseUrl}/competencia/${exercicio}/detalhe`
        return apiAnaliseContas.get(url)
    }

    static async validaEncerramento(competencia: string, idPrestador?: string): Promise<IValidaEncerramentoCompetencia> {
        const url = `${baseUrl}/competencia/validar-encerramento`
        const { data } = await apiAnaliseContas.get<IValidaEncerramentoCompetencia>(url, {
            params: {
                competencia: competencia,
                prestadorUuid: idPrestador
            }
        })

        return data
    }

    static async getResumoPrestador(competencia: string, uuidPrestador: string): Promise<AxiosResponse<IResumoPrestadorQuery>> {
        const url = `${baseUrl}/prestador/resumo?competencia=${competencia}&uuidPrestador=${uuidPrestador}`
        return apiAnaliseContas.get(url)
    }

    static async getResumo(competencia: string): Promise<AxiosResponse<IResumoQuery>> {
        const url = `${baseUrl}/resumo?competencia=${competencia}`
        return apiAnaliseContas.get(url)
    }

    static async getResumoLote(id: string, competencia: string): Promise<AxiosResponse<ILoteValoresQuery>> {
        return apiAnaliseContas.get(`${baseUrl}/lote/resumo-producao?uuidLote=${id}&competencia=${competencia}`)
    }

    static async getGuiasLote(props: IGetPropsGuiaLoteAnalise & ISortPage): Promise<AxiosResponse<IPageGuiaLote>> {
        const params = ObjectUtils.removeEmptyParams(props)
        return apiAnaliseContas.get(`${baseUrl}/guias?${params}`)
    }

    static async getArquivosGuia(idGuia) {
        return apiAnaliseContas.get(`${baseUrl}/guia/${idGuia}/arquivos-diversos`)
    }

    static async getMultipleItensGuia(idGuia: any) {
        return apiAnaliseContas.get(`/guia/${idGuia}/itens`)
    }

    static async putReabrirCompetencia(competencia: string): Promise<AxiosResponse> {
        const url = `${baseUrl}/processamento/reabrir`
        return apiAnaliseContas.put(url, undefined, {
            params: {
                competencia
            }
        })
    }
    static async putReabrirCompetenciaPorPrestador(prestadorUuid: string, competencia: string): Promise<AxiosResponse> {
        const url = `${baseUrl}/prestador/${prestadorUuid}/processamento/reabrir`
        return apiAnaliseContas.put(url, undefined, {
            params: {
                prestadorUuid,
                competencia
            }
        })
    }

    static async patchFinalizarLote(idLote: string): Promise<AxiosResponse> {
        const url = `${baseUrl}/lote/${idLote}/finalizar`
        return apiAnaliseContas.patch(url)
    }

    static async getLotes(props: IGetPropsLote & ISortPage): Promise<AxiosResponse<IPageLotes>> {
        const params = ObjectUtils.removeEmptyParams(props)
        return apiAnaliseContas.get(`${baseUrl}/prestador/lotes?${params}`)
    }

    static async getTipoLote() {
        return apiAnaliseContas.get<
            {
                prettyName: string
                value: TipoLoteGuiaEnum
            }[]
        >(`${baseUrl}/prestador/lotes/tipo-lote`)
    }

    static async getSituacoesGuiasLote(idLote: string): Promise<AxiosResponse<SituacaoGuiaQuery[]>> {
        const url = `${baseUrl}/lote/${idLote}/guia/situacoes-de-guias`
        return apiAnaliseContas.get(url)
    }

    static async getSituacoesLotesPrestador(competencia: string, prestadorId: string): Promise<AxiosResponse<ILotePrestadorQuery[]>> {
        // TODO precisa retornar o status de encerramento por prestador
        const url = `${baseUrl}/lote/situacoes-de-lotes?competencia=${competencia}&prestadorId=${prestadorId}`
        return apiAnaliseContas.get(url)
    }

    static async getPrestador(props: IGetPropsPrestador & ISortPage): Promise<AxiosResponse<IPagePrestador>> {
        const params = ObjectUtils.removeEmptyParams(props)
        return apiAnaliseContas.get(`${baseUrl}/prestadores?${params}`)
    }

    static async getQuantidadePrestadoresPorSituacao(competencia: string): Promise<AxiosResponse<IQuantidadePrestadoresSituacaoQuery>> {
        return apiAnaliseContas.get(`/prestador-analise/situacoes`, {
            params: { competencia: competencia }
        })
    }

    static async getExercicios(): Promise<AxiosResponse<IExercicioQuery[]>> {
        const url = `${baseUrl}/competencia/exercicios`
        return apiAnaliseContas.get(url)
    }

    static async putEncerrarCompetencia(competencia: string): Promise<AxiosResponse<ICompetenciaQuery>> {
        return apiAnaliseContas.put(`${baseUrl}/processamento/encerrar`, undefined, {
            params: { competencia }
        })
    }

    static async putEncerrarPorPrestador(prestadorUuuid: string, competencia: string): Promise<AxiosResponse<ICompetenciaQuery>> {
        return apiAnaliseContas
            .put(`${baseUrl}/prestador/${prestadorUuuid}/processamento/encerrar`, undefined, {
                params: {
                    competencia,
                    prestadorUuuid
                }
            })
            .then((res) => {
                if (res.data.mensagemDescricao) {
                    throw { response: res }
                }
                return res
            })
    }

    static async patchRelatarPendencia(data: ILotePendenciaCommand): Promise<AxiosResponse<ILotePendenciaQuery>> {
        return apiAnaliseContas.patch(`${baseUrl}/lote/relatar-pendencia-lote`, data)
    }

    static async patchGlosarLotes(data: IAnaliseContasLoteGlosaCommand): Promise<AxiosResponse<ILoteGlosaQuery>> {
        const url = `${baseUrl}/lote/glosar-lote`
        return apiAnaliseContas.patch(url, data)
    }

    static async patchReabrirLote(data: string[], params?: IGetPropsLoteReabrir): Promise<AxiosResponse> {
        return apiAnaliseContas.patch(`${baseUrl}/lote/reabrir-lotes`, data, {
            params
        })
    }

    static async getItensGuia(params: IGetPropsItemGuiaAnalise): Promise<AxiosResponse<IPageGuiaItens>> {
        return apiAnaliseContas.get(`${baseUrl}/guia/itens`, {
            params
        })
    }

    static async getDetalhesItemGuia(itemId: string) {
        return apiAnaliseContas.get<IItemGuiaDetalhesDTO>(`/itens-guia/${itemId}/detalhes`)
    }

    static async calcularGlosa(data: any) {
        return apiAnaliseContas.post<IItemGuiaDetalhesDTO>(`/itens-guia/calcular-glosa`, data)
    }

    static async glosarItem(data: any) {
        return apiAnaliseContas.patch(`/itens-guia/glosar`, data)
    }

    static async aprovarItem(
        id: string,
        data?: {
            horarioEspecial: boolean
            viaAcesso: ViaAcessoEnum
        }
    ) {
        return apiAnaliseContas.patch<IItemGuiaDetalhesDTO>(`/itens-guia/${id}/aprovar`, data)
    }

    static async desaprovarItem(id: string) {
        return apiAnaliseContas.patch<IItemGuiaDetalhesDTO>(`/itens-guia/${id}/desfazer-aprovacao`)
    }

    static async desfazerGlosaItem(id: string) {
        return apiAnaliseContas.patch<IItemGuiaDetalhesDTO>(`/itens-guia/${id}/desfazer-glosa`)
    }

    static async getLote(idLote: string): Promise<AxiosResponse<ILoteDTO>> {
        const url = `${baseUrl}/lote/${idLote}`
        return apiAnaliseContas.get(url)
    }

    static async getGuiaInformacoesGerais(uuidGuia: string): Promise<AxiosResponse<IGuiaInfoGeraisQuery>> {
        return apiAnaliseContas.get(`${baseUrl}/guia/informacoes-gerais?uuidGuia=${uuidGuia}`)
    }

    static async getHistoricoRegulacao(uuidGuia: string): Promise<AxiosResponse<IGuiaInfoGeraisQuery>> {
        return apiAnaliseContas.get(`${baseUrl}/guia/historico-regulacao?uuidGuia=${uuidGuia}`)
    }

    static async patchGlosarGuiaTotal(uuidGuia: string, data?: IGlosarGuiaTotalCommand[]): Promise<AxiosResponse<IGlosarGuiaTotalQuery>> {
        const url = `${baseUrl}/guia/glosar-guia-total/${uuidGuia}`
        return apiAnaliseContas.patch(url, data)
    }

    static async patchGlosarGuiasTotal(data?: IGlosarGuiasTotalForm): Promise<AxiosResponse<IGlosarGuiaTotalQuery>> {
        const url = `${baseUrl}/guia/glosar-guias-total`
        return apiAnaliseContas.patch(url, data)
    }

    static async patchGlosarGuiaParcial(uuidGuia: string, data?: IGlosarGuiaParcialCommand[]): Promise<AxiosResponse<IGlosarGuiaParcialQuery>> {
        const url = `${baseUrl}/guia/glosar-guia-parcial/${uuidGuia}`
        return apiAnaliseContas.patch(url, data)
    }

    static async patchFinalizarGuia(idGuia: string): Promise<AxiosResponse> {
        return apiAnaliseContas.patch(`${baseUrl}/guia/${idGuia}/finalizar`)
    }

    static async getMotivoGlosa(props?: IGetMotivoGlosaProps): Promise<AxiosResponse<IPageMotivoGlosa>> {
        const params = ObjectUtils.propsToParams(props)
        const url = props ? `/motivo-glosa?${params}` : `/motivo-glosa`
        return apiAnaliseContas.get(url)
    }

    static async patchReabrirGuias(data: string[]): Promise<AxiosResponse> {
        const url = `${baseUrl}/guia/reabrir-guias`
        return apiAnaliseContas.patch(url, data)
    }

    static async enviarParaFinanceiro(competencia: string): Promise<AxiosResponse> {
        const url = `${baseUrl}/envia-para-financeiro`
        return apiAnaliseContas.patch(url, { competencia })
    }

    static getArquivoGuiaDownload(idLote: string, idArquivo: string, arquivo): Promise<AxiosResponse<Blob>> {
        let url
        arquivo.isDocumentoDiverso
            ? (url = `${baseUrl}/guia/${idLote}/arquivo/${idArquivo}/download?tipoDocumento=${arquivo.tipo}`)
            : (url = `${baseUrl}/guia/${idLote}/arquivo/${idArquivo}/download`)

        return apiAnaliseContas.get(url, { responseType: 'blob' })
    }

    static getDocumentoLoteDownload(idLote: string, tipoDocumento: string): Promise<AxiosResponse<Blob>> {
        const url = `/lote/${idLote}/documentos/download`
        return apiAnaliseContas.get(url, { responseType: 'blob', params: { tipoDocumento } })
    }
}

export async function getArquivoUrl(url: string): Promise<any> {
    const res = await apiAuditoriaMain.get(`/arquivo/display?url=${url}`, {
        method: 'GET',
        responseType: 'blob'
    })
    return res
}
