import React from 'react'
import * as S from './styles'

export type CheckboxProps = {
    onCheck?: (value: boolean) => void
    label: string
    checked?: boolean
    className?: string
} & React.InputHTMLAttributes<HTMLInputElement>

const Checkbox = ({ onCheck, label, checked, className, ...rest }: CheckboxProps) => {
    return (
        <S.Wrapper className={className}>
            <S.WrapperItem>
                <label>
                    <input
                        type="checkbox"
                        onChange={() => {
                            !!onCheck && onCheck(!checked)
                        }}
                        checked={checked}
                        {...rest}
                    />
                    <S.ContainerChecked>
                        <S.Check />
                        <S.Label>
                            <p>{label}</p>
                        </S.Label>
                    </S.ContainerChecked>
                </label>
            </S.WrapperItem>
        </S.Wrapper>
    )
}

export default Checkbox
