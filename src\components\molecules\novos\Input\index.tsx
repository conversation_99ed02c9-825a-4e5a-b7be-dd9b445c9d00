/* eslint-disable react-hooks/exhaustive-deps */
import React, { InputHTMLAttributes, useEffect, useRef } from 'react'
import { Wrapper, InputStyled, Label } from './styled'

export default function Input({ value, placeholder, ...props }: InputHTMLAttributes<HTMLInputElement>) {
    const ref = useRef(null)

    useEffect(() => {
        if (value === undefined) {
            ref.current.value = ''
        }
    }, [value])

    return (
        <Wrapper>
            <Label ref={ref}>
                {placeholder}
                {props?.required ? <span> *</span> : null}
            </Label>
            <InputStyled ref={ref} value={value} {...props} />
        </Wrapper>
    )
}
