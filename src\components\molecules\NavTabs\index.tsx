/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState } from 'react'
import { ReactSVG } from 'react-svg'
import * as S from './styles'
export type Tab = {
    label: React.ReactNode
    icon?: string
    component?: any
    identifier?: string
    counter?: number
    permission?: string[]
    // qtd?: number
}
type NavTabsProps = {
    tabs: Tab[]
    // setTabName?: any
    variant?: 'type_1' | 'type_2' | 'type_3'
    type?: 'default' | 'compact'
    setTabSelected?: (index: number) => void
}
const NavTabs = ({ tabs, setTabSelected, type, variant = 'type_1' }: NavTabsProps) => {
    const [actived, setActived] = useState(0)
    return (
        <S.GuidePanel>
            <S.Content variant={variant}>
                {tabs?.map((item, index) => (
                    <S.Tabs
                        type={type}
                        variant={variant}
                        key={`tabs-${item.label}-${index}`}
                        actived={index === actived}
                        onClick={() => {
                            setActived(index)
                            // setTabName(item?.label)
                            !!setTabSelected && setTabSelected(index)
                        }}
                    >
                        {!!item.icon && <ReactSVG className="icon" src={item.icon} wrapper="span" />}
                        <p>{item.label}</p>

                        {item?.counter !== undefined && item?.counter !== null && (
                            <S.CounterBadge actived={index === actived}>{item?.counter}</S.CounterBadge>
                        )}
                        {/* {item?.qtd && <S.QtdBox><p>{item?.qtd}</p></S.QtdBox>} */}
                    </S.Tabs>
                ))}
            </S.Content>
            {tabs?.find((_, index) => index === actived)?.component}
        </S.GuidePanel>
    )
}

export default NavTabs
