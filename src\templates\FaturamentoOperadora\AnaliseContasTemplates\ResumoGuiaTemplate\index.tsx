import Button from 'components/atoms/Button'
import DividerSectionCard from 'components/atoms/DividerSectionCard'
import TitleSection from 'components/atoms/TitleSection'
import FileCard from 'components/molecules/FileCard'
import ModalAddGlossReasons from 'components/molecules/ModalAddGlossReasons'
import { IGlossReason } from 'components/molecules/ModalAddGlossReasons/gloss-reason'
import NewBadge from 'components/molecules/NewBadge'
import { useCallback, useEffect, useState } from 'react'
import { useGlobalEvents } from 'src/hooks/globalEvents'
import { useToast } from 'src/hooks/toast'
import { AnaliseContasService } from 'src/services/analiseContasApi/analiseContasServices'
import { StatusItemEnum } from 'src/services/faturamentoPrestadorApi/geracao-recurso-glosa/enuns'
import { IGuiaInfoGeraisQuery } from 'types/analiseContas/guia'
import { IItemGuiaDTO } from 'types/analiseContas/guiasLote'
import { SituacaoGuia, SituacaoLote } from 'types/common/enums'
import { getCurrentClient } from 'utils/parseAssets'
import { ClientEnum } from 'utils/parseAssets/client-enum'
import { capitalize, getMessageErrorFromApiResponse } from 'utils/stringUtils'
import GuiaAnaliseContas from './organisms/GuiaAnaliseContas'
import * as S from './styles'
import dynamic from 'next/dynamic'

const AnimatedLoadingLottie = dynamic(() => import('components/atoms/AnimatedLoadingLottie'), { ssr: false })

export interface IResumoGuiasProps {
    idGuia: string
}

const ResumoGuiaTemplate = ({ idGuia }: IResumoGuiasProps) => {
    const { addToast } = useToast()
    const { publish } = useGlobalEvents('guia')

    const [motivoGlosaSelect, setMotivoGlosaSelect] = useState(null)
    const [isModalOpen, setIsModalOpen] = useState(false)
    const [justificativaGlosa, setJustificativaGlosa] = useState('')
    const [infoGeraisGuia, setInfoGeraisGuia] = useState<IGuiaInfoGeraisQuery>()
    const [arquivos, setArquivos] = useState([])
    const [itensGuia, setItensGuia] = useState<IItemGuiaDTO>()
    const [actionRequestInProgress, setActionRequestInProgress] = useState(false)
    const [loadingData, setLoadingData] = useState(false)

    useEffect(() => {
        AnaliseContasService.getMultipleItensGuia(idGuia)
            .then(({ data }) => {
                setItensGuia(data)
            })
            .catch((err) => {
                addToast({
                    title: 'Ocorreu um erro ao carregar informações de arquivos',
                    description: getMessageErrorFromApiResponse(err),
                    type: 'error',
                    duration: 3000
                })
            })
    }, [])

    const loadPageData = useCallback(() => {
        setLoadingData(true)
        if (!idGuia) return
        AnaliseContasService.getGuiaInformacoesGerais(idGuia)
            .then(({ data }) => {
                setInfoGeraisGuia(data)
                AnaliseContasService.getArquivosGuia(idGuia).then(({ data }) => {
                    setArquivos(data.arquivos)
                    setLoadingData(false)
                })
            })
            .catch((err) => {
                addToast({
                    title: 'Ocorreu um erro ao carregar informações da guia',
                    description: getMessageErrorFromApiResponse(err),
                    type: 'error',
                    duration: 3000
                })
                setLoadingData(false)
            })
    }, [idGuia, setInfoGeraisGuia])

    const handleSubmitGlosa = useCallback(
        (reasons: IGlossReason[]) => {
            const data = reasons.map((r) => ({ justificativaGlosa: r.justificativaGlosa, motivoGlosa: r.motivoGlosaUUID }))

            setActionRequestInProgress(true)

            return AnaliseContasService.patchGlosarGuiaTotal(idGuia, data)
                .then(() => {
                    loadPageData()
                    publish('reload')

                    addToast({
                        title: 'Guia glosada com sucesso',
                        type: 'success',
                        duration: 3000
                    })
                    setIsModalOpen(false)
                })
                .catch((err) => {
                    addToast({
                        title: 'Ocorreu um erro ao glosar a guia',
                        description: getMessageErrorFromApiResponse(err),
                        type: 'error',
                        duration: 3000
                    })
                })
                .finally(() => {
                    setActionRequestInProgress(false)
                })
        },
        [justificativaGlosa, motivoGlosaSelect, idGuia, loadPageData]
    )

    const handleFinalizarGuia = useCallback(() => {
        setActionRequestInProgress(true)

        AnaliseContasService.patchFinalizarGuia(idGuia)
            .then(() => {
                loadPageData()
                publish('reload')

                addToast({
                    title: 'Guia finalizada com sucesso',
                    type: 'success',
                    duration: 3000
                })
            })
            .catch((err) => {
                addToast({
                    title: 'Ocorreu um erro ao finalizar a guia',
                    type: 'error',
                    description: getMessageErrorFromApiResponse(err),
                    duration: 3000
                })
            })
            .finally(() => {
                setActionRequestInProgress(false)
            })
    }, [idGuia, loadPageData])

    const handleReabrirGuia = useCallback(() => {
        setActionRequestInProgress(true)

        AnaliseContasService.patchReabrirGuias([idGuia])
            .then(() => {
                addToast({ type: 'success', title: 'Guia reaberta com sucesso' })
                loadPageData()
            })
            .catch((err) => {
                addToast({
                    type: 'error',
                    title: 'Erro ao reabrir a guia',
                    description: getMessageErrorFromApiResponse(err)
                })
            })
            .finally(() => {
                setActionRequestInProgress(false)
            })
    }, [])

    const allowFinishGuide = () => {
        if (getCurrentClient() !== ClientEnum.PLANSERV) {
            const items = [
                ...(itensGuia?.itensGuiaDiariaDTO || []),
                ...(itensGuia?.itensGuiaHonorarioDTO || []),
                ...(itensGuia?.itensGuiaMaterialDTO || []),
                ...(itensGuia?.itensGuiaMedicamentoDTO || []),
                ...(itensGuia?.itensGuiaOPMEDTO || []),
                ...(itensGuia?.itensGuiaProcedimentoDTO || []),
                ...(itensGuia?.itensGuiaTaxaDTO || []),
                ...(itensGuia?.itensGuiaGasoterapiaDTO || [])
            ]
            return items?.every(({ status }) => [StatusItemEnum.SEM_GLOSA, StatusItemEnum.GLOSADO, StatusItemEnum.RECURSADO].includes(status))
        }

        return true
    }

    useEffect(loadPageData, [idGuia])

    return (
        <S.Content>
            {loadingData ? (
                <AnimatedLoadingLottie style={{ width: '240px', margin: 'auto' }} />
            ) : (
                <>
                    <S.SubHeader>
                        <div>
                            <S.SubHeaderTitle>
                                Guia -{' '}
                                {infoGeraisGuia?.numeroGuiaOperadora ? infoGeraisGuia?.numeroGuiaOperadora : infoGeraisGuia?.numeroGuiaPrestador}
                                {infoGeraisGuia?.tipoProcesso && <NewBadge style={{ fontSize: '14px' }} type={infoGeraisGuia?.tipoProcesso} />}
                                {/* // */}
                                {infoGeraisGuia?.isBenficiarioSuspenso && (
                                    <NewBadge
                                        style={{ fontSize: '14px', background: '#CFE5F3', borderRadius: '8px', padding: '4px 10px' }}
                                        type={'DEFAULT'}
                                        text={'Beneficiário suspenso'}
                                    />
                                )}
                                {infoGeraisGuia?.isBenficiarioCancelado && (
                                    <NewBadge
                                        style={{ fontSize: '14px', background: '#fde1df', borderRadius: '8px', padding: '4px 10px' }}
                                        type={'DEFAULT'}
                                        text={'Beneficiário cancelado'}
                                    />
                                )}
                                {infoGeraisGuia?.isBenficiarioEmCarencia && (
                                    <NewBadge
                                        style={{ fontSize: '14px', background: '#fde1df', borderRadius: '8px', padding: '4px 10px' }}
                                        type={'DEFAULT'}
                                        text={'Beneficiário em carência'}
                                    />
                                )}
                                {/* // */}
                            </S.SubHeaderTitle>
                            <S.SubHeaderSubTitle>{capitalize(infoGeraisGuia?.prestador?.nomePrestador)}</S.SubHeaderSubTitle>
                        </div>

                        <S.ActionsContainer>
                            {infoGeraisGuia?.situacaoAtual === SituacaoGuia.ABERTA && (
                                <>
                                    <Button
                                        typeButton="text"
                                        themeButton="ihealth"
                                        style={{ width: 'fit-content', whiteSpace: 'nowrap' }}
                                        onClick={() => setIsModalOpen(true)}
                                        disabled={actionRequestInProgress}
                                    >
                                        Glosar guia
                                    </Button>
                                    <Button
                                        themeButton={'warning'}
                                        onClick={() => handleFinalizarGuia()}
                                        disabled={actionRequestInProgress || !allowFinishGuide()}
                                        title={
                                            !allowFinishGuide() &&
                                            'A guia só poderá ser finalizada quando as análises técnica e administrativa forem realizadas'
                                        }
                                    >
                                        Finalizar
                                    </Button>
                                </>
                            )}

                            {infoGeraisGuia?.situacaoAtual === SituacaoGuia.ANALISADA && infoGeraisGuia?.situacaoLote !== SituacaoLote.FECHADO && (
                                <Button
                                    typeButton="text"
                                    themeButton="ihealth"
                                    onClick={handleReabrirGuia}
                                    style={{ width: 'fit-content' }}
                                    iconLeft="/faturamento/assets/icons/refresh.svg"
                                    disabled={actionRequestInProgress}
                                >
                                    Reabrir
                                </Button>
                            )}
                        </S.ActionsContainer>
                    </S.SubHeader>
                    <DividerSectionCard>
                        <TitleSection>Documentos</TitleSection>
                        <S.Header>
                            {arquivos?.length > 0 && !loadingData ? (
                                <S.FileCardsContainer>
                                    {arquivos?.map((arquivo) => (
                                        <FileCard
                                            label="Arquivo"
                                            background="grey"
                                            themeColor="blue"
                                            standart="expanded"
                                            key={arquivo.id}
                                            fileName={arquivo.filename}
                                            idGuia={idGuia}
                                            idArquivo={arquivo.id}
                                            arquivo={arquivo}
                                        />
                                    ))}
                                </S.FileCardsContainer>
                            ) : (
                                <p>Não há documentos vinculados a esta guia </p>
                            )}
                        </S.Header>
                    </DividerSectionCard>
                    <DividerSectionCard dividerContent={true}>
                        <GuiaAnaliseContas setItensGuia={setItensGuia} uuidGuia={idGuia} infoGeraisGuia={infoGeraisGuia} itensGuia={itensGuia} />
                    </DividerSectionCard>
                </>
            )}

            <ModalAddGlossReasons
                isOpen={isModalOpen}
                onClose={() => {
                    setIsModalOpen(!isModalOpen)
                }}
                onConfirm={handleSubmitGlosa}
            />
        </S.Content>
    )
}

export default ResumoGuiaTemplate
