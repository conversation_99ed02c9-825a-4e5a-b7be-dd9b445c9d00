import * as S from './styles'
import moment from 'moment'
import React, { useState, useEffect, useCallback } from 'react'
import { IPagination } from 'types/common/pagination'
import { IPrestadorAnaliseQuery } from 'types/cobrancaPrestador/prestadorAnalise'
import { ILoteCobrancaDTO, IPageLote } from 'types/cobrancaPrestador/loteCobranca'
import { useAuth } from 'src/hooks/auth'
import { CobrancaServices, IGetCobrancaProps } from 'src/services/faturamentoPrestadorApi/cobrancaServices'
import { NumberUtils } from 'utils/numberUtils'
import { capitalize } from 'utils/stringUtils'
import { PaginationHelper } from 'utils/helpers/paginationHelper'
import DropLote from 'components/molecules/DropLote'
import NoContent from 'components/molecules/NoContent'
import Pagination from 'components/molecules/Pagination'
import ButtonDropLots from 'components/molecules/Buttons/ButtonDownloadLots'
import Checkbox from 'components/atoms/CheckBox'
import { GeracaoRecursoGlosa } from 'src/services/faturamentoPrestadorApi/geracao-recurso-glosa'
import { ModuloEnum, TipoEnvioEnum } from 'src/services/faturamentoPrestadorApi/geracao-recurso-glosa/enuns'
import { IGetLotes } from 'src/services/faturamentoPrestadorApi/geracao-recurso-glosa/types'
import { IPage } from 'types/pagination'
import { useToast } from 'src/hooks/toast'
import { Box, CircularProgress } from '@mui/material'
import { retiraSequencial } from 'utils/functions'
import { TipoLoteEnum } from 'src/services/composicaoPagamentoApi/enum'

type ListaLotesEmAnaliseProps = {
    competenciaSelecionada: IPrestadorAnaliseQuery
    searchLote: string
    forceUpdate: boolean
    refreshList: boolean
    setLotes: any
    lotes: any
    filtro?: string
    checkboxFilter: ICheckeboxFilterAnalise
    setCheckboxFilter: React.Dispatch<React.SetStateAction<ICheckeboxFilterAnalise>>
    loadingLotes: boolean
    setLoadingLotes: React.Dispatch<React.SetStateAction<boolean>>
}
export interface ICheckeboxFilterAnalise {
    lotesCobranca: boolean
    lotesRecursoGlosa: boolean
}

const ListaLotesEmAnalise = ({
    competenciaSelecionada,
    setLotes,
    lotes,
    refreshList,
    filtro,
    checkboxFilter,
    setCheckboxFilter,
    loadingLotes,
    setLoadingLotes
}: ListaLotesEmAnaliseProps) => {
    const { prestadorVinculado } = useAuth()
    const { addToast } = useToast()

    const labels: string[] = ['Lote', 'Tipo', 'Data de envio', 'Data do processamento', 'Envio']

    const [valorTotalApresentado, setValorTotalApresentado] = useState<number>(0)
    const [pagination, setPagination] = useState<IPagination>()
    const [numberPage, setNumberPage] = useState(0)

    function createDropLot(item: IGetLotes) {
        return [
            {
                component: (
                    <div>
                        <p>{item?.identificadorLote}</p>
                    </div>
                )
            },
            {
                component: (
                    <div>
                        <p>{item.tipoLote}</p>
                    </div>
                )
            },
            {
                component: (
                    <div>
                        <p>{moment(item.dataEnvio).format('DD/MM/YYYY [-] HH:mm')}</p>
                    </div>
                )
            },
            {
                component: (
                    <div>
                        <p>{''}</p>
                    </div>
                )
            },
            {
                component: (
                    <div>
                        <p>{item.tipoEnvio === 'ELETRONICO' ? 'Eletrônico' : capitalize(item.tipoEnvio)}</p>
                    </div>
                )
            }
        ]
    }

    const handleDownloadProtocoloRecebimentoXml = (idLote) => {
        CobrancaServices.getProtocoloRecebimentoXml(idLote).then((response) => {
            const fileName = response.headers['content-disposition'].split('filename=')[1] ?? 'download.xml'
            const url = window.URL.createObjectURL(new Blob([response.data]))
            const link = document.createElement('a')
            link.href = url
            link.setAttribute('download', fileName) //or any other extension
            document.body.appendChild(link)
            link.click()
        })
    }

    const handleDownloadProtocoloRecebimentoPdf = (idLote) => {
        CobrancaServices.getProtocoloRecebimentoPdf(idLote).then((response) => {
            const fileName = response.headers['content-disposition'].split('filename=')[1] ?? 'download.pdf'
            const url = window.URL.createObjectURL(response.data)
            const link = document.createElement('a')
            link.href = url
            link.setAttribute('download', fileName) //or any other extension
            document.body.appendChild(link)
            link.click()
        })
    }

    // const carregarLotes = useCallback(
    //     (filter?: string, page?: number) => {
    //         const getProps: IGetCobrancaProps =
    //             filter === ''
    //                 ? {
    //                       size: 10,
    //                       page: page || 0
    //                   }
    //                 : {
    //                       size: 10,
    //                       page: page || 0,
    //                       filtroNumeroLote: filter
    //                   }

    //         CobrancaServices.getLotes(competenciaSelecionada?.competencia, prestadorVinculado?.uuid, 'EM_ANALISE', getProps).then(({ data }) => {
    //             // if (data.totalElements > 0) {
    //             setLotes(data)
    //             // }
    //             // initLotesChecks(data.content)

    //             const objectPagination = PaginationHelper.parserPagination<ILoteCobrancaDTO>(data, setNumberPage)
    //             setPagination(objectPagination)
    //         })
    //     },
    //     [competenciaSelecionada, prestadorVinculado]
    // )

    const carregarLotes = () => {
        setLoadingLotes(true)
        if (competenciaSelecionada?.competencia && prestadorVinculado?.uuid) {
            GeracaoRecursoGlosa.getlotesEmAnalise({
                identificadorLote: retiraSequencial(filtro),
                competencia: competenciaSelecionada?.competencia,
                prestadorId: prestadorVinculado?.uuid,
                modulo: ModuloEnum.MEDICO,
                lotes: checkboxFilter?.lotesCobranca,
                lotesRecursoGlosa: true,
                page: numberPage,
                size: 5
            })
                .then(({ data }) => {
                    setLotes(data)

                    const objectPagination = PaginationHelper.parserPagination<IGetLotes>(data, setNumberPage)
                    setPagination(objectPagination)
                })
                .catch((err) => {
                    addToast({
                        title: err?.message ? err?.message : 'Ocorreu um erro ao buscar as informações',
                        type: 'error',
                        duration: 3000
                    })
                })
                .finally(() => setLoadingLotes(false))
        }
    }

    useEffect(() => {
        carregarLotes()
    }, [numberPage, refreshList, checkboxFilter])

    useEffect(() => {
        CobrancaServices.getValorApresentado(competenciaSelecionada?.competencia, prestadorVinculado?.uuid).then(({ data }) => {
            setValorTotalApresentado(data.valorApresentado)
        })
    }, [])

    return (
        <>
            {loadingLotes ? (
                <Box sx={{ display: 'flex', minHeight: 218, alignItems: 'center', justifyContent: 'center' }}>
                    <CircularProgress sx={{ color: '#2B45D4' }} />
                </Box>
            ) : (
                <S.Wrapper>
                    {!lotes || lotes?.content.length === 0 ? (
                        <NoContent title="No momento não existe nenhum lote em análise" path="/parametros/regras-de-excludencia/nova-regra" />
                    ) : (
                        <>
                            <div className="contentInput">
                                <div className="contentNumber">
                                    <span className="pageNumber">
                                        {pagination?.paginaAtual * pagination?.linhasPorPagina + 1} -{' '}
                                        {lotes?.content?.length + pagination?.paginaAtual * pagination?.linhasPorPagina}
                                        {' de '}
                                        {pagination?.totalRegistros}
                                    </span>
                                </div>
                            </div>
                            <S.HeaderLabel>
                                {labels.map((item, index) => (
                                    <div key={index}>
                                        <p>{item}</p>
                                    </div>
                                ))}
                            </S.HeaderLabel>
                            {lotes?.content?.map((lote: IGetLotes, index) => {
                                return (
                                    <DropLote items={createDropLot(lote)} key={index}>
                                        <S.ContentDropLot>
                                            <p>Protocolo de Recebimento</p>
                                            <S.RowButtons>
                                                {lote.tipoEnvio !== TipoEnvioEnum?.MANUAL && (
                                                    <ButtonDropLots icon={'/faturamento/assets/icons/download.svg'}>
                                                        <span onClick={() => handleDownloadProtocoloRecebimentoXml(lote?.loteId)}>Baixar XML</span>
                                                    </ButtonDropLots>
                                                )}
                                                <ButtonDropLots icon={'/faturamento/assets/icons/download.svg'}>
                                                    <span onClick={() => handleDownloadProtocoloRecebimentoPdf(lote?.loteId)}>Baixar PDF</span>
                                                </ButtonDropLots>
                                            </S.RowButtons>
                                        </S.ContentDropLot>
                                    </DropLote>
                                )
                            })}
                            <div style={{ marginTop: '40px' }}>
                                <Pagination
                                    totalPage={pagination?.totalPaginas}
                                    totalRegister={pagination?.totalRegistros}
                                    actualPage={pagination?.paginaAtual}
                                    setNumberPage={pagination?.setNumberPage}
                                />
                            </div>
                        </>
                    )}
                </S.Wrapper>
            )}
        </>
    )
}

export default ListaLotesEmAnalise
