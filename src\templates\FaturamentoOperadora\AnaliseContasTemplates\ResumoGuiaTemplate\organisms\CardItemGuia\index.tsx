import { Box, Button, CircularProgress, TextField } from '@mui/material'

import Chip from 'components/atoms/Chip'
import InputNumberQtd from 'components/atoms/Inputs/InputNumberQtd'
import ReadOnlyField from 'components/atoms/ReadOnlyField'
import Status from 'components/atoms/Status'
import ModalAddGlossReasons from 'components/molecules/ModalAddGlossReasons'
import { IGlossReason } from 'components/molecules/ModalAddGlossReasons/gloss-reason'
import Pagination from 'components/molecules/Pagination'
import ModalGenericComponent from 'components/organisms/ModalGeneric'
import React, { useEffect, useMemo, useState } from 'react'
import { ReactSVG } from 'react-svg'
import { useToast } from 'src/hooks/toast'
import { AnaliseContasService } from 'src/services/analiseContasApi/analiseContasServices'
import { StatusItemEnum, TipoItemEnum, ViaAcessoEnum } from 'src/services/faturamentoPrestadorApi/geracao-recurso-glosa/enuns'
import { IGuiaInfoGeraisQuery, IItemGuiaDetalhesDTO, IMensagens } from 'types/analiseContas/guia'
import { IItemGuiaAgrupadoDTO, IItemGuiaDTO } from 'types/analiseContas/guiasLote'
import { IPagination } from 'types/common/pagination'
import { PaginationHelper } from 'utils/helpers/paginationHelper'
import { NumberUtils } from 'utils/numberUtils'
import InfoItensModal from '../InfoItensModal'
import * as S from './styles'
import { grauParticipacaoOptions, statusOptions, viaAcessoOptions } from './utils'
import dynamic from 'next/dynamic'

const AnimatedLoadingLottie = dynamic(() => import('components/atoms/AnimatedLoadingLottie'), { ssr: false })

const CardItemGuia = ({
    item,
    index,
    setItensGuia,
    permission,
    infoGeraisGuia
}: {
    item: IItemGuiaAgrupadoDTO
    index: number
    setItensGuia: React.Dispatch<React.SetStateAction<IItemGuiaDTO>>
    infoGeraisGuia: IGuiaInfoGeraisQuery
    permission?: boolean
}) => {
    const { addToast } = useToast()
    const [detalhes, setDetalhes] = useState<IItemGuiaDetalhesDTO>()
    const [calculoGlosa, setCalculoGlosa] = useState<IItemGuiaDetalhesDTO>()
    const [infoModalOpen, setInfoModalOpen] = useState(false)
    const [isOpen, setOpen] = useState(false)
    const [isModalGlosaOpen, setIsModalGlosaOpen] = useState(false)
    const [loading, setLoading] = useState(false)
    const [requisicaoController, setRequisicaoController] = useState({
        desfazerGlosaBtn: false,
        desfazerAprovacaoBtn: false,
        aprovacaoBtn: false
    })

    const [formControl, setFormControl] = useState({
        viaAcesso: '',
        quantidadeAGlosar: null,
        horarioEspecial: null
    })

    // MODAL CONTROLLER
    const [chatModalOpen, setChatModalOpen] = useState(false)
    const [pagination, setPagination] = React.useState<IPagination>()
    const [numberPage, setNumberPage] = React.useState(0)
    const [mensagens, setMensagens] = React.useState<IMensagens[]>([])
    const [loadingMensages, setLoadingMensages] = useState(false)
    const [mensageField, setMensageField] = useState('')

    function atualizaItensGuia(tipoGuia: TipoItemEnum | 'HONORARIO', status: StatusItemEnum) {
        const objectLiteral: Record<TipoItemEnum | 'HONORARIO', void> = {
            ['HONORARIO']: setItensGuia((prev) => ({
                ...prev,
                itensGuiaHonorarioDTO: prev?.itensGuiaHonorarioDTO?.map((procedimento) => {
                    if (procedimento?.uuid === item?.uuid) {
                        return { ...procedimento, status: status }
                    } else {
                        return procedimento
                    }
                })
            })),
            [TipoItemEnum.DIARIA]: setItensGuia((prev) => ({
                ...prev,
                itensGuiaDiariaDTO: prev?.itensGuiaDiariaDTO?.map((procedimento) => {
                    if (procedimento?.uuid === item?.uuid) {
                        return { ...procedimento, status: status }
                    } else {
                        return procedimento
                    }
                })
            })),
            [TipoItemEnum.GASOTERAPIA]: setItensGuia((prev) => ({
                ...prev,
                itensGuiaGasoterapiaDTO: prev?.itensGuiaGasoterapiaDTO?.map((procedimento) => {
                    if (procedimento?.uuid === item?.uuid) {
                        return { ...procedimento, status: status }
                    } else {
                        return procedimento
                    }
                })
            })),
            [TipoItemEnum.MATERIAL]: setItensGuia((prev) => ({
                ...prev,
                itensGuiaMaterialDTO: prev?.itensGuiaMaterialDTO?.map((procedimento) => {
                    if (procedimento?.uuid === item?.uuid) {
                        return { ...procedimento, status: status }
                    } else {
                        return procedimento
                    }
                })
            })),
            [TipoItemEnum.MEDICAMENTO]: setItensGuia((prev) => ({
                ...prev,
                itensGuiaMedicamentoDTO: prev?.itensGuiaMedicamentoDTO?.map((procedimento) => {
                    if (procedimento?.uuid === item?.uuid) {
                        return { ...procedimento, status: status }
                    } else {
                        return procedimento
                    }
                })
            })),
            [TipoItemEnum.OPME]: setItensGuia((prev) => ({
                ...prev,
                itensGuiaOPMEDTO: prev?.itensGuiaOPMEDTO?.map((procedimento) => {
                    if (procedimento?.uuid === item?.uuid) {
                        return { ...procedimento, status: status }
                    } else {
                        return procedimento
                    }
                })
            })),
            [TipoItemEnum.PROCEDIMENTO]: setItensGuia((prev) => ({
                ...prev,
                itensGuiaProcedimentoDTO: prev?.itensGuiaProcedimentoDTO?.map((procedimento) => {
                    if (procedimento?.uuid === item?.uuid) {
                        return { ...procedimento, status: status }
                    } else {
                        return procedimento
                    }
                })
            })),
            [TipoItemEnum.TAXA]: setItensGuia((prev) => ({
                ...prev,
                itensGuiaTaxaDTO: prev?.itensGuiaTaxaDTO?.map((procedimento) => {
                    if (procedimento?.uuid === item?.uuid) {
                        return { ...procedimento, status: status }
                    } else {
                        return procedimento
                    }
                })
            }))
        }

        objectLiteral[tipoGuia]
    }

    function carregarMensagens() {
        setLoadingMensages(true)
        AnaliseContasService.getMensagens(item?.uuid, { page: numberPage, size: 5, sort: 'dataCriacao,DESC' })
            .then(({ data }) => {
                setMensagens(data?.content)
                setPagination(PaginationHelper.parserPagination<IMensagens>(data, setNumberPage))
            })
            .catch(() => {
                addToast({
                    type: 'error',
                    duration: 3000,
                    title: 'Erro ao carregar mensagem'
                })
            })
            .finally(() => setLoadingMensages(false))
    }

    function enviarMensagem() {
        setLoadingMensages(true)
        AnaliseContasService.enviarMensagem({
            idItem: item?.uuid,
            mensagem: mensageField,
            props: { page: numberPage, size: 5, sort: 'dataCriacao,DESC' }
        })
            .then(({ data }) => {
                // setNumberPage(0)
                setMensageField('')
                setMensagens(data?.content)
                setPagination(PaginationHelper.parserPagination<IMensagens>(data, setNumberPage))
            })
            .catch(() => {
                addToast({
                    type: 'error',
                    duration: 3000,
                    title: 'Erro ao enviar mensagem'
                })
            })
            .finally(() => setLoadingMensages(false))
    }

    // useEffect(() => {
    //     if (detalhes && detalhes.calculosDeGlosa && ) {
    //         const updatedValues = {}

    //         if (!formControl.horarioEspecial) {
    //             updatedValues.horarioEspecial = detalhes.horarioEspecial
    //         }

    //         if (formControl.viaAcesso === '') {
    //             updatedValues.viaAcesso = detalhes.viaAcesso
    //         }

    //         if (formControl.quantidadeAGlosar === 0) {
    //             updatedValues.quantidadeAGlosar = detalhes.calculosDeGlosa.quantidadeAGlosar
    //         }
    //         console.log('updatedValues', updatedValues)
    //         // FIXME:
    //         setFormControl((state) => {
    //             console.log('updatedValues', { ...state })
    //             return { ...state, ...updatedValues }
    //         })
    //     }
    // }, [detalhes])

    useEffect(() => {
        if (isOpen) carregarDetalhesItem(item?.uuid)
    }, [isOpen])

    useEffect(() => {
        if (chatModalOpen) carregarMensagens()
    }, [chatModalOpen, numberPage])

    useEffect(() => {
        // carregarDetalhesItem(item.uuid)
        if (detalhes && formControl?.quantidadeAGlosar !== null && formControl?.viaAcesso !== '' && formControl?.horarioEspecial !== null) {
            actionGlosa(item?.uuid)
        }
    }, [formControl?.quantidadeAGlosar, formControl?.viaAcesso, formControl?.horarioEspecial])

    const carregarDetalhesItem = (itemId: string) => {
        AnaliseContasService.getDetalhesItemGuia(itemId)
            .then(({ data }) => {
                setDetalhes(data)
                setFormControl({
                    quantidadeAGlosar: data?.calculosDeGlosa?.quantidadeAGlosar === 0 ? 0 : data?.calculosDeGlosa?.quantidadeAGlosar,
                    horarioEspecial: data?.horarioEspecial,
                    viaAcesso: data?.viaAcesso
                })

                // const objectPagination = PaginationHelper.parserPagination<IGuiaItensQuery>(data, setNumberPage)
                // setPagination(objectPagination)
            })
            .catch(() => {
                addToast({
                    title: 'Ocorreu um erro ao buscar detalhe do item.',
                    type: 'error',
                    duration: 3000
                })
            })
    }

    const actionGlosa = (itemId: string) => {
        setLoading(true)
        const { horarioEspecial, quantidadeAGlosar, viaAcesso } = formControl

        const body = {
            viaAcesso,
            horarioEspecial,
            quantidadeAGlosar,
            motivosGlosa: [],
            itemGuiaId: itemId
        }

        AnaliseContasService.calcularGlosa(body)
            .then(({ data }) => {
                setCalculoGlosa(data)
            })
            .catch(() => {
                addToast({
                    type: 'error',
                    duration: 3000,
                    title: ''
                })
            })
            .finally(() => setLoading(false))
    }

    const handleGlosarItem = (reasons: IGlossReason[]) => {
        const { horarioEspecial, viaAcesso, quantidadeAGlosar } = formControl

        const data = {
            viaAcesso,
            horarioEspecial,
            quantidadeAGlosar,
            itemGuiaId: item?.uuid,
            motivosGlosa: reasons?.map((r) => ({ justificativaGlosa: r?.justificativaGlosa, motivoGlosa: r?.motivoGlosaUUID }))
        }

        return AnaliseContasService.glosarItem(data)
            .then(({ data }) => {
                carregarDetalhesItem(item?.uuid)
                atualizaItensGuia(item?.tipo, data?.status)
            })
            .catch((err) => {
                addToast({
                    type: 'error',
                    duration: 3000,
                    title: JSON.stringify(err)
                })
            })
    }

    const validarStatusGuia = (): boolean => {
        const isGlosado = item.status === 'GLOSADO'
        const isAnaliseTecnica = infoGeraisGuia.etapaGuiaAnalise === 'ANALISE_TECNICA'
        const isAnaliseAdministrativa = infoGeraisGuia.etapaGuiaAnalise === 'ANALISE_ADMINISTRATIVA'
        return (
            isGlosado &&
            ((isAnaliseTecnica && detalhes?.motivosDeGlosaTecnica?.length > 0) ||
                (isAnaliseAdministrativa && detalhes?.motivosDeGlosaAdministrativa?.length > 0))
        )
    }

    const handleAprovarItem = () => {
        setRequisicaoController((prev) => ({ ...prev, aprovacaoBtn: true }))
        AnaliseContasService.aprovarItem(item?.uuid, {
            horarioEspecial: formControl?.horarioEspecial,
            viaAcesso: formControl?.viaAcesso as ViaAcessoEnum
        })
            .then(({ data }) => {
                carregarDetalhesItem(item?.uuid)
                // setStatus(data?.status)
                atualizaItensGuia(item?.tipo, data?.status)
            })
            .catch((data) => {
                addToast({
                    type: 'error',
                    duration: 3000,
                    title: data?.response?.data?.message
                })
            })
            .finally(() => setRequisicaoController((prev) => ({ ...prev, aprovacaoBtn: false })))
    }

    const handleDesfazerGlosa = () => {
        setRequisicaoController((prev) => ({ ...prev, desfazerGlosaBtn: true }))
        setDetalhes(null)
        AnaliseContasService.desfazerGlosaItem(item?.uuid)
            .then(({ data }) => {
                carregarDetalhesItem(item?.uuid)
                atualizaItensGuia(item?.tipo, data?.status)
            })
            .catch(() => {
                addToast({
                    type: 'error',
                    duration: 3000,
                    title: 'Erro ao desfazer glosa'
                })
            })
            .finally(() => setRequisicaoController((prev) => ({ ...prev, desfazerGlosaBtn: false })))
    }

    const handleDesaprovarItem = () => {
        setRequisicaoController((prev) => ({ ...prev, desfazerAprovacaoBtn: true }))
        AnaliseContasService.desaprovarItem(item?.uuid)
            .then(({ data }) => {
                carregarDetalhesItem(item?.uuid)
                // setStatus(data?.status)
                atualizaItensGuia(item?.tipo, data?.status)
            })
            .catch(() => {
                addToast({
                    type: 'error',
                    duration: 3000,
                    title: 'Erro ao desfazer aprovação'
                })
            })
            .finally(() => setRequisicaoController((prev) => ({ ...prev, desfazerAprovacaoBtn: false })))
    }

    const handleOpenItem = () => {
        setOpen(!isOpen)
    }

    const handleOpenModalMotivos = () => {
        setIsModalGlosaOpen(true)
    }

    const condition = useMemo(() => {
        const objectLiteral = {
            [StatusItemEnum.SEM_GLOSA]: {
                label: 'Desfazer aprovação',
                func: handleDesaprovarItem
            },
            [StatusItemEnum.RECURSADO]: {
                label: 'Desfazer aprovação',
                func: handleDesaprovarItem
            },
            [StatusItemEnum.INCOMPLETO]: {
                label: 'Glosar',
                func: () => console.log('incompleto')
            },
            [StatusItemEnum.AGUARDANDO_ANALISE]:
                formControl?.quantidadeAGlosar === 0 && detalhes?.calculosDeGlosa?.valorGlosado === 0
                    ? {
                          label: 'Aprovar',
                          func: handleAprovarItem
                      }
                    : {
                          label: 'Glosar',
                          func: handleOpenModalMotivos
                      },
            [StatusItemEnum.GLOSADO]: (() => {
                if (item?.status === 'GLOSADO' && formControl?.quantidadeAGlosar === 0) {
                    return {
                        label: 'Aprovar',
                        func: handleAprovarItem
                    }
                } else if (!validarStatusGuia()) {
                    return {
                        label: 'Glosar',
                        func: handleOpenModalMotivos
                    }
                } else {
                    return {
                        label: 'Desfazer glosa',
                        func: handleDesfazerGlosa
                    }
                }
            })()
        }

        return objectLiteral[item?.status]
    }, [item?.status, formControl, detalhes])

    const statusText = useMemo(() => statusOptions?.find((s) => s?.label === item?.status)?.text, [item?.status])
    const statusColor = useMemo(() => statusOptions?.find((s) => s?.label === item?.status)?.status, [item?.status])

    return (
        <>
            <S.CardItemGuia key={index}>
                <S.GlosaContainer>
                    <S.ContentItem onClick={handleOpenItem}>{`${item?.codigoItem} ${item?.descricaoItem}`}</S.ContentItem>

                    <S.ContainerStatus onClick={handleOpenItem}>
                        <Status text={statusText} status={statusColor} />

                        <S.IconButton>
                            <ReactSVG src="/faturamento/assets/icons/chevron.svg" style={{ transform: `rotate(${isOpen ? '0deg' : '180deg'})` }} />
                        </S.IconButton>
                    </S.ContainerStatus>
                </S.GlosaContainer>

                {isOpen && (
                    <S.GlosaContent>
                        <S.ChipsContainer>
                            {detalhes && detalhes?.calculosDeGlosa ? (
                                <>
                                    {(item?.tipo === 'PROCEDIMENTO' || item?.tipo === 'HONORARIO') && detalhes?.grauParticipacao && (
                                        <Chip
                                            text={
                                                grauParticipacaoOptions?.find((grau) => grau?.value === detalhes?.grauParticipacao)?.codigo +
                                                ' - ' +
                                                grauParticipacaoOptions?.find((grau) => grau?.value === detalhes?.grauParticipacao)?.descricao
                                            }
                                            label="Grau de participação"
                                        />
                                    )}
                                    {(item?.tipo === 'PROCEDIMENTO' || item?.tipo === 'HONORARIO') && formControl?.viaAcesso && (
                                        <Chip
                                            label="Via de acesso"
                                            options={viaAcessoOptions}
                                            visibleOptions={item?.status === 'SEM_GLOSA' || item?.status === 'GLOSADO'}
                                            handleClickOption={({ value }) => setFormControl((state) => ({ ...state, viaAcesso: value as string }))}
                                            text={viaAcessoOptions?.find((via) => via?.value === formControl?.viaAcesso)?.label}
                                        />
                                    )}
                                    {(item?.tipo === 'PROCEDIMENTO' || item?.tipo === 'HONORARIO') && (
                                        <Chip
                                            label="Horário especial"
                                            options={[
                                                { value: true, label: 'Sim' },
                                                { value: false, label: 'Não' }
                                            ]}
                                            text={formControl?.horarioEspecial ? 'Sim' : 'Não'}
                                            visibleOptions={
                                                item?.status === 'SEM_GLOSA' ||
                                                item?.status === 'GLOSADO' ||
                                                infoGeraisGuia?.etapaGuiaAnalise !== 'ANALISE_TECNICA'
                                            }
                                            handleClickOption={({ value }) => setFormControl((state) => ({ ...state, horarioEspecial: value }))}
                                        />
                                    )}
                                </>
                            ) : (
                                <Box sx={{ display: 'flex', width: '100%', justifyContent: 'center', alignItems: 'center', height: '30vh' }}>
                                    <CircularProgress />
                                </Box>
                            )}
                        </S.ChipsContainer>

                        {detalhes && (
                            <>
                                <S.ContainerInputs style={{ marginBottom: 16 }}>
                                    {loading ? (
                                        <Box sx={{ display: 'flex', width: '100%', justifyContent: 'center', alignItems: 'center', height: '30vh' }}>
                                            <CircularProgress />
                                        </Box>
                                    ) : (
                                        <div className="row">
                                            <div>
                                                <S.CardResult>
                                                    <p>Quantidade apresentada</p>
                                                    <span>{calculoGlosa?.calculosDeGlosa?.quantidadeApresentada}</span>
                                                </S.CardResult>
                                                <S.CardResult>
                                                    <p>Quantidade apurada</p>
                                                    <span>{calculoGlosa?.calculosDeGlosa?.quantidadeApurada}</span>
                                                </S.CardResult>
                                            </div>

                                            <div>
                                                <S.CardResult>
                                                    <p>Valor unitário apresentado</p>
                                                    <span>{NumberUtils.currencyMask(calculoGlosa?.calculosDeGlosa?.valorUnitarioApresentado)}</span>
                                                </S.CardResult>
                                                <S.CardResult>
                                                    <p>Valor unitário apurado</p>
                                                    <span>{NumberUtils.currencyMask(calculoGlosa?.calculosDeGlosa?.valorUnitarioApurado)}</span>
                                                </S.CardResult>
                                            </div>

                                            <div>
                                                <S.CardResult>
                                                    <p>Total apresentado</p>
                                                    <span>{NumberUtils.currencyMask(calculoGlosa?.calculosDeGlosa?.totalApresentado)}</span>
                                                </S.CardResult>
                                            </div>
                                        </div>
                                    )}
                                </S.ContainerInputs>

                                <S.ContainerInputs className="row">
                                    {validarStatusGuia() || item.status == 'SEM_GLOSA' ? (
                                        <ReadOnlyField
                                            disable
                                            label="Quantidade glosada"
                                            text={calculoGlosa?.calculosDeGlosa?.quantidadeAGlosar?.toString()}
                                        />
                                    ) : (
                                        <InputNumberQtd
                                            textAlign="center"
                                            label="Quantidade a glosar"
                                            count={formControl?.quantidadeAGlosar}
                                            handleOnChange={(value: any) => setFormControl((state) => ({ ...state, quantidadeAGlosar: value }))}
                                            minCount={detalhes?.calculosDeGlosa?.quantidadeAGlosar}
                                            maxCount={calculoGlosa?.calculosDeGlosa?.quantidadeApresentada || 0}
                                        />
                                    )}
                                    <ReadOnlyField
                                        label="Valor glosado"
                                        disable={calculoGlosa?.calculosDeGlosa?.valorGlosado === 0}
                                        text={NumberUtils.currencyMask(calculoGlosa?.calculosDeGlosa?.valorGlosado)}
                                    />
                                    <ReadOnlyField
                                        label="Total apurado"
                                        text={NumberUtils.currencyMask(calculoGlosa?.calculosDeGlosa?.totalApurado)}
                                    />
                                </S.ContainerInputs>

                                <S.BottomContainer>
                                    <div>
                                        <S.SeeDetails
                                            onClick={() => {
                                                setInfoModalOpen(true)
                                            }}
                                        >
                                            Ver detalhes
                                        </S.SeeDetails>
                                        <S.SeeDetails
                                            onClick={() => {
                                                setChatModalOpen(true)
                                            }}
                                        >
                                            Comentários
                                        </S.SeeDetails>
                                    </div>
                                    <S.WrapperButton>
                                        {permission && item?.status !== 'RECURSADO' && (
                                            <S.GlosaButton
                                                disabled={
                                                    (item?.status === 'SEM_GLOSA' && requisicaoController?.desfazerAprovacaoBtn) ||
                                                    (item?.status === 'AGUARDANDO_ANALISE' && requisicaoController?.aprovacaoBtn)
                                                }
                                                isEdit
                                                typeButton="ghost"
                                                onClick={condition.func}
                                                themeButton={'primary'}
                                            >
                                                {condition?.label}
                                            </S.GlosaButton>
                                        )}

                                        {/* {item?.status === 'GLOSADO' && formControl?.quantidadeAGlosar === 0 && (
                                            <>
                                                {!validStatusGuiar() ? (
                                                    <S.GlosaButton isEdit typeButton="ghost" onClick={handleOpenModalMotivos} themeButton={'primary'}>
                                                        Adicionar glosa
                                                    </S.GlosaButton>
                                                ) : (
                                                    <S.GlosaButton
                                                        disabled={requisicaoController?.desfazerGlosaBtn}
                                                        isEdit
                                                        typeButton="ghost"
                                                        onClick={handleDesfazerGlosa}
                                                        themeButton={'primary'}
                                                    >
                                                        Desfazer glosa
                                                    </S.GlosaButton>
                                                )}
                                            </>
                                        )} */}
                                    </S.WrapperButton>
                                </S.BottomContainer>
                            </>
                        )}
                    </S.GlosaContent>
                )}
            </S.CardItemGuia>

            <InfoItensModal
                detalhes={detalhes}
                calculoGlosa={calculoGlosa}
                isModalOpen={infoModalOpen}
                setIsModalOpen={() => setInfoModalOpen(!infoModalOpen)}
            />

            <ModalAddGlossReasons isOpen={isModalGlosaOpen} onConfirm={handleGlosarItem} onClose={() => setIsModalGlosaOpen(!isModalGlosaOpen)} />

            <ModalGenericComponent widthModal="70%" open={chatModalOpen} setOpenModal={setChatModalOpen} title="Comentários">
                <>
                    <div>
                        <S.SubtitleModal style={{ marginBottom: '8px' }}>Adicionar comentario</S.SubtitleModal>
                        <S.WrapperMenssage>
                            <TextField
                                value={mensageField}
                                fullWidth
                                onChange={({ target }) => {
                                    setMensageField(target.value)
                                }}
                            />
                            <Button
                                onClick={() => enviarMensagem()}
                                color="secondary"
                                endIcon={<ReactSVG src="/faturamento/assets/icons/send_icon.svg" />}
                            >
                                Enviar
                            </Button>
                        </S.WrapperMenssage>
                    </div>
                    <div>
                        {mensagens?.length > 0 ? (
                            <>
                                {loadingMensages ? (
                                    <S.ContainerLottie>
                                        <AnimatedLoadingLottie />
                                    </S.ContainerLottie>
                                ) : (
                                    <>
                                        <S.SubtitleModal>Historico de comentário</S.SubtitleModal>
                                        <S.wrapperChat>
                                            {mensagens?.map((mensagem, index) => (
                                                <S.MensageIcon
                                                    style={mensagem?.minhaMensagem ? { justifyContent: 'end' } : { justifyContent: 'start' }}
                                                    key={index}
                                                >
                                                    {!mensagem?.minhaMensagem && (
                                                        <S.ContainerImage
                                                            backgroundImage={
                                                                mensagem?.imageUrl ? mensagem?.imageUrl : '/faturamento/assets/icons/avatar.svg'
                                                            }
                                                        />
                                                    )}
                                                    <S.TratativaCard>
                                                        {!mensagem?.minhaMensagem && (
                                                            <>
                                                                <div className="card-header">
                                                                    <p>{mensagem?.nomeUsuario}</p>
                                                                </div>
                                                            </>
                                                        )}

                                                        <div className="card-header">
                                                            <p>{mensagem?.comentario}</p>
                                                        </div>
                                                        <div className="card-description">
                                                            <div style={{ display: 'flex', gap: '20px' }}>
                                                                <p>
                                                                    <strong>Data: </strong>
                                                                    {new Date(mensagem?.data).toLocaleDateString('pt-BR')}
                                                                </p>
                                                                <p>
                                                                    <strong>Hora: </strong>
                                                                    {new Date(mensagem?.data).toLocaleTimeString('pt-BR')}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </S.TratativaCard>
                                                </S.MensageIcon>
                                            ))}
                                        </S.wrapperChat>
                                    </>
                                )}

                                <S.WrapperPagination>
                                    <Pagination
                                        totalPage={pagination?.totalPaginas}
                                        totalRegister={pagination?.totalRegistros}
                                        actualPage={pagination?.paginaAtual}
                                        setNumberPage={pagination?.setNumberPage}
                                    />

                                    <p>
                                        {pagination?.paginaAtual * pagination?.linhasPorPagina + 1} -{' '}
                                        {mensagens?.length + pagination?.paginaAtual * pagination?.linhasPorPagina}
                                        {' de '}
                                        {pagination?.totalRegistros}
                                    </p>
                                </S.WrapperPagination>
                            </>
                        ) : null}
                    </div>
                </>
            </ModalGenericComponent>
        </>
    )
}

export default CardItemGuia
