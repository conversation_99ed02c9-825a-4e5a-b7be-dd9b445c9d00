import { ObjectUtils } from 'utils/objectUtils'
import { apiProcessadorRegras } from 'src/services/apis/apiProcessadorRegras'
import { IPageResult, ISortPage } from 'types/pagination'
import { IExclusaoRegraPrestadorDTO, IExclusaoRegraPrestadorForm, IGetRegrasProps, IRegraDTO, IRegraForm, IRegrasImplementadasDTO } from './types'

const baseUrl = '/regras'

export class Regras {
    static async get(props?: IGetRegrasProps & ISortPage) {
        const params = ObjectUtils.propsToParams(props)
        return apiProcessadorRegras.get<IPageResult<IRegraDTO>>(`${baseUrl}?${params}`)
    }

    static async getRegrasDisponiveis(props?: { identificadorOuDescricao?: string } & ISortPage) {
        const params = ObjectUtils.propsToParams(props)
        return apiProcessadorRegras.get<IPageResult<IRegrasImplementadasDTO>>(`${baseUrl}/disponiveis?${params}`)
    }

    static async post(data: IRegraForm) {
        return apiProcessadorRegras.post<IRegraDTO>(`${baseUrl}`, data)
    }
    static async patch(uuid: string) {
        return apiProcessadorRegras.patch<IRegraDTO>(`${baseUrl}/${uuid}`)
    }

    static async getRegraById(uuid: string) {
        return apiProcessadorRegras.get<IRegraDTO>(`${baseUrl}/${uuid}`)
    }

    static async getPrestadoresById(uuid: string) {
        return apiProcessadorRegras.get<IPageResult<IExclusaoRegraPrestadorDTO>>(`${baseUrl}/${uuid}/prestadores-dispensados`)
    }

    static async postPrestadoresById(uuid: string, data: IExclusaoRegraPrestadorForm[]) {
        return apiProcessadorRegras.post<IExclusaoRegraPrestadorDTO[]>(`${baseUrl}/${uuid}/prestadores-dispensados`, data)
    }

    static async deletePrestadoresById(
        uuid: string,
        data: {
            prestadorId?: string
        }[]
    ) {
        return apiProcessadorRegras.delete<IExclusaoRegraPrestadorDTO[]>(`${baseUrl}/${uuid}/prestadores-dispensados`, {
            data
        })
    }
}
