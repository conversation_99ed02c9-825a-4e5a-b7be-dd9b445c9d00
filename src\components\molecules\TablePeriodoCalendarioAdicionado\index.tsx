/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState } from 'react'
import { ReactSVG } from 'react-svg'
import Modal from 'components/atoms/Modal'
import Button from 'components/atoms/Button'
import moment from 'moment'

import { typefieldsNewPeriod } from 'components/organisms/FormsRequest/FormAddPeriod'

import * as S from './styles'

type TableProcedureItemsProps = {
    period: typefieldsNewPeriod[]
    removeItem: () => void
}

function returnDay(str: string) {
    const day = moment(str).date()
    return day
}

const TableAddedPeriod = ({ removeItem, period }: TableProcedureItemsProps) => {
    const [isModalOpen, setIsModalOpen] = useState(false)

    return (
        <div>
            <S.Table>
                <thead>
                    <tr>
                        <th className="first-column">Descrição</th>
                        <th>Dia Início</th>
                        <th>Dia Final</th>
                        {/* <th>Data limite</th> */}
                        <th></th>
                        <th></th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    {period.map((item, index) => (
                        <S.TabContent key={index}>
                            <td className="first-column">{`Período ${index + 1}`}</td>
                            <td>{item?.diaInicio}</td>
                            <td> {item?.diaFim}</td>
                            {/* <td>{item.diaFinal}</td> */}
                            <td></td>
                            <td></td>
                            <td className="last-column">
                                <S.buttonBin>
                                    {' '}
                                    <ReactSVG
                                        src="/faturamento/assets/icons/trash-blue.svg"
                                        wrapper="div"
                                        className="iconLeftBtn"
                                        alt="icon"
                                        aria-label="iconLabel"
                                        onClick={() => {
                                            setIsModalOpen(true)
                                        }}
                                    />
                                </S.buttonBin>
                            </td>
                        </S.TabContent>
                    ))}
                </tbody>
            </S.Table>
            <Modal isOpen={isModalOpen} style={{ width: '45vw' }} onClose={() => setIsModalOpen(false)}>
                <S.ModalContainer>
                    <S.ModalTitle>Deletar período?</S.ModalTitle>
                    <S.ModalSubtitle>Tem certeza que deseja deletar este período?</S.ModalSubtitle>
                    <div>
                        <S.ContainerButtons>
                            <Button
                                typeButton="text"
                                style={{
                                    backgroundColor: 'transparent',
                                    borderColor: 'gray',
                                    color: 'black',
                                    width: '40%'
                                }}
                                className="Button back"
                                onClick={() => setIsModalOpen(false)}
                            >
                                Cancelar
                            </Button>
                            <Button
                                className="btn-cancelar-guia"
                                themeButton="danger"
                                style={{
                                    width: '30%'
                                }}
                                onClick={() => {
                                    removeItem()
                                    setIsModalOpen(false)
                                }}
                                iconLeft="/faturamento/assets/icons/mai-ic-close.mono.svg"
                            >
                                Confirmar
                            </Button>
                        </S.ContainerButtons>
                    </div>
                </S.ModalContainer>
            </Modal>
        </div>
    )
}

export default TableAddedPeriod
