<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_1479_30876)">
<path d="M10 26C10 14.9543 18.9543 6 30 6C41.0457 6 50 14.9543 50 26C50 37.0457 41.0457 46 30 46C18.9543 46 10 37.0457 10 26Z" fill="#FFCC00"/>
<path d="M27.8 30.5004C24.1 30.5004 21 27.5004 21 23.7004C21 20.0004 24 16.9004 27.8 16.9004C31.5 16.9004 34.6 19.9004 34.6 23.7004C34.5 27.5004 31.5 30.5004 27.8 30.5004ZM27.8 28.6004C30.5 28.6004 32.6 26.4004 32.6 23.8004C32.6 21.1004 30.4 19.0004 27.8 19.0004C25.1 19.0004 23 21.2004 23 23.8004C22.9 26.4004 25.1 28.6004 27.8 28.6004ZM34.6 29.3004L38.7 33.4004C39.1 33.8004 39.1 34.4004 38.7 34.8004C38.3 35.2004 37.7 35.2004 37.3 34.8004L33.2 30.7004C32.8 30.3004 32.8 29.7004 33.2 29.3004C33.6 28.9004 34.2 28.9004 34.6 29.3004Z" fill="black" fill-opacity="0.88"/>
</g>
<defs>
<filter id="filter0_d_1479_30876" x="0" y="0" width="60" height="60" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1479_30876"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1479_30876" result="shape"/>
</filter>
</defs>
</svg>
