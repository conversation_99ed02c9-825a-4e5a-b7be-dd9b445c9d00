import React, { useState } from 'react'
import { useRouter } from 'next/router'
import Input from 'components/atoms/Input'
import Button from 'components/atoms/Button'
import Layout from 'components/molecules/Layout'
import FooterButtons from 'components/atoms/FooterButtons'
import Select from 'components/molecules/Select'
import TableAddedPeriod from 'components/molecules/TablePeriodoCalendarioAdicionado'
import DividerSectionCard from 'components/atoms/DividerSectionCard'
import { useToast } from 'src/hooks/toast'
import { FormAddPeriod } from 'components/organisms/FormsRequest/FormAddPeriod'
import * as S from './styles'
import TitleSection from 'components/atoms/TitleSection'
import { defaultSelectOption, selectProps } from 'types/common/select'
import { PrestadorService } from 'src/services/faturamentoOperadoraApi/prestadorServices'
import AsyncSimpleSelect from 'components/molecules/AsyncSimpleSelect'
import { CalendarioService } from 'src/services/analiseContasApi/Calendario'
import { moduloEnum, tipoCalendarioEnum } from 'src/services/analiseContasApi/Calendario/enums'
import { ICalendarioDTO, ICalendarioForm, ICompetenciaCalendarioDTO } from 'src/services/analiseContasApi/Calendario/types'
import { CalendarioRecursoGlosaService } from 'src/services/analiseContasApi/CalendarioRecursoGlosa'

interface props {
    id?: string
}

const getCompetenciasAnoVigente = () => {
    const anoAtual = new Date().getFullYear()
    const meses = [
        { mes: 'JAN.', numero: '01' },
        { mes: 'FEV.', numero: '02' },
        { mes: 'MAR.', numero: '03' },
        { mes: 'ABR.', numero: '04' },
        { mes: 'MAI.', numero: '05' },
        { mes: 'JUN.', numero: '06' },
        { mes: 'JUL.', numero: '07' },
        { mes: 'AGO.', numero: '08' },
        { mes: 'SET.', numero: '09' },
        { mes: 'OUT.', numero: '10' },
        { mes: 'NOV.', numero: '11' },
        { mes: 'DEZ.', numero: '12' }
    ]
    return meses.map((m, idx) => {
        const dataInicioEnvio = `${anoAtual}-${m.numero}-01`
        const proximoMes = idx === 11 ? 1 : idx + 2
        const proximoAno = idx === 11 ? anoAtual + 1 : anoAtual
        const dataLimiteEnvio = `${proximoAno}-${proximoMes.toString().padStart(2, '0')}-01`
        return {
            dataInicioEnvio,
            dataLimiteEnvio,
            competencia: dataInicioEnvio,
            mesCompetencia: m.mes
        }
    })
}

const competencias: { label: string; value: ICompetenciaCalendarioDTO }[] = getCompetenciasAnoVigente().map((element) => ({
    label: element?.mesCompetencia,
    value: element as unknown as ICompetenciaCalendarioDTO
}))

const CalendarioCriarEditarTemplate = ({ id }: props) => {
    const router = useRouter()
    const [isRequired, setIsRequired] = React.useState(true)
    const [formCaledario, setFormCalendario] = React.useState<ICalendarioDTO>()
    const [prestador, setPrestador] = useState<selectProps>(defaultSelectOption)
    const { addToast } = useToast()
    const [adicionarPeriodo, setAdicionarPeriodo] = useState<boolean>(false)
    const [competenciaSelecionada, setCompetenciaSelecionada] = useState<ICompetenciaCalendarioDTO>()

    async function loadPrestador(filter: string) {
        setPrestador({ label: '', value: '' } as selectProps)

        return PrestadorService.getPrestador({
            page: 0,
            size: 20,
            nomeCompletoNomeFantasiaRazaoSocialCpfCnpj: filter
            //nomeCompletoNomeFantasia: filter
        }).then(({ data }) => {
            return data?.content?.map((prestador) => {
                return {
                    value: prestador?.uuid,
                    label:
                        prestador?.cnpj != null ? `${prestador?.cnpj} - ${prestador?.razaoSocial}` : `${prestador?.cpf} - ${prestador?.nomeCompleto}`
                }
            })
        })
    }

    function handleSubmit(props: ICalendarioDTO) {
        const payload: ICalendarioForm = { ...props, diasEmAberto: props?.prazoCobranca }

        if (id) {
            CalendarioService.put({ id, calendario: payload })
                .then(() => {
                    addToast({ type: 'success', title: 'Calendário atualizado com sucesso !' })
                    router.back()
                })
                .catch((error) => {
                    const messageError = error?.response?.data?.message

                    const message = messageError ? 'Erro ao atualizar o calendário: ' + messageError : 'Erro ao atualizar o calendário'

                    addToast({ type: 'error', title: message, duration: 3000 })
                })
        } else {
            if (formCaledario?.modulo === moduloEnum.RECURSO) {
                CalendarioRecursoGlosaService.post({
                    competencia: competenciaSelecionada?.competencia,
                    dataInicio: competenciaSelecionada?.dataInicioEnvio,
                    prestadorId: formCaledario?.prestadorId
                })
                    .then(() => {
                        addToast({ type: 'success', title: 'Calendário criado com sucesso !' })
                        router.back()
                    })
                    .catch((error) => {
                        const messageError = error?.response?.data?.message

                        const message = messageError ? 'Erro ao criar o calendário: ' + messageError : 'Erro ao criar o calendário'

                        addToast({ type: 'error', title: message, duration: 3000 })
                    })
            } else {
                CalendarioService.post(payload)
                    .then(() => {
                        addToast({ type: 'success', title: 'Calendário criado com sucesso !' })
                        router.back()
                    })
                    .catch((error) => {
                        const messageError = error?.response?.data?.message

                        const message = messageError ? 'Erro ao criar o calendário: ' + messageError : 'Erro ao criar o calendário'

                        addToast({ type: 'error', title: message, duration: 3000 })
                    })
            }
        }
    }

    React.useEffect(() => {
        if (formCaledario?.modulo === moduloEnum.RECURSO && competenciaSelecionada) {
            setIsRequired(false)
            return
        }
        if (formCaledario?.modulo && formCaledario?.prazoCobranca && formCaledario?.tipoCalendario && !!formCaledario?.diaInicio) {
            setIsRequired(false)
            return
        }
        setIsRequired(true)
    }, [formCaledario, competenciaSelecionada])

    React.useEffect(() => {
        if (id) {
            CalendarioService.getByID({ id }).then(({ data }) => {
                setFormCalendario(data)
                PrestadorService.getByID(data?.prestadorId).then((prestador) => {
                    setPrestador({
                        value: prestador?.data?.uuid,
                        label:
                            prestador?.data?.cnpj != null
                                ? `${prestador?.data?.cnpj} - ${prestador?.data?.razaoSocial}`
                                : `${prestador?.data?.cpf} - ${prestador?.data?.nomeCompleto}`
                    })
                })
            })
        }
    }, [id])

    return (
        <Layout isLoggedIn={true} title={id ? 'Alterar Calendário' : 'Novo Calendário'}>
            <DividerSectionCard>
                <TitleSection>
                    PRESTADOR<b>*</b>
                </TitleSection>
                <S.ContainerInputs>
                    <div className="row">
                        <Select
                            className="rule-name"
                            label="Tipo"
                            style={{ width: '23%' }}
                            value={[
                                {
                                    value: 'GERAL',
                                    label: 'Todos'
                                },
                                {
                                    value: 'ESPECIFICO',

                                    label: 'Específico'
                                }
                            ]?.find((item) => item?.value === formCaledario?.tipoCalendario)}
                            defaultValue={''}
                            options={[
                                {
                                    value: 'GERAL',
                                    label: 'Todos'
                                },
                                {
                                    value: 'ESPECIFICO',

                                    label: 'Específico'
                                }
                            ]}
                            onChange={(e) => setFormCalendario((prev) => ({ ...prev, tipoCalendario: tipoCalendarioEnum[e?.value] }))}
                        />
                        {formCaledario?.tipoCalendario === 'ESPECIFICO' ? (
                            <AsyncSimpleSelect
                                defaultValue={prestador}
                                value={prestador}
                                onChange={(e) => {
                                    setPrestador(e)
                                    setFormCalendario((prev) => ({ ...prev, prestadorId: e?.value }))
                                }}
                                required
                                label="Contrato, CNPJ ou Razão Social"
                                loadOptions={loadPrestador}
                                defaultOptions={true}
                                isClearable
                            />
                        ) : null}
                    </div>
                </S.ContainerInputs>
            </DividerSectionCard>

            <DividerSectionCard style={{ marginTop: '24px' }}>
                <TitleSection>
                    INFORMAÇÕES GERAIS <b>*</b>
                </TitleSection>
                <S.ContainerInputs>
                    <S.WrapperSititle>
                        <S.SubTitle>Por padrão todos os lotes poderão conter a quantidade máxima de 100 guias.</S.SubTitle>
                    </S.WrapperSititle>
                    <div className="row">
                        <Select
                            className="medical-bills"
                            label="Tipo de faturamento"
                            value={[
                                {
                                    value: moduloEnum.ODONTO,
                                    label: 'Odonto'
                                },
                                {
                                    value: moduloEnum.MEDICO,
                                    label: 'Medico'
                                },
                                {
                                    value: moduloEnum.RECURSO,

                                    label: 'Recurso de Glosa'
                                }
                            ]?.find((item) => item?.value === formCaledario?.modulo)}
                            defaultValue={''}
                            options={[
                                {
                                    value: moduloEnum.ODONTO,
                                    label: 'Odonto'
                                },
                                {
                                    value: moduloEnum.MEDICO,
                                    label: 'Medico'
                                },
                                {
                                    value: moduloEnum.RECURSO,

                                    label: 'Recurso de Glosa'
                                }
                            ]?.filter((element) => (id ? element?.value !== moduloEnum.RECURSO : element))}
                            onChange={(e) => setFormCalendario((prev) => ({ ...prev, modulo: moduloEnum[e?.value] }))}
                        />

                        {formCaledario?.modulo === moduloEnum.RECURSO ? (
                            <Select
                                className="rule-name"
                                label="Competência"
                                required
                                style={{ width: '23%' }}
                                value={competencias?.find((item) => item?.value === competenciaSelecionada)}
                                defaultValue={''}
                                options={competencias}
                                onChange={(e) => setCompetenciaSelecionada(e?.value as unknown as ICompetenciaCalendarioDTO)}
                            />
                        ) : (
                            <Input
                                className="rule-name"
                                isDefault="default"
                                label="Data limite de atendimento em dias"
                                placeholder=""
                                type="number"
                                maxLength={4}
                                value={formCaledario?.prazoCobranca?.toString()}
                                initialValue={formCaledario?.prazoCobranca}
                                handleOnChange={(e) => setFormCalendario((prev) => ({ ...prev, prazoCobranca: e }))}
                            />
                        )}
                    </div>
                </S.ContainerInputs>
            </DividerSectionCard>

            {formCaledario?.modulo !== moduloEnum.RECURSO && (
                <DividerSectionCard dividerContent={true}>
                    <S.PeriodHeader>
                        <TitleSection>
                            PERÍODO <b>*</b>
                        </TitleSection>
                        <Button
                            style={{ width: '20%' }}
                            positionButton="center"
                            typeButton={'ghost'}
                            themeButton={'ihealth'}
                            disabled={formCaledario?.diaInicio ? true : false}
                            iconLeft={'/faturamento/assets/icons/plus-blue.svg'}
                            onClick={() => setAdicionarPeriodo(true)}
                        >
                            Inserir período
                        </Button>
                    </S.PeriodHeader>

                    {adicionarPeriodo && (
                        <FormAddPeriod
                            onCreatPeriod={(data) => {
                                setFormCalendario({ ...formCaledario, diaInicio: data?.diaInicio, diaFim: data?.diaFim })
                            }}
                            visible={setAdicionarPeriodo}
                            contador={1}
                        />
                    )}

                    {formCaledario?.diaInicio !== undefined && formCaledario?.diaFim !== undefined && (
                        <TableAddedPeriod
                            removeItem={() => setFormCalendario({ ...formCaledario, diaInicio: undefined, diaFim: undefined })}
                            period={[
                                {
                                    descricao: 'Periodo 01',
                                    diaInicio: formCaledario?.diaInicio,
                                    diaFim: formCaledario?.diaFim,
                                    id: '1'
                                }
                            ]}
                        />
                    )}
                </DividerSectionCard>
            )}

            <FooterButtons>
                <Button
                    typeButton={'text'}
                    style={{ width: '15%', color: 'black' }}
                    onClick={() => {
                        router.push('/calendario-faturamento')
                    }}
                >
                    Cancelar
                </Button>
                <Button themeButton={'warning'} style={{ width: '16%' }} disabled={isRequired} onClick={() => handleSubmit(formCaledario)}>
                    {id ? 'Salvar alterações' : 'Criar calendário'}
                </Button>
            </FooterButtons>
        </Layout>
    )
}

export default CalendarioCriarEditarTemplate
