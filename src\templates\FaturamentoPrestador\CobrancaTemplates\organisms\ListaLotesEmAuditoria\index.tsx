import DropLote from 'components/molecules/DropLote'
import React, { useEffect, useState } from 'react'
import * as S from './styles'

import NoContent from 'components/molecules/NoContent'
import Pagination from 'components/molecules/Pagination'
import moment from 'moment'
import { useRouter } from 'next/router'
import { ReactSVG } from 'react-svg'
import { useAuth } from 'src/hooks/auth'
import { useToast } from 'src/hooks/toast'
import { CobrancaServices, IGetCobrancaProps } from 'src/services/faturamentoPrestadorApi/cobrancaServices'
import { ILoteCobrancaDTO, IPageLote } from 'types/cobrancaPrestador/loteCobranca'
import { IPrestadorAnaliseQuery } from 'types/cobrancaPrestador/prestadorAnalise'
import { IPagination } from 'types/common/pagination'
import { PaginationHelper } from 'utils/helpers/paginationHelper'
import { NumberUtils } from 'utils/numberUtils'
import { capitalize } from 'utils/stringUtils'
import DropLoteCard from '../DropLoteCards'

type ListaLotesEmAnaliseProps = {
    // data: ILoteCobrancaDTO[]
    competenciaSelecionada: IPrestadorAnaliseQuery
    searchLote: string
    forceUpdate: boolean
    setLotes: React.Dispatch<React.SetStateAction<IPageLote>>
    lotes: IPageLote
}

const ListaLotesEmAuditoria = ({ competenciaSelecionada, searchLote, forceUpdate, setLotes, lotes }: ListaLotesEmAnaliseProps) => {
    const { addToast } = useToast()
    const route = useRouter()
    const { prestadorVinculado } = useAuth()

    const dropsLots: React.ReactNode = []

    // const [pageLotes, setPageLotes] = useState<IPageLote>()
    const [pagination, setPagination] = useState<IPagination>()
    const [numberPage, setNumberPage] = useState(0)

    const labels: string[] = ['Lote', 'Valor apresentado', 'Valor glosado', 'Valor apurado', 'Envio']

    function createDropLot(item: ILoteCobrancaDTO) {
        return [
            {
                component: (
                    <div>
                        <p>{item?.numeroLote}</p>
                    </div>
                )
            },
            {
                component: (
                    <div>
                        <p>{NumberUtils.maskMoney(item?.valorApresentado)}</p>
                    </div>
                )
            },
            {
                component: (
                    <div>
                        <p>valor_glosado</p>
                    </div>
                )
            },
            {
                component: (
                    <div>
                        <p>valor_apurado</p>
                    </div>
                )
            },
            {
                component: (
                    <div>
                        <p>{item?.tipoEnvio === 'ELETRONICO' ? 'Eletrônico' : capitalize(item?.tipoEnvio)}</p>
                    </div>
                )
            }
        ]
    }

    // const carregarLotes = useCallback(
    //     (filter?: string, page?: number) => {
    //         const getProps: IGetCobrancaProps =
    //             filter === ''
    //                 ? {
    //                       size: 10,
    //                       page: page || 0
    //                   }
    //                 : {
    //                       size: 10,
    //                       page: page || 0,
    //                       filtroNumeroLote: filter
    //                   }

    //         if (competenciaSelecionada?.competencia && prestadorVinculado?.uuid) {
    //             CobrancaServices.getLotes({
    //                 competencia: competenciaSelecionada?.competencia,
    //                 idPrestador: prestadorVinculado.uuid,
    //                 situacao: situacaoLoteEnum.EM_AUDITORIA,
    //                 ...getProps
    //             }).then(({ data }) => {
    //                 setPageLotes(data)
    //                 // initLotesChecks(data.content)

    //                 const objectPagination = PaginationHelper.parserPagination<ILoteCobrancaDTO>(data, setNumberPage)
    //                 setPagination(objectPagination)
    //             })
    //         }
    //     },
    //     [competenciaSelecionada, prestadorVinculado]
    // )

    const handleDownloadXML = (idLote: string) => {
        CobrancaServices.getProtocoloRecebimentoXml(idLote)
            .then((response) => {
                const fileName = response.headers['content-disposition'].split('filename=')[1] ?? 'download.xml'
                const url = window.URL.createObjectURL(new Blob([response.data]))
                const link = document.createElement('a')
                link.href = url
                link.setAttribute('download', fileName) //or any other extension
                document.body.appendChild(link)
                link.click()
            })
            .catch(() => {
                addToast({ title: 'Erro ao fazer download', type: 'error', duration: 3000 })
            })
    }
    const handleDownloadPDF = (idLote: string) => {
        CobrancaServices.getProtocoloRecebimentoPdf(idLote)
            .then((response) => {
                const fileName = response.headers['content-disposition'].split('filename=')[1] ?? 'download.pdf'
                const url = window.URL.createObjectURL(response.data)
                const link = document.createElement('a')
                link.href = url
                link.setAttribute('download', fileName) //or any other extension
                document.body.appendChild(link)
                link.click()
            })
            .catch(() => {
                addToast({ title: 'Erro ao fazer download', type: 'error', duration: 3000 })
            })
    }

    const carregarLotes = (filter?: string, page?: number) => {
        const getProps: IGetCobrancaProps =
            filter === ''
                ? {
                      size: 10,
                      page: page || 0
                  }
                : {
                      size: 10,
                      page: page || 0,
                      filtroNumeroLote: searchLote
                  }

        CobrancaServices.getLotes(competenciaSelecionada?.competencia, prestadorVinculado.uuid, 'EM_AUDITORIA', getProps).then(({ data }) => {
            setLotes(data)
            // initLotesChecks(data.content)

            const objectPagination = PaginationHelper.parserPagination<ILoteCobrancaDTO>(data, setNumberPage)
            setPagination(objectPagination)
        })
    }

    useEffect(() => {
        carregarLotes(searchLote, numberPage)
    }, [numberPage, competenciaSelecionada])

    return (
        <S.Wrapper>
            {!lotes || lotes?.content.length === 0 ? (
                <NoContent title="No momento não existe nenhum lote em auditoria" path="/parametros/regras-de-excludencia/nova-regra" />
            ) : (
                <>
                    <div className="contentInput">
                        <div className="contentNumber">
                            <span className="pageNumber">
                                {pagination?.paginaAtual * pagination?.linhasPorPagina + 1} -{' '}
                                {lotes?.content?.length + pagination?.paginaAtual * pagination?.linhasPorPagina}
                                {' de '}
                                {pagination?.totalRegistros}
                            </span>
                        </div>
                    </div>
                    <S.HeaderLabel>
                        {labels.map((item, index) => (
                            <div key={index}>
                                <p>{item}</p>
                            </div>
                        ))}
                    </S.HeaderLabel>
                    {lotes?.content?.map((lote: ILoteCobrancaDTO, index) => {
                        return (
                            <DropLote items={createDropLot(lote)} key={index}>
                                <S.ContentDropLot>
                                    <div className="row1">
                                        <DropLoteCard
                                            iconSrc="/faturamento/assets/icons/blueHuman.svg"
                                            title="Lote enviado por"
                                            value="Leslie Alexander"
                                        />
                                        <DropLoteCard
                                            iconSrc="/faturamento/assets/icons/calender.svg"
                                            title="Data de envio"
                                            value={moment(lote?.dataEnvio).format('DD/MM/YYYY [-] HH:mm')}
                                        />
                                        <DropLoteCard
                                            iconSrc="/faturamento/assets/icons/calender.svg"
                                            title="Data de processamento"
                                            value="08/12/2022 - 00:00"
                                        />
                                        <DropLoteCard
                                            iconSrc="/faturamento/assets/icons/calender.svg"
                                            title="Período para recursar"
                                            value="Aguardando demonstrativo"
                                        />
                                    </div>
                                    <div className="row1_1">
                                        <DropLoteCard
                                            iconSrc="/faturamento/assets/icons/calender.svg"
                                            title="Data de envio do recurso"
                                            value="08/12/2022 - 00:00"
                                        />
                                        <DropLoteCard
                                            blueValue
                                            iconSrc="/faturamento/assets/icons/listMany.svg"
                                            title="Lote de recurso de glosa"
                                            value="5001"
                                        />
                                    </div>
                                    <div className="row2">
                                        <S.ProtocolWrapper>
                                            <p>Protocolo de recebimento</p>
                                            <S.FileWrapper>
                                                {lote?.tipoEnvio !== 'MANUAL' && (
                                                    <S.FileCard>
                                                        <ReactSVG src="/faturamento/assets/icons/download_blue.svg" />
                                                        <p onClick={() => handleDownloadXML(lote?.loteCobrancaId)}>Baixar XML</p>
                                                    </S.FileCard>
                                                )}

                                                <S.FileCard>
                                                    <ReactSVG src="/faturamento/assets/icons/download_blue.svg" />
                                                    <p onClick={() => handleDownloadPDF(lote?.loteCobrancaId)}>Baixar PDF</p>
                                                </S.FileCard>
                                            </S.FileWrapper>
                                        </S.ProtocolWrapper>
                                        <S.ResourceWrapper onClick={() => route.push(`/prestador/medico/cobranca/lotes/${lote?.loteCobrancaId}`)}>
                                            <ReactSVG src="/faturamento/assets/icons/glosa_icon.svg" />
                                            <p>Recursar glosa</p>
                                        </S.ResourceWrapper>
                                    </div>
                                </S.ContentDropLot>
                                {/* <S.ContentDropLot>
                                    <p>Protocolo de Recebimento</p>
                                    <S.RowButtons>
                                        {lote.tipoEnvio !== 'MANUAL' && (
                                            <ButtonDropLots icon={'/faturamento/assets/icons/download.svg'}>
                                                <span onClick={() => handleDownloadXML(lote.loteCobrancaId)}>Baixar XML</span>
                                            </ButtonDropLots>
                                        )}
                                        <ButtonDropLots icon={'/faturamento/assets/icons/download.svg'}>
                                            <span onClick={() => handleDownloadPDF(lote.loteCobrancaId)}>Baixar PDF</span>
                                        </ButtonDropLots>
                                    </S.RowButtons>
                                </S.ContentDropLot> */}
                            </DropLote>
                        )
                    })}
                    <div style={{ marginTop: '40px' }}>
                        <Pagination
                            totalPage={pagination?.totalPaginas}
                            totalRegister={pagination?.totalRegistros}
                            actualPage={pagination?.paginaAtual}
                            setNumberPage={pagination?.setNumberPage}
                        />
                    </div>
                </>
            )}
        </S.Wrapper>
    )
}

export default ListaLotesEmAuditoria
