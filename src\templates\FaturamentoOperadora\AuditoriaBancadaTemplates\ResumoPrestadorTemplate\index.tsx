import * as S from './styles'
import Title from 'components/atoms/Title'
import Button from 'components/atoms/Button'
import DividerSectionCard from 'components/atoms/DividerSectionCard'
import TitleSection from 'components/atoms/TitleSection'
import ContentDataInfo from 'components/molecules/ShowDataConfirm'
import { NumberUtils } from 'utils/numberUtils'
import CardsFilter from 'components/molecules/GuideGlosaFilter'
import { useEffect, useState, useCallback } from 'react'
import SearchBar from 'components/molecules/SearchBar'
import { ReactSVG } from 'react-svg'
import Item from 'components/atoms/Item'
import Pagination from 'components/molecules/Pagination'
import { IPagination } from 'types/common/pagination'
import { AuditoriaService } from 'src/services/analiseContasApi/auditoriaServices'
import { IResumoPrestadorQuery } from 'types/analiseContas/resumoPrestador'
import { IGetPropsLotesAuditoria, ILoteAuditoriaQuery, IPageLotesAuditoria } from 'types/auditoria/lotes'
import { ILoteQuery, IPageLotes } from 'types/analiseContas/lotes'
import { PaginationHelper } from 'utils/helpers/paginationHelper'
import { PrestadorService } from 'src/services/faturamentoOperadoraApi/prestadorServices'
import { IPrestadorQuery } from 'types/faturamentoOperadora/provider'
import { useToast } from 'src/hooks/toast'
import { useRouter } from 'next/router'
import NoContent from 'components/molecules/NoContent'

const ResumoPrestadorTemplate = ({ idPrestador, competencia }) => {
    const [resumo, setResumo] = useState<IResumoPrestadorQuery>()
    const [pageLotes, setPageLotes] = useState<IPageLotesAuditoria>()
    const [filterFields, setFilterFields] = useState([])
    const [filterSelected, setFilterSelected] = useState('EM_AUDITORIA')
    const [situacoes, setSituacoes] = useState([])
    const [prestador, setPrestador] = useState<IPrestadorQuery>()

    const [pagination, setPagination] = useState<IPagination>()
    const [numberPage, setNumberPage] = useState<number>(0)
    const [filtro, setFiltro] = useState<string>()

    const { addToast } = useToast()
    const route = useRouter()

    const getItemSituacao = (situacao: string) => {
        console.log(situacao)
        return situacoes?.some((i) => i.atributo === situacao)
            ? situacoes?.find((i) => i.atributo === situacao)
            : { porcentagem: 0, quantidadeDeLotes: 0 }
    }

    useEffect(() => {
        PrestadorService.getByID(idPrestador)
            .then(({ data }) => setPrestador(data))
            .catch((response) => {
                addToast({
                    title: 'Erro ao encontrar prestador',
                    type: 'error',
                    duration: 3000
                })
            })
    }, [])

    useEffect(() => {
        if (!idPrestador || !competencia) return

        AuditoriaService.getResumoPrestador(competencia, idPrestador).then(({ data }) => setResumo(data))
        AuditoriaService.getSituacoesLotes(idPrestador, competencia).then(({ data }) => setSituacoes(data))
    }, [idPrestador])

    useEffect(() => {
        const { porcentagem: percEmAuditoria, quantidadeDeLotes: qtdEmAuditoria } = getItemSituacao('EM_AUDITORIA')
        const { porcentagem: percAnalisados, quantidadeDeLotes: qtdAnalisados } = getItemSituacao('FECHADO')

        setFilterFields([
            { name: 'Em auditoria', value: `${qtdEmAuditoria || '0'} - (${percEmAuditoria || '0'}%)`, situacao: 'EM_AUDITORIA' },
            { name: 'Analisados', value: `${qtdAnalisados || '0'} - (${percAnalisados || '0'}%)`, situacao: 'FECHADO' }
        ])
    }, [situacoes])

    const carregarLotes = useCallback(
        (page?: number, filter?: string) => {
            const getProps: IGetPropsLotesAuditoria = filter
                ? {
                      competencia,
                      size: 10,
                      page: page || 0,
                      situacao: filterSelected,
                      filtro: filter
                  }
                : {
                      competencia,
                      size: 10,
                      page: page || 0,
                      situacao: filterSelected
                  }

            AuditoriaService.getLotes(idPrestador, getProps).then(({ data }) => {
                setPageLotes(data)

                const objectPagination = PaginationHelper.parserPagination<ILoteAuditoriaQuery>(data, setNumberPage)
                setPagination(objectPagination)
            })
        },
        [filterSelected]
    )

    useEffect(() => {
        carregarLotes()
    }, [carregarLotes])

    useEffect(() => {
        carregarLotes(numberPage)
    }, [numberPage])

    const handleOnSearch = () => {
        carregarLotes(numberPage, filtro)
    }

    const handleOnClose = () => {
        setFiltro('')
        carregarLotes(numberPage)
    }

    return (
        <>
            <Title>{prestador?.nomeFantasia}</Title>
            <DividerSectionCard>
                <S.Header>
                    <Button themeButton={'warning'} style={{ width: '20%' }} onClick={() => console.log('Hi')}>
                        Encerrar auditoria
                    </Button>
                </S.Header>
                <TitleSection style={{ paddingTop: '24px' }}>Valores</TitleSection>
                <S.Header>
                    <S.ContentData>
                        <ContentDataInfo
                            label={'Apresentado'}
                            value={!resumo?.valorApresentado ? 'R$ 0,00' : NumberUtils.maskMoney(resumo?.valorApresentado)}
                            background={'grey'}
                            standart={'expanded'}
                        />
                        <ContentDataInfo
                            label={'Apurado'}
                            value={!resumo?.valorApurado ? 'R$ 0,00' : NumberUtils.maskMoney(resumo?.valorApurado)}
                            background={'grey'}
                            standart={'expanded'}
                        />
                        <ContentDataInfo
                            label={'Glosado'}
                            value={!resumo?.valorGlosado ? 'R$ 0,00' : NumberUtils.maskMoney(resumo?.valorGlosado)}
                            background={'grey'}
                            standart={'expanded'}
                        />
                    </S.ContentData>
                </S.Header>
            </DividerSectionCard>
            <DividerSectionCard dividerContent={true}>
                <CardsFilter cardsFilter={filterFields} filterSelected={filterSelected} setFilterSelected={setFilterSelected} />

                <S.HeaderList>
                    <SearchBar
                        value={filtro}
                        placeholder="Procurar por número de lote"
                        handleOnClose={handleOnClose}
                        handleOnSearch={handleOnSearch}
                        handleOnChange={(e) => setFiltro(e?.target?.value)}
                    />
                    {pageLotes?.content?.length > 0 ? (
                        <S.Page>
                            <span className="pageNumber">
                                {pagination?.paginaAtual * pagination?.linhasPorPagina + 1} -{' '}
                                {pageLotes?.content?.length + pagination?.paginaAtual * pagination?.linhasPorPagina}
                                {' de '}
                                {pagination?.totalRegistros}
                            </span>
                            <ReactSVG src="/faturamento/assets/icons/dots.svg" />
                        </S.Page>
                    ) : null}
                </S.HeaderList>
                {pageLotes?.content?.length === 0 ? (
                    <div style={{ marginTop: '44px' }}>
                        <NoContent title="Por enquanto não há lotes" />
                    </div>
                ) : (
                    <>
                        <>
                            <S.ContentItemResult>
                                <Item>
                                    <p>Lotes</p>
                                </Item>
                                <Item>
                                    <p>Valor apresentado</p>
                                </Item>
                                <Item>
                                    <p>Nº de guias</p>
                                </Item>
                                <Item>
                                    <p>Tipo de lote</p>
                                </Item>
                                <Item>
                                    <p></p>
                                </Item>
                            </S.ContentItemResult>
                            {pageLotes?.content?.map((obj, index) => (
                                <S.ContentTable
                                    key={index}
                                    onClick={() =>
                                        route.push(`/processamento-contas/auditoria-bancada/resumo-lote/${obj.id}?competencia=${competencia}`)
                                    }
                                >
                                    <Item>
                                        <span>{obj.numeroLote}</span>
                                    </Item>
                                    <Item>
                                        <span>{NumberUtils.maskMoney(obj.valorApresentado)}</span>
                                    </Item>
                                    <Item>
                                        <span>{obj.totalGuias}</span>
                                    </Item>
                                    <Item>
                                        <span>{obj.tipoLote}</span>
                                    </Item>
                                </S.ContentTable>
                            ))}
                            <Pagination
                                totalPage={pagination?.totalPaginas}
                                totalRegister={pagination?.totalRegistros}
                                actualPage={pagination?.paginaAtual}
                                setNumberPage={pagination?.setNumberPage}
                            />
                        </>
                    </>
                )}
            </DividerSectionCard>
        </>
    )
}
export default ResumoPrestadorTemplate
