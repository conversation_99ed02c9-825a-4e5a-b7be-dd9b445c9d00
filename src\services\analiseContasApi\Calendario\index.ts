import { AxiosResponse } from 'axios'
import { apiAnaliseContas } from '../../apis/apiAnaliseContas'
import { ObjectUtils } from 'utils/objectUtils'
import { moduloEnum } from './enums'
import { IPage, ISortPage } from 'types/pagination'
import { ICalendarioDTO, ICalendarioForm, ICalendarioHistoricoAlteracaoDTO, ICompetenciaCalendarioDTO } from './types'

const baseUrl = '/calendario'

export class CalendarioService {
    static async get(props?: { prestadorId?: string; modulo?: moduloEnum } & ISortPage) {
        const params = ObjectUtils.propsToParams(props)
        const url = props ? `${baseUrl}?${params}` : baseUrl
        return apiAnaliseContas.get<{ content: ICalendarioDTO[] } & IPage>(url)
    }

    static async post(calendario: ICalendarioForm) {
        return apiAnaliseContas.post<ICalendarioDTO>(baseUrl, calendario)
    }

    static async getHistoricoAlteracoes(props?: ISortPage) {
        const params = ObjectUtils.propsToParams(props)
        const url = props ? `${baseUrl}/historico?${params}` : `${baseUrl}/historico`
        return apiAnaliseContas.get<ICalendarioHistoricoAlteracaoDTO[]>(url)
    }

    static async delete(id: string): Promise<AxiosResponse> {
        return apiAnaliseContas.delete(`${baseUrl}/${id}`)
    }

    static async getByID({ id }: { id: string }) {
        return apiAnaliseContas.get<ICalendarioDTO>(`${baseUrl}/${id}`)
    }

    static async put({ id, calendario }: { id: string; calendario: ICalendarioForm }) {
        return apiAnaliseContas.put<ICalendarioDTO>(`${baseUrl}/${id}`, calendario)
    }

    static async getCompetencia(props: { prestadorId?: string; competencia?: Date; modulo: moduloEnum }) {
        const params = ObjectUtils.propsToParams(props)
        const url = props ? `${baseUrl}/competencias-envio?${params}` : `${baseUrl}/competencias-envio`
        return apiAnaliseContas.get<ICompetenciaCalendarioDTO[]>(url)
    }
}
