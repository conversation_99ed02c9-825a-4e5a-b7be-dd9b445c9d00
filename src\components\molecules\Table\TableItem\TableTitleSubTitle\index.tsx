import React, { HTMLAttributes } from 'react'
import { SubTitle, Title, Wrapper } from './styles'

interface IPropsTitleSubTitle {
    title: string
    subTitle?: string
    titleStyles?: React.CSSProperties
    subTitleStyles?: React.CSSProperties
}

const TableTitleSubTitle = ({ title, subTitle, titleStyles, subTitleStyles, ...rest }: IPropsTitleSubTitle & HTMLAttributes<HTMLDivElement>) => {
    return (
        <Wrapper {...rest}>
            <Title style={{ ...titleStyles }}>{title}</Title>
            <SubTitle style={{ ...subTitleStyles }}>{subTitle}</SubTitle>
        </Wrapper>
    )
}

export default TableTitleSubTitle
