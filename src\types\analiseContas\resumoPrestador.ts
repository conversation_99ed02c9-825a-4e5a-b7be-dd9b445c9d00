export interface IResumoPrestadorQuery {
    valorApresentado: number
    valorApurado: number
    valorGlosado: number
    numeroNotaFiscal: string
    valorNotaFiscal: number
    nomeArquivoNotaFiscal: string
    idArquivoNotaFiscal: string
    numeroCaixa: string
    situacaoPrestador?: string
    cnpj?: string
    nomeFantasia?: string
}

export const initialResumoPrestador: IResumoPrestadorQuery = {
    numeroCaixa: '',
    valorApresentado: 0,
    valorApurado: 0,
    valorGlosado: 0,
    numeroNotaFiscal: '',
    valorNotaFiscal: 0,
    idArquivoNotaFiscal: '',
    nomeArquivoNotaFiscal: ''
}
