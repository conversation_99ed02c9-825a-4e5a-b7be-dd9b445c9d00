import Button from 'components/atoms/Button'
import CardsFilter from '../GuideGlosaFilter'

import NoContent from 'components/molecules/NoContent'
import { useCallback, useEffect, useState } from 'react'
import { useAuth } from 'src/hooks/auth'
import { useToast } from 'src/hooks/toast'
import { CobrancaServices, IGetCobrancaProps } from 'src/services/faturamentoPrestadorApi/cobrancaServices'
import { GeracaoRecursoGlosa } from 'src/services/faturamentoPrestadorApi/geracao-recurso-glosa'
import { ModuloEnum } from 'src/services/faturamentoPrestadorApi/geracao-recurso-glosa/enuns'
import { ICheckeboxFilterAnalisado } from 'src/templates/FaturamentoPrestador/CobrancaTemplates/organisms/ListaLotesAnalisados'
import ModalNovoLote from '../ModalNovoLote'
import { IPageLote } from 'types/cobrancaPrestador/loteCobranca'
import { IPrestadorAnaliseQuery } from 'types/cobrancaPrestador/prestadorAnalise'
import { IResumoSituacaoLoteCobrancaQuery } from 'types/cobrancaPrestador/resumoSituacaoLoteCobranca'
import { retiraSequencial } from 'utils/functions'
import { NumberUtils } from 'utils/numberUtils'
import ListaLotesEmAnalise, { ICheckeboxFilterAnalise } from '../ListaLotesEmAnalise'
import ListaLotesProcessando from '../ListaLotesProcessando'
import ListaLotesRecusados from '../ListaLotesRecusados'
import * as S from './styles'

const TabLoteMedico = ({
    competenciaSelecionada,
    competenciaDisponivel,
    setForceUpdateCompetencias
}: {
    competenciaSelecionada: IPrestadorAnaliseQuery
    competenciaDisponivel: any
    setForceUpdateCompetencias: any
}) => {
    const { addToast } = useToast()
    const { prestadorVinculado } = useAuth()

    const [isOpenModal, setIsOpenModal] = useState(false)
    const [isOpenModalDocumentos, setIsOpenModalDocumentos] = useState(false)
    const [isOpenModalConciliacao, setIsOpenModalConciliacao] = useState(false)
    const [idLoteParaConciliacao, setIdLoteParaConciliacao] = useState<string>()
    const [filterFields, setFilterFields] = useState([])
    const [idLoteCobranca, setIdLoteCobranca] = useState()
    const [documentosObrigatorios, setDocumentosObrigatorios] = useState([])
    const [numeroNota, setNumeroNota] = useState()
    const [valorNota, setValorNota] = useState()
    const [dataEmissao, setDataEmissao] = useState<string>()
    const [fileNFUploadModal, setFileNFUploadModal] = useState()
    const [isReset, setIsReset] = useState(false)
    const [filterSelected, setFilterSelected] = useState('PROCESSANDO')
    const [lotes, setLotes] = useState<IPageLote | any>()
    const [checkboxFilterAnalisado, setCheckboxFilterAnalisado] = useState<ICheckeboxFilterAnalisado>({
        lotesSemGlosa: false,
        lotesGlosaNaoRecursado: false,
        lotesGlosaRecursado: false,
        lotesRecurso: false
    })
    const [checkboxFilterAnalise, setCheckboxFilterAnalise] = useState<ICheckeboxFilterAnalise>({
        lotesCobranca: false,
        lotesRecursoGlosa: false
    })
    const [exigeNF, setExigeNF] = useState<boolean>()
    const [calendarioEnvioLotesAberto, setCalendarioEnvioLotesAberto] = useState<boolean>(true)
    const [refresh, setRefresh] = useState(false)
    const [refreshList, setRefreshList] = useState(false)
    const [situacoes, setSituacoes] = useState<IResumoSituacaoLoteCobrancaQuery[]>()
    const [filtro, setFiltro] = useState<string>('')
    const [loadingLotes, setLoadingLotes] = useState(true)

    // const [lotesDevolucao, setLotesDevolucao] = useState<ILoteDevolucaoQuery[]>([])
    // const [lotesRecusa, setLotesRecusa] = useState<ILoteRecusaQuery[]>([])

    const handleResetState = () => {
        setIsOpenModal(false)
        setIsOpenModalDocumentos(false)
        setIsOpenModalConciliacao(false)
        setIdLoteParaConciliacao(null)
        setDocumentosObrigatorios([])
        setNumeroNota(null)
        setValorNota(null)
        setFileNFUploadModal(null)
        setExigeNF(null)
        setIsReset(true)

        carregarSituacoes()
    }

    const carregarSituacoes = useCallback(() => {
        CobrancaServices.getPorcentagemSituacoes(competenciaSelecionada.competencia, prestadorVinculado.uuid).then(({ data }) => {
            setSituacoes(data)
        })
    }, [competenciaSelecionada])

    useEffect(() => {
        if (!competenciaSelecionada) return

        carregarSituacoes()
    }, [competenciaSelecionada])

    const getItemSituacao = (situacao: string) => {
        return situacoes?.some((i) => i.situacao === situacao)
            ? situacoes?.find((i) => i.situacao === situacao)
            : { porcentagem: 0, quantidadeDeLotes: 0 }
    }

    useEffect(() => {
        const filters = [
            { name: 'Processando', situacao: 'PROCESSANDO' },
            { name: 'Validado', situacao: 'EM_ANALISE' },
            { name: 'Recusado', situacao: 'RECUSADO' }
        ]

        setFilterFields(filters)
    }, [situacoes])

    const carregarLotesProcessando = () => {
        if (prestadorVinculado?.uuid)
            CobrancaServices.getLotesSimulacao(competenciaSelecionada?.competencia, prestadorVinculado?.uuid, filterSelected).then(({ data }) => {
                setLotes(data)
            })
    }

    useEffect(() => {
        if (!competenciaSelecionada) return
        if ((filterSelected === 'PROCESSANDO' || filterSelected === 'EM_AUDITORIA') && filtro.length === 0) {
            carregarLotesProcessando()
        }

        // if (filterSelected === 'RECUSADO') {
        //     CobrancaServices.getLotesVisaoRecusa(competenciaSelecionada?.competencia).then(({ data }) => {
        //         setLotesRecusa(data.content)
        //     })
        // }

        // if (filterSelected === 'DEVOLVIDO') {
        //     CobrancaServices.getLotesVisaoDevolucao(competenciaSelecionada?.competencia).then(({ data }) => {
        //         setLotesDevolucao(data.content)
        //     })
        // }
    }, [filterSelected, competenciaSelecionada])

    useEffect(() => {
        carregarLotesProcessando()
    }, [])

    useEffect(() => {
        // setCalendarioEnvioLotesAberto(true)

        CobrancaServices.getAbertoParaEnvioLote({
            modulo: 'MEDICO',
            competencia: competenciaSelecionada?.competencia,
            prestadorId: prestadorVinculado?.uuid
        })
            .then(({ data }) => {
                setCalendarioEnvioLotesAberto(!data?.calendarioEnvioLotesAberto)
            })
            .catch(() => {
                // addToast({ title: 'Ocorreu erro ao enviar a nota fiscal. Tente novamente.', type: 'error' })
            })

        console.log(calendarioEnvioLotesAberto)
    }, [competenciaSelecionada])

    const handleClickGerarNota = (idLote, numeroNota, valorNota, dataEmissao, file, documentosObrigatorios) => {
        if (!idLote && !numeroNota && !valorNota && !file) {
            return setRefresh(true)
        }

        CobrancaServices.postNotaFiscalParaLotes({
            idLotes: [idLote],
            numeroNota: numeroNota,
            valor: NumberUtils.unMaskMoney(valorNota)?.toString(),
            dataEmissao,
            arquivos: [file]
        })
            .then(() => {
                setRefresh(true)

                if (documentosObrigatorios.length === 0) {
                    carregarLotesProcessando()
                    addToast({ title: 'Lote e nota fiscal enviados com sucesso.', type: 'success', duration: 5000 })
                    handleResetState()
                }
            })
            .catch(() => {
                addToast({ title: 'Ocorreu erro ao enviar a nota fiscal. Tente novamente.', type: 'error' })
            })
    }

    const handleClickConciliarNota = (idLote: string) => {
        setIdLoteParaConciliacao(idLote)
        setIsOpenModalConciliacao(true)
    }

    const handleSetStepDocumentos = ({ idLoteCobranca, numeroNota, valorNota, dataEmissao, fileNFUploadModal, documentosObrigatorios, exigeNF }) => {
        setIsOpenModal(false)

        if (documentosObrigatorios.length === 0 && exigeNF) {
            handleClickGerarNota(idLoteCobranca, numeroNota, valorNota, dataEmissao, fileNFUploadModal, documentosObrigatorios)
        }

        if (documentosObrigatorios.length > 0) {
            if (exigeNF) {
                handleClickGerarNota(idLoteCobranca, numeroNota, valorNota, dataEmissao, fileNFUploadModal, documentosObrigatorios)
            }

            setIsOpenModalDocumentos(true)
            setDocumentosObrigatorios(documentosObrigatorios)
            setExigeNF(exigeNF)
        }

        setIdLoteCobranca(idLoteCobranca)
        setNumeroNota(numeroNota)
        setValorNota(valorNota)
        setDataEmissao(dataEmissao)
        setFileNFUploadModal(fileNFUploadModal)
    }

    const handleSubmit = () => {
        if (exigeNF) {
            handleClickGerarNota(idLoteCobranca, numeroNota, valorNota, dataEmissao, fileNFUploadModal, documentosObrigatorios)
        }
        setIsReset(true)
    }

    const carregarLotes = (filter?: string, page?: number) => {
        setLoadingLotes(true)
        const getProps: IGetCobrancaProps =
            filter === '' || filter === undefined
                ? {
                      size: 5,
                      page: page || 0
                  }
                : {
                      size: 5,
                      page: page || 0,
                      filtroNumeroLote: filter
                  }

        CobrancaServices.getLotes(competenciaSelecionada?.competencia, prestadorVinculado.uuid, '', getProps)
            .then(({ data }) => {
                // data.content[0]?.situacaoLote ? setFilterSelected(filterSelected) : setFilterSelected('NOCONTENT')

                if (data?.content[0]?.situacaoLote === 'FECHADO' || filterSelected === 'ANALISADO') {
                    setLoadingLotes(true)
                    GeracaoRecursoGlosa.getlotesAnalisados({
                        identificadorLote: retiraSequencial(filtro),
                        competencia: competenciaSelecionada?.competencia,
                        prestadorId: prestadorVinculado?.uuid,
                        modulo: ModuloEnum.MEDICO,
                        lotesGlosasNaoRecursadas: checkboxFilterAnalisado?.lotesGlosaNaoRecursado,
                        lotesGlosasRecursadas: checkboxFilterAnalisado?.lotesGlosaRecursado,
                        lotesRecursoGlosa: checkboxFilterAnalisado?.lotesRecurso,
                        lotesSemGlosa: checkboxFilterAnalisado?.lotesSemGlosa,
                        page: 0,
                        size: 5
                    })
                        .then(({ data }) => {
                            setLotes(data)
                        })
                        .catch((err) => {
                            addToast({
                                title: err?.message ? err?.message : 'Ocorreu um erro ao buscar as informações',
                                type: 'error',
                                duration: 3000
                            })
                        })
                        .finally(() => setLoadingLotes(false))
                }

                if (data?.content[0]?.situacaoLote === 'EM_ANALISE' || filterSelected === 'EM_ANALISE') {
                    setLoadingLotes(true)
                    GeracaoRecursoGlosa.getlotesEmAnalise({
                        identificadorLote: retiraSequencial(filtro),
                        competencia: competenciaSelecionada?.competencia,
                        prestadorId: prestadorVinculado?.uuid,
                        modulo: ModuloEnum.MEDICO,
                        lotes: checkboxFilterAnalise?.lotesCobranca,
                        lotesRecursoGlosa: checkboxFilterAnalise?.lotesRecursoGlosa,
                        page: 0,
                        size: 10
                    })
                        .then(({ data }) => {
                            setLotes(data)
                        })
                        .catch((err) => {
                            addToast({
                                title: err?.message ? err?.message : 'Ocorreu um erro ao buscar as informações',
                                type: 'error',
                                duration: 3000
                            })
                        })
                        .finally(() => setLoadingLotes(false))
                }

                if (data?.content[0]?.situacaoLote !== 'FECHADO' && data?.content[0]?.situacaoLote !== 'EM_ANALISE') {
                    setLotes(data)
                }
            })
            .finally(() => setLoadingLotes(false))
    }

    const handleClickPesquisar = () => {
        filtro !== '' ? carregarLotes(retiraSequencial(filtro)) : carregarLotes()
    }

    const handleClickClearBtn = () => {
        setFiltro('')
        // carregarLotesProcessando()
        setRefreshList(!refreshList)
    }

    return (
        <S.Container>
            <S.HeaderSearch>
                <S.Search>
                    {/* <Input
                        value={filtro}
                        placeholder="Procure por lote"
                        handleClickClearField={() => handleClickClearBtn()}
                        handleClickIconRight={handleClickPesquisar}
                        handleOnChange={(e) => setFiltro(e.target.value)}
                    /> */}
                </S.Search>
                <S.AddButton>
                    <div className="contentButton">
                        <Button
                            disabled={calendarioEnvioLotesAberto}
                            themeButton="secondary"
                            style={{ padding: '12px 20px' }}
                            iconLeft={'/faturamento/assets/icons/plus.svg'}
                            onClick={() => {
                                setIsOpenModal(true)
                            }}
                        >
                            Novo lote
                        </Button>
                    </div>
                </S.AddButton>
            </S.HeaderSearch>
            <div className="contentHeader">
                <CardsFilter
                    cardsFilter={[
                        { name: 'Processando', situacao: 'PROCESSANDO' },
                        { name: 'Validado', situacao: 'VALIDADO' },
                        { name: 'Recusado', situacao: 'RECUSADO' }
                    ]}
                    filterSelected={filterSelected}
                    setFilterSelected={(value) => {
                        setFilterSelected(value)
                        setFiltro('')
                    }}
                />
            </div>

            {/* table */}
            {(filterSelected === 'PROCESSANDO' || filterSelected === 'VALIDADO') && (
                <ListaLotesProcessando
                    lotes={lotes}
                    refresh={refresh}
                    setRefresh={setRefresh}
                    filtroNumeroLote={filtro}
                    carregarSituacoes={carregarSituacoes}
                    competenciaSelecionada={competenciaSelecionada}
                    refreshList={refreshList}
                    setLotes={setLotes}
                    loadingLotes={loadingLotes}
                    setLoadingLotes={setLoadingLotes}
                    situacao={filterSelected}
                />
            )}

            {/* {filterSelected === 'VALIDADO' && (
                <ListaLotesEmAnalise
                    filtro={filtro}
                    forceUpdate={isOpenModal}
                    competenciaSelecionada={competenciaSelecionada}
                    searchLote={filtro}
                    setLotes={setLotes}
                    refreshList={refreshList}
                    lotes={lotes}
                    checkboxFilter={checkboxFilterAnalise}
                    setCheckboxFilter={setCheckboxFilterAnalise}
                    loadingLotes={loadingLotes}
                    setLoadingLotes={setLoadingLotes}
                />
            )} */}

            {filterSelected === 'RECUSADO' && (
                <ListaLotesRecusados
                    forceUpdate={isOpenModal}
                    competenciaSelecionada={competenciaSelecionada}
                    searchLote={filtro}
                    refreshList={refreshList}
                    setLotes={setLotes}
                    lotes={lotes}
                    loadingLotes={loadingLotes}
                    setLoadingLotes={setLoadingLotes}
                    situacao={filterSelected}
                />
            )}

            {filterSelected === 'NOCONTENT' && (
                <NoContent
                    title="Não encontramos o lote pesquisado. Confira o número digitado e tente novamente."
                    path="/parametros/regras-de-excludencia/nova-regra"
                />
            )}

            <ModalNovoLote
                reset={isReset}
                tipoLote="cobranca"
                labelButton="Próximo"
                isOpen={isOpenModal}
                setReset={setIsReset}
                setIsOpen={setIsOpenModal}
                onClickConciliarNotaFiscal={(idLote) => {
                    handleClickConciliarNota(idLote)
                }}
                handleSetStepDocumentos={handleSetStepDocumentos}
                onClickEnviarNotaFiscal={(idLote, numeroNota, valorNota, file) => {
                    handleClickGerarNota(idLote, numeroNota, valorNota, dataEmissao, file, documentosObrigatorios)
                }}
                setForceUpdateCompetencias={setForceUpdateCompetencias}
                // fileUpload={fileUpload}
                // setFileUpload={setFileUpload}
                competencia={!competenciaSelecionada?.competencia ? competenciaDisponivel?.competencia : competenciaSelecionada?.competencia}
            />
        </S.Container>
    )
}

export default TabLoteMedico
