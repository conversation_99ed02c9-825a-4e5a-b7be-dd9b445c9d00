import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { ReactSVG } from 'react-svg'
import Button from 'components/atoms/Button'
import Layout from 'components/molecules/Layout'
import SearchBar from 'components/molecules/SearchBar'
import TitleSection from 'components/atoms/TitleSection'
import ContentDataInfo from 'components/molecules/ShowDataConfirm'
import DividerSectionCard from 'components/atoms/DividerSectionCard'
import * as S from './styles'
import { RecursoGlosaService } from 'src/services/analiseContasApi/recursoGlosaServices'
import { NumberUtils } from 'utils/numberUtils'
import { ICompetenciaQuery } from 'types/analiseContas/competencia'
import { IPagination } from 'types/common/pagination'
import SelectComp from './Components/SelectComp'
// import AlertBg from './Components/AlertBg'
import { useRouter } from 'next/router'
import { IResumoQuantidadeGuiasEValoresPorCompetencia, IResumoTotalDto } from 'types/recursoGlosaPrestador/recursoGlosaMock'
import TablePagination from 'components/molecules/TablePagination'
import { IPrestadorListDto, IQuantitativosProvider } from 'types/analiseContas/recursoGlosa'
import { StringUtils, capitalize } from 'utils/stringUtils'
import { PaginationHelper } from 'utils/helpers/paginationHelper'
import { useToast } from 'src/hooks/toast'
import NoContent from 'components/molecules/NoContent'
import { useDebounce } from 'utils/useDebounce'
import AlertBg from './Components/AlertBg'

export type ParseTypes = {
    label: string | number
    value: string | number
}

export type ICompSelected = {
    competencia: string
    situacao: string
    descricaoMes: string
    situacaoCompetenciaRecursoGlosa?: 'EM_ANALISE' | 'ENCERRADA' | 'FECHADA' | 'PAGA'
}

const RecursoGlosaTemplates = () => {
    const { addToast } = useToast()
    const [competencias, setCompetencias] = useState([])
    const [resumo, setResumo] = useState<IResumoQuantidadeGuiasEValoresPorCompetencia>()
    const [quantitativoProviders, setQuantitativoProviders] = useState<IQuantitativosProvider>()
    const [pageNumber, setPageNumber] = useState(0)
    const [paymentProcess, setPaymentProcess] = useState(false)
    const [finishedProcess, setFinishedProcess] = useState(false)
    const [stepName, setStepName] = useState(null)
    const [enableButtons, setEnableButtons] = useState({
        finishButton: false,
        reopenButton: false
    })
    // const [enableFinishButton, setEnableFinishButton] = useState<boolean>(false)

    const [filtro, setFiltro] = useState<string>('')
    const debouncedValue = useDebounce<string>(filtro, 500)

    const tableGuideLabel: { label: string; value: any | 'acao' }[] = [
        { label: 'Prestador', value: 'prestador' },
        { label: 'Lotes finalizados', value: 'lotesFinalizados' },
        { label: 'Status', value: 'status' }
    ]

    // FIXME: COMPETENCIA E EXERCICIO DADOS MOCK

    const [competenciaSelecionada, setCompetenciaSelecionada] = useState<ICompSelected>()
    const [anos, setAnos] = useState<ParseTypes[]>([])
    const [prestadoresList, setPrestadoresList] = useState<IPrestadorListDto[]>([])
    const [competencia, setCompetencia] = useState<ICompetenciaQuery>()
    const [anoExercicio, setAnoExercicio] = useState<number>()
    const [guiaPagination, setGuiaPagination] = useState<IPagination>()
    const [refresh, setRefresh] = useState<boolean>(true)

    // const anos = useMemo(() => anos?.map((ano) => ({ label: ano, value: ano })), [anos])
    const initialValueSelect = useMemo(() => anoExercicio && { label: anoExercicio.toString(), value: anoExercicio.toString() }, [anoExercicio])
    const route = useRouter()

    // GET DE EXERCÍCIOS
    const getExercicio = () => {
        RecursoGlosaService?.getExerciciosRecursoGlosa()
            .then(({ data }) => {
                const exercicios = data?.map((x) => ({
                    label: x?.ano,
                    value: x?.ano
                }))

                setAnos(exercicios)

                if (data) {
                    setAnoExercicio(data[data.length - 1].ano)
                }
            })
            .catch((error) => {
                console.error(error)
            })
    }

    // GET DE COMPETÊNCIAS
    const getCompetencias = () => {
        // FIXME: DADOS MOCK VINDO DO SERVICES.
        RecursoGlosaService?.getCompetenciasData(anoExercicio).then(({ data }) => {
            const value = data?.map((item) => {
                return item
            })

            setCompetencias(value)

            const selecionada = value[value?.length - 1]
            setCompetenciaSelecionada(selecionada)
        })
    }

    useEffect(() => {
        getExercicio()
    }, [])

    useEffect(() => {
        if (anoExercicio) {
            getCompetencias()
        }
    }, [anoExercicio, anos, refresh])

    useEffect(() => {
        if (competenciaSelecionada?.competencia) {
            RecursoGlosaService?.getResumoGeral(competenciaSelecionada?.competencia)
                .then(({ data }) => {
                    setResumo(data)
                })
                .catch((error) => {
                    // console.error(error)
                })
        }
    }, [anoExercicio, competenciaSelecionada])

    const parseCompetencias = useMemo(() => {
        return competencias?.map((item) => {
            return {
                competencia: item.competencia,
                situacao: item.situacao?.toString(),
                descricaoMes: item?.descricaoMes
            }
        })
    }, [competencias])

    const onChangeAnoExercicio = useCallback(
        (ano: React.SetStateAction<number>) => {
            // resetValues()
            setAnoExercicio(ano)
        },
        [setAnoExercicio]
    )

    // GET PRESTADORES LIST

    function getPrestadoresList() {
        RecursoGlosaService?.getProviderList({
            nomeCnpj: filtro,
            situacao: stepName ? stepName : null,
            competencia: competenciaSelecionada?.competencia,
            page: pageNumber,
            size: 10
        })
            .then(({ data }) => {
                setPrestadoresList(data?.content)
                setGuiaPagination(PaginationHelper.parserPagination(data, setPageNumber))
            })
            .catch((err) => {
                // console.log('error')
            })
    }

    // GET QUANTITATIVOS PRESTADORES

    function getPrestadoresQuantitativos() {
        RecursoGlosaService?.getQuantitativoProviders(competenciaSelecionada?.competencia)
            .then(({ data }) => {
                setQuantitativoProviders(data)
            })
            .catch((err) => {
                // console.log('error')
            })
    }

    useEffect(() => {
        setPageNumber(0)
    }, [debouncedValue])

    useEffect(() => {
        if (competenciaSelecionada?.competencia) {
            getPrestadoresList()
            getPrestadoresQuantitativos()
        }
    }, [anoExercicio, debouncedValue, competenciaSelecionada, pageNumber, stepName])

    const handleSetCompetencia = (competencia: any) => {
        setCompetenciaSelecionada(competencia)
    }

    // TODO: HANDLECLICK

    const handleFinalizarRecurso = () => {
        // setFinishedProcess(true)
        setEnableButtons((prev) => ({ ...prev, finishButton: true }))
        RecursoGlosaService.pathFinalizarAnaliseDeRecursos(competenciaSelecionada?.competencia)
            .then(() => {
                addToast({
                    title: 'Recurso finalizado com sucesso',
                    type: 'success'
                })
                getPrestadoresList()
                getPrestadoresQuantitativos()
                setRefresh(!refresh)
                setEnableButtons((prev) => ({ ...prev, finishButton: false }))
            })
            .catch(({ response }) => {
                addToast({
                    title: response?.data?.message,
                    type: 'error'
                })
            })
            .finally(() => {
                setEnableButtons((prev) => ({ ...prev, finishButton: false }))
            })
    }

    const handleReabrirCompetencia = () => {
        setEnableButtons((prev) => ({ ...prev, reopenButton: true }))
        RecursoGlosaService.pathReabrirAnaliseDeRecursos(competenciaSelecionada?.competencia)
            .then(() => {
                addToast({
                    title: 'Recurso reaberto com sucesso',
                    type: 'success'
                })
                getPrestadoresList()
                getPrestadoresQuantitativos()
                RecursoGlosaService?.getCompetenciasData(anoExercicio).then(({ data }) => {
                    const value = data?.map((item) => {
                        return item
                    })

                    setCompetencias(value)

                    const selecionada = value[value?.length - 1]
                    setCompetenciaSelecionada(selecionada)
                })
                setEnableButtons((prev) => ({ ...prev, reopenButton: false }))
            })
            .catch(({ response }) => {
                addToast({
                    title: response?.data?.message,
                    type: 'error'
                })
            })
            .finally(() => {
                setEnableButtons((prev) => ({ ...prev, reopenButton: false }))
            })
    }

    // ENVIAR PARA O FINANCEIRO

    const handleEnviarFinanceiro = () => {
        RecursoGlosaService.postEnviarParaFinanceiro(competenciaSelecionada?.competencia)
            .then((response) => {
                addToast({
                    title: 'Enviou para o financeiro com sucesso!',
                    type: 'success'
                })

                setRefresh(!refresh)
            })
            .catch(({ response }) => {
                addToast({
                    title: response?.data?.message,
                    type: 'error'
                })
            })
    }

    enum EnumStatus {
        'NAO_INICIADO' = 'Não iniciado',
        'EM_ANALISE' = 'Em análise',
        'FINALIZADO' = 'Finalizado',
        'ENCERRADA' = 'Encerrada'
    }

    function handleClickToSelectStatus(valuel: string) {
        setPageNumber(0)
        setStepName(valuel)
    }

    return (
        <Layout isLoggedIn={true} title={'Recurso de Glosa'}>
            <S.Container>
                <SelectComp
                    data={parseCompetencias}
                    hasDefaultValueInSelect={true}
                    dateFilter={anos}
                    valueSelect={initialValueSelect}
                    onChangeAnoExercicio={onChangeAnoExercicio}
                    competenciaSelecionada={competenciaSelecionada}
                    setCompetenciaSelecionada={handleSetCompetencia}
                />
            </S.Container>

            <S.ContainerInfo>
                {(competenciaSelecionada?.situacaoCompetenciaRecursoGlosa === 'PAGA' ||
                    competenciaSelecionada?.situacaoCompetenciaRecursoGlosa === 'FECHADA') && (
                    <AlertBg
                        title="Recurso de glosa finalizada"
                        color="info"
                        iconSrc="/faturamento/assets/icons/info.svg"
                        description="As contas já estão na fase de composição de pagamento. Acesse Composição de pagamento para conferir"
                    />
                )}
            </S.ContainerInfo>

            <DividerSectionCard dividerContent={true}>
                <S.Header>
                    <TitleSection style={{ fontSize: '16px' }}>Resumo</TitleSection>
                    {competenciaSelecionada?.situacao === EnumStatus.ENCERRADA && (
                        <S.ActionsContainer>
                            <S.Action onClick={handleReabrirCompetencia}>
                                <ReactSVG src="/faturamento/assets/icons/refresh.svg" />
                                <p>Reabrir</p>
                            </S.Action>
                            <S.Action onClick={handleEnviarFinanceiro}>
                                <ReactSVG src="/faturamento/assets/icons/money.svg" />
                                <p>Enviar para o financeiro</p>
                            </S.Action>
                        </S.ActionsContainer>
                    )}
                    {competenciaSelecionada?.situacao === (EnumStatus.EM_ANALISE || EnumStatus.NAO_INICIADO) && (
                        <Button
                            disabled={enableButtons?.finishButton}
                            themeButton={'warning'}
                            style={{ width: '200px' }}
                            onClick={handleFinalizarRecurso}
                        >
                            {enableButtons?.finishButton ? 'Finalizando Recurso' : 'Finalizar Recurso'}
                        </Button>
                    )}
                    {competenciaSelecionada?.situacao === EnumStatus.FINALIZADO && (
                        <S.ActionsContainer>
                            <S.InfoCard>
                                <img src="/faturamento/assets/icons/info.svg" />
                                <p>O recurso de glosa já foi encerrado e se encontra na fase de pagamento</p>
                            </S.InfoCard>
                        </S.ActionsContainer>
                    )}
                </S.Header>
                <TitleSection style={{ paddingTop: '24px', color: 'rgba(0, 0, 0, 0.56)', fontSize: '16px' }}>Valores</TitleSection>
                <S.Header>
                    <S.ContentData>
                        <S.TuplaDataInfo
                            label={'Recursado'}
                            value={NumberUtils.maskMoney(resumo?.valorRecursado)}
                            background={'grey'}
                            standart={'expanded'}
                        />
                        <S.TuplaDataInfo
                            label={'Deferido'}
                            value={NumberUtils.maskMoney(resumo?.valorDeferido)}
                            background={'grey'}
                            standart={'expanded'}
                        />
                        <S.TuplaDataInfo
                            label={'Indeferido'}
                            value={NumberUtils.maskMoney(resumo?.valorIndeferido)}
                            background={'grey'}
                            standart={'expanded'}
                        />
                    </S.ContentData>
                </S.Header>
                <TitleSection style={{ paddingTop: '24px', color: 'rgba(0, 0, 0, 0.56)', fontSize: '16px' }}>Guias</TitleSection>
                <S.Header>
                    <S.ContentData>
                        <S.TuplaDataInfo label={'Total'} value={resumo?.qtdGuias ?? '-'} background={'grey'} standart={'expanded'} />
                        <S.TuplaDataInfo label={'Abertas'} value={resumo?.qtdGuiasNaoAnalisadas ?? '-'} background={'grey'} standart={'expanded'} />
                        <S.TuplaDataInfo label={'Finalizadas'} value={resumo?.qtdGuiasAnalisadas ?? '-'} background={'grey'} standart={'expanded'} />
                    </S.ContentData>
                </S.Header>
            </DividerSectionCard>
            <DividerSectionCard dividerContent={true}>
                <TitleSection style={{ marginBottom: '24px', fontSize: '16px' }}>Prestadores</TitleSection>

                <S.HeaderGlosaList>
                    <SearchBar
                        handleOnClose={() => setFiltro('')}
                        value={filtro}
                        handleOnChange={(e) => {
                            setFiltro(e.target.value)
                        }}
                        placeholder="Procurar prestador"
                    />
                    {/* <S.Page>
                        <span className="pageNumber">1 - 2 de 2</span>
                        <ReactSVG src="/faturamento/assets/icons/dots.svg" />
                    </S.Page> */}
                </S.HeaderGlosaList>
                {/* <ResultGlosaCard data={prestadores?.content} pagination={pagination} setNumberPage={setNumberPage} /> */}

                <S.Steps>
                    <li onClick={() => handleClickToSelectStatus(null)} className={stepName === null && 'actived'}>
                        Total
                        <S.Badge status={'TOTAL'} className={stepName === null && 'actived'}>
                            {quantitativoProviders?.qtdTotal}
                        </S.Badge>
                    </li>
                    <li onClick={() => handleClickToSelectStatus('FINALIZADO')} className={stepName === 'FINALIZADO' && 'actived'}>
                        Finalizados
                        <S.Badge status={'FINALIZADO'} className={stepName === 'FINALIZADO' && 'actived'}>
                            {quantitativoProviders?.qtdFinalizado}
                        </S.Badge>
                    </li>
                    <li onClick={() => handleClickToSelectStatus('EM_ANALISE')} className={stepName === 'EM_ANALISE' && 'actived'}>
                        Em análise
                        <S.Badge status={'EM_ANALISE'} className={stepName === 'EM_ANALISE' && 'actived'}>
                            {quantitativoProviders?.qtdEmAnalise}
                        </S.Badge>
                    </li>
                    <li onClick={() => handleClickToSelectStatus('NAO_INICIADO')} className={stepName === 'NAO_INICIADO' && 'actived'}>
                        Não iniciado
                        <S.Badge status={'NAO_INICIADO'} className={stepName === 'NAO_INICIADO' && 'actived'}>
                            {quantitativoProviders?.qtdNaoIniciado}
                        </S.Badge>
                    </li>
                </S.Steps>

                {prestadoresList?.length > 0 ? (
                    <TablePagination
                        style={{ marginTop: '46px' }}
                        titles={tableGuideLabel}
                        // selectIdField={'id'}
                        noSelectAll={true}
                        // enableSelect={true}
                        padding={1}
                        pagination={guiaPagination}
                        onClickElement={'prestadorAnaliseRecursoId'}
                        onClickAction={(prestadorAnaliseRecursoId) =>
                            route.push(`/processamento-contas/recurso-glosa/prestador/${prestadorAnaliseRecursoId}`)
                        }
                        values={prestadoresList?.map((item) => {
                            return {
                                ...item,
                                prestador: (
                                    <S.ProviderData>
                                        <p>{capitalize(item?.nome) || '-'}</p>
                                        <div>
                                            <p>{StringUtils?.maskCnpj(item?.cnpj) || '-'}</p>
                                        </div>
                                    </S.ProviderData>
                                ),
                                lotesFinalizados: `${item?.qtdLotesFinalizados} de ${item?.qtdLotes}`,
                                status: <S.Badge status={item?.situacao}>{EnumStatus[item?.situacao]}</S.Badge>,
                                prestadorId: item?.prestadorId
                            }
                        })}
                        customGridStyles={'1.5fr 1fr 1fr'}
                    />
                ) : (
                    <NoContent title="Não há prestadores para os parâmetros informados." />
                )}
            </DividerSectionCard>
        </Layout>
    )
}

export default RecursoGlosaTemplates
