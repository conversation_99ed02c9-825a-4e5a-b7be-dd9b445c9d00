import { Situacao<PERSON><PERSON>, TipoEnvio, TipoLote } from 'types/common/enums'
import { SituacaoProcessamentoLoteEnum } from './enums'

export interface IGetProcessamentoContasQuantitativosProps {
    tipoProcessamentoLote: 'NORMAL' | 'SIMULACAO'
    competencia: string
    codigoContratado?: string
    emAbertoPor?: number
}

export interface IGetProcessamentoContasQuantitativoDto {
    percentualFalha: number
    percentualRecusa: number
    lotesEmAbertoAcimaTempoBase: number
    quantidadeLotesNaoSicronizados: number
    quantidadeLotesFalhaCompetencia: number
    resumoLoteProcessado: {
        tipoProcessamento: 'NORMAL' | 'SIMULACAO'
        situacao: SituacaoLote
        quantidadeLotes: number
    }[]
    resumoProcessamentoLote: {
        quantidadeLotes: number
        tipoProcessamento: 'NORMAL' | 'SIMULACAO'
        situacaoProcessamento: SituacaoProcessamentoLoteEnum
        situacaoRegistoProcessamento: string
    }[]
    resumoTempoProcessamento: {
        mediaTempoProcessamento: number
        menor15: number
        entre15e60: number
        maior60: number
        totalRegistros: number
    }
}

export interface IProcessamentoContasQuantitativoResumo {
    percentualFalha: number
    percentualRecusa: number
    lotesEmAbertoAcimaTempoBase: number
    quantidadeLotesNaoSicronizados: number
    quantidadeLotesFalhaCompetencia: number
    quantidadeLotesAgendados: number
    quantidadeLotesProcessando: number
    quantidadeLotesProcessados: number
    quantidadeLotesRecusados: number
    quantidadeLotesEmAnalise: number
    quantidadeLotesComFalha: number
    quantidadeLotesCancelados: number
    resumoTempoProcessamento: {
        mediaTempoProcessamento: number
        menor15: number
        entre15e60: number
        maior60: number
        totalRegistros: number
    }
}

export interface IGetProcessamentoPrestadoresProps {
    page?: number
    size?: number
    tipoProcessamentoLote: 'NORMAL' | 'SIMULACAO'
    competencia?: string
    codigoContratado?: string
    situacaoLote?: SituacaoLote
    situacaoProcessamentoLote?: SituacaoProcessamentoLoteEnum
    emAbertoPor?: number
}

export interface IGetItemProcessamentoPrestadorDto {
    prestadorId: string
    nomeFantasia: string | null
    cpfCnpj: string
    categoria: string | null
    quantidadeLotes: number | null
}

export interface IGetRegistrosProcessamentoProps {
    page?: number
    size?: number
    competencia?: string
    prestadorId?: string
    situacaoLote?: SituacaoLote
    situacaoProcessamento?: SituacaoProcessamentoLoteEnum
    identificadorLote?: number
}

export interface IGetRegistroProcessamentoLoteDto {
    uuid: string
    identificadorLote: number
    tipoLote: TipoLote
    tipoEnvio: TipoEnvio
    dataEnvio: string
    situacaoLote: SituacaoLote
}

export interface IPatchReprocessarLotesComFalhaProps {
    competencia?: string
    identificadorLotes?: number[]
    prestadorId?: string
}

export interface IGetSincronizarAnaliseContasProps {
    competencia?: string
}

export interface IGetPermiteRedistribuirLotesAgendadosProps {
    competencia?: string
}

export interface IGetRedistribuirLotesAgendadosProps {
    competencia?: string
}

export interface IGetDownloadXmlLoteProps {
    identificadorLote: number
}
