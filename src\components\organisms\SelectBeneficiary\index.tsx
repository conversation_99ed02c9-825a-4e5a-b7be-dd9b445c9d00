/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useState } from 'react'
import { ReactSVG } from 'react-svg'

import Modal from 'react-modal'
import * as S from './styles'
import { customStyles } from './styles'
import BeneficiarySearch from 'components/molecules/BeneficiarySearch'
import BeneficiaryBar from 'components/molecules/BeneficiaryBar'
import { Beneficiary } from 'src/types/faturamentoOperadora/beneficiary'
type SelectBeneficiaryProps = {
    clickModal: () => void
    recipient?: Beneficiary
    setRecipient: (recipient: Beneficiary | null) => void
    biometry?: boolean
}
const SelectBeneficiary = ({ clickModal, recipient, biometry = true, setRecipient }: SelectBeneficiaryProps) => {
    const [modalIsOpen, setIsOpen] = useState(false)
    const [beneficiarySelected, setDataBeneficiarySelected] = useState<Beneficiary | null>(null)
    const [verified, setIsVerified] = useState(true)

    const [cpf, setCpf] = useState('')

    function openModal() {
        setIsOpen(true)
        setIsVerified(true)

        setTimeout(() => {
            setRecipient(beneficiarySelected)
            setIsOpen(false)
            clickModal()
        }, 500)
    }

    function closeModal() {
        setIsOpen(false)
    }

    useEffect(() => {
        if (recipient !== null && recipient !== undefined) {
            setDataBeneficiarySelected(recipient)
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [recipient])

    return (
        <S.Container>
            {!beneficiarySelected && (
                <BeneficiarySearch
                    label="CPF do Beneficiário ou número do cartão"
                    value={cpf}
                    setValue={setCpf}
                    onClick={(recipient: Beneficiary) => {
                        setDataBeneficiarySelected(recipient)
                    }}
                />
            )}

            {beneficiarySelected && (
                <>
                    <BeneficiaryBar
                        onClickIcon={() => {
                            setDataBeneficiarySelected(null)
                            setRecipient(null)
                        }}
                        openModal={biometry ? openModal : () => setRecipient(beneficiarySelected)}
                        disabled={!!recipient}
                        recipient={beneficiarySelected}
                        themeColor={'blue'}
                        iconClose={!recipient ? false : true}
                    />
                    {biometry && modalIsOpen && (
                        <Modal isOpen={modalIsOpen} ariaHideApp={false} onRequestClose={closeModal} style={customStyles}>
                            <S.Content verified={verified}>
                                <h1>Verificar identidade do beneficiário</h1>
                                <ReactSVG src="/faturamento/assets/icons/biometric-blue.svg" alt="Biometria" wrapper="span" />
                                <p>{verified ? 'Identidade verificada!' : 'Posicione o dedo sobre o sensor de impressão digital.'}</p>
                                <button onClick={() => closeModal()}>Cancelar</button>
                            </S.Content>
                        </Modal>
                    )}
                </>
            )}
        </S.Container>
    )
}

export default SelectBeneficiary

