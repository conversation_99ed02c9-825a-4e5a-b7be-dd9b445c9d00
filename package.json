{"name": "boilerplate-nextjs-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix"}, "dependencies": {"@emotion/react": "^11.9.0", "@emotion/styled": "^11.8.1", "@material-ui/core": "^5.0.0-beta.5", "@material-ui/icons": "^4.11.3", "@material-ui/styles": "^4.11.5", "@mui/icons-material": "^5.11.11", "@mui/lab": "^5.0.0-alpha.124", "@mui/material": "^5.8.0", "@mui/styles": "^5.8.0", "@types/lodash": "^4.14.197", "@types/react-query": "^1.2.9", "@types/uuid": "^8.3.4", "axios": "^0.27.2", "date-fns": "^2.28.0", "js-cookie": "^3.0.1", "jwt-decode": "^3.1.2", "lodash": "^4.17.21", "lottie-web": "^5.9.6", "moment": "^2.29.4", "next": "^12.1.6", "next-images": "^1.8.4", "polished": "^4.2.2", "react": "^18.1.0", "react-datepicker": "^4.8.0", "react-dom": "^18.1.0", "react-drag-drop-files": "2.3.7", "react-elastic-carousel": "0.11.5", "react-file-drop": "^3.1.3", "react-icons": "^4.3.1", "react-loading-skeleton": "^3.1.0", "react-modal": "^3.14.4", "react-query": "^3.39.3", "react-responsive": "^9.0.0-beta.8", "react-responsive-carousel": "^3.2.23", "react-select": "^5.8.0", "react-select-async-paginate": "^0.7.5", "react-spring": "^9.5.0", "react-svg": "^14.1.19", "react-toastify": "^9.0.1", "uuid": "^8.3.2", "styled-components": "^6.1.16"}, "devDependencies": {"@types/react": "npm:types-react@rc", "@babel/core": "^7.17.9", "@babel/preset-typescript": "^7.16.7", "@types/js-cookie": "^3.0.2", "@types/node": "^17.0.29", "@typescript-eslint/eslint-plugin": "^5.21.0", "@typescript-eslint/parser": "^5.21.0", "babel-plugin-styled-components": "^2.0.7", "eslint": "8.14.0", "eslint-config-next": "12.1.5", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.29.4", "eslint-plugin-react-hooks": "^4.5.0", "prettier": "2.6.2", "typescript": "^4.6.3", "typescript-styled-plugin": "^0.18.2", "@types/styled-components": "^5.1.34"}, "resolutions": {"@babel/preset-typescript": "^7.16.7"}}