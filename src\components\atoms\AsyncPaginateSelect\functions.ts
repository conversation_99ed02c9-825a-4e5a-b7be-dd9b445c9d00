import { IPageResult } from 'src/types/pagination'

export interface IElements<T, Label, Value> {
    label: Label
    value: Value
    element?: T
}

interface Adicional {
    page: number
}

export interface IRetorno<T, Label, Value> {
    options: IElements<T, Label, Value>[]
    hasMore: boolean
    additional: Adicional
}

export function parserLoadOption<T, Label extends keyof T, Value extends keyof T>(
    data: IPageResult<T>,
    label: Label,
    value: Value,
    element?: boolean
): IRetorno<T, T[Label], T[Value]> {
    return {
        options: data?.content?.map((obj) => ({
            label: obj?.[label],
            value: obj?.[value],
            element: element ? obj : undefined
        })),
        hasMore: !data?.last,
        additional: {
            page: data?.pageable?.pageNumber + 1
        }
    }
}
