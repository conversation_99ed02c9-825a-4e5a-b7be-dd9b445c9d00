import { apiAnaliseContas } from 'src/services/apis/apiAnaliseContas'
import { IPage, ISortPage } from 'types/pagination'
import { ObjectUtils } from 'utils/objectUtils'
import { ICalendarioRecursoGlosaDTO, ICalendarioRecursoGlosaForm } from './types'

const baseUrl = '/calendario-recurso-glosa'

export class CalendarioRecursoGlosaService {
    static async get(
        props: {
            isCalendarioGeral?: boolean
            prestadorId?: string
            competencia?: Date
        } & ISortPage
    ) {
        const params = ObjectUtils.propsToParams(props)
        const url = props ? `${baseUrl}?${params}` : baseUrl
        return apiAnaliseContas.get<{ content: ICalendarioRecursoGlosaDTO[] } & IPage>(url)
    }

    static async post(form: ICalendarioRecursoGlosaForm) {
        return apiAnaliseContas.post<ICalendarioRecursoGlosaDTO>(baseUrl, form)
    }

    static async getPrestador(props: { competencia: Date; prestadorId: string }) {
        const params = ObjectUtils.propsToParams(props)
        const url = `${baseUrl}/prestador?${params}`
        return apiAnaliseContas.get<ICalendarioRecursoGlosaDTO>(url)
    }

    static async delete({ uuid }: { uuid: string }) {
        return apiAnaliseContas.delete(`${baseUrl}/${uuid}`)
    }

    static async getById({ uuid }: { uuid: string }) {
        return apiAnaliseContas.get<ICalendarioRecursoGlosaDTO>(`${baseUrl}/${uuid}`)
    }
}
