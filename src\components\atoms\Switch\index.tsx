import React from 'react'
import * as S from './styles'

const Switch = ({ isOn, handleToggle, disabled = false }) => {
    return (
        <>
            <S.CheckBoxWrapper disabled={disabled}>
                <S.CheckBox checked={isOn} onChange={handleToggle} id="checkbox" type="checkbox" />
                <S.CheckBoxLabel htmlFor="checkbox" />
            </S.CheckBoxWrapper>
        </>
    )
}

export default Switch
