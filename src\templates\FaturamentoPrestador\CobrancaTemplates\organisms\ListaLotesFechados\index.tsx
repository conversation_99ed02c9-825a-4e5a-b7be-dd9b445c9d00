import InfoWithIconDropLote from 'components/atoms/InfoWithIconDropLote'
import ButtonDropLots from 'components/molecules/Buttons/ButtonDownloadLots'
import DropLote from 'components/molecules/DropLote'
import NoContent from 'components/molecules/NoContent'
import Pagination from 'components/molecules/Pagination'
import { useCallback, useEffect, useState } from 'react'
import { useAuth } from 'src/hooks/auth'
import { useToast } from 'src/hooks/toast'
import { CobrancaServices, IGetCobrancaProps } from 'src/services/faturamentoPrestadorApi/cobrancaServices'
import { IPageLote } from 'types/cobrancaPrestador/loteCobranca'
import { IPrestadorAnaliseQuery } from 'types/cobrancaPrestador/prestadorAnalise'
import { ILoteCobrancaFechamentoQuery, IPageLoteFechamento } from 'types/cobrancaPrestador/visaoFechamento'
import { SituacaoLote, SituacaoNotaFiscal } from 'types/common/enums'
import { IPagination } from 'types/common/pagination'
import { DateUtils } from 'utils/dateUtils'
import { PaginationHelper } from 'utils/helpers/paginationHelper'
import { NumberUtils } from 'utils/numberUtils'
import { capitalize } from 'utils/stringUtils'
import ModalAdicionarNotaFiscal from '../ModalAdicionarNotaFiscal'
import ModalConciliarNotaFiscal from '../ModalConciliarNotaFiscal'
import * as S from './styles'
import { ILoteCobrancaDTO } from 'types/cobrancaPrestador/loteCobranca'

type ListaLotesFechadosProps = {
    competenciaSelecionada: IPrestadorAnaliseQuery
    searchLote: string
    forceUpdate: boolean
    refreshList: boolean
    setLotes: React.Dispatch<React.SetStateAction<IPageLoteFechamento>>
    lotes: IPageLoteFechamento
}

type LotesChecked = {
    index: number
    checked: boolean
    idLote: string
}

const ListaLotesFechados = ({ competenciaSelecionada, searchLote = '', forceUpdate, setLotes, lotes, refreshList }: ListaLotesFechadosProps) => {
    const { addToast } = useToast()
    const { prestadorVinculado } = useAuth()

    const [isOpenModalEnviarNotaFiscal, setIsOpenModalEnviarNotaFiscal] = useState(false)
    const [isOpenModalEnviarNotaFiscalMultiplosLotes, setIsOpenModalEnviarNotaFiscalMultiplosLotes] = useState(false)
    const [fileUpload, setFileUpload] = useState<File | null>(null)
    // const [pageLotes, setPageLotes] = useState<IPageLoteFechamento>()
    const [pagination, setPagination] = useState<IPagination>()
    const [numberPage, setNumberPage] = useState(0)
    const [filtro, setFiltro] = useState<string>(searchLote)
    const [todosLotesSelecionados, setTodosLotesSelecionados] = useState(false)
    const [lotesChecks, setLotesChecks] = useState<LotesChecked[]>([])

    // 'Status da nota'

    const labels: string[] = ['Lote', 'Valor apresentado', 'Valor apurado', 'Valor glosado', 'Envio']

    const carregarLotes = useCallback(
        (filter?: string, page?: number) => {
            const getProps: IGetCobrancaProps =
                filter === ''
                    ? {
                          size: 10,
                          page: page || 0
                      }
                    : {
                          size: 10,
                          page: page || 0,
                          filtroNumeroLote: filter
                      }

            if (competenciaSelecionada?.competencia && prestadorVinculado?.uuid) {
                CobrancaServices.getLotesVisaoFechamento(competenciaSelecionada.competencia, prestadorVinculado?.uuid, getProps).then(({ data }) => {
                    setLotes(data)
                    initLotesChecks(data?.content)

                    const objectPagination = PaginationHelper.parserPagination<ILoteCobrancaFechamentoQuery>(data, setNumberPage)
                    setPagination(objectPagination)
                })
            }
        },
        [competenciaSelecionada, prestadorVinculado]
    )

    const initLotesChecks = (data: ILoteCobrancaFechamentoQuery[]) => {
        const lotesChecks: LotesChecked[] = []
        data?.forEach((item, index) => {
            if (item.situacaoNotaFiscal !== SituacaoNotaFiscal.ENVIADA) {
                lotesChecks.push({
                    index,
                    checked: false,
                    idLote: item.loteCobrancaId
                })
            }
        })
        setLotesChecks(lotesChecks)
    }

    // const marcarTodos = () => {
    //     setLotesChecks(lotesChecks.map((item) => ({ ...item, checked: !todosLotesSelecionados })))
    // }

    // useEffect(() => carregarLotes('', numberPage), [forceUpdate, searchLote])

    useEffect(() => {
        carregarLotes(filtro, numberPage)
    }, [numberPage, competenciaSelecionada, refreshList])

    // const handleClickPesquisar = () => {
    //     carregarLotes(filtro, numberPage)
    // }

    useEffect(() => {
        if (!lotes) return

        setTodosLotesSelecionados(
            lotesChecks.filter((i) => i.checked).length === lotes?.content.filter((i) => i?.situacaoNotaFiscal !== SituacaoNotaFiscal.ENVIADA).length
        )
    }, [lotesChecks, lotes])

    const handleDownloadProtocoloRecebimentoXml = (idLote) => {
        CobrancaServices.getProtocoloRecebimentoXml(idLote)
            .then((response) => {
                const fileName = response?.headers['content-disposition']?.split('filename=')[1] ?? 'download.xml'
                const url = window.URL.createObjectURL(new Blob([response?.data]))
                const link = document.createElement('a')
                link.href = url
                link.setAttribute('download', fileName) //or any other extension
                document.body.appendChild(link)
                link.click()
            })
            .catch(() => {
                addToast({ title: 'Erro ao fazer download', type: 'error', duration: 3000 })
            })
    }

    const handleDownloadProtocoloRecebimentoPdf = (idLote) => {
        CobrancaServices.getProtocoloRecebimentoPdf(idLote)
            .then((response) => {
                const fileName = response?.headers['content-disposition']?.split('filename=')?.[1] ?? 'download.pdf'
                const url = window.URL.createObjectURL(response?.data)
                const link = document.createElement('a')
                link.href = url
                link.setAttribute('download', fileName) //or any other extension
                document.body.appendChild(link)
                link.click()
            })
            .catch(() => {
                addToast({ title: 'Erro ao fazer download', type: 'error', duration: 3000 })
            })
    }

    // const handleDownloadDemonstrativoAnaliseXml = (idLote) => {
    //     CobrancaServices.getDemonstrativoAnaliseXml(idLote)
    //         .then((response) => {
    //             const fileName = response.headers['content-disposition'].split('filename=')[1] ?? 'download.xml'
    //             const url = window.URL.createObjectURL(new Blob([response.data]))
    //             const link = document.createElement('a')
    //             link.href = url
    //             link.setAttribute('download', fileName) //or any other extension
    //             document.body.appendChild(link)
    //             link.click()
    //         })
    //         .catch(() => {
    //             addToast({ title: 'Erro ao fazer download', type: 'error', duration: 3000 })
    //         })
    // }

    // const handleDownloadDemonstrativoAnaliseXlsx = (idLote) => {
    //     CobrancaServices.getDemonstrativoAnaliseXlsx(idLote)
    //         .then((response) => {
    //             const fileName = response.headers['content-disposition'].split('filename=')[1] ?? 'download.xlxs'
    //             const url = window.URL.createObjectURL(new Blob([response.data]))
    //             const link = document.createElement('a')
    //             link.href = url
    //             link.setAttribute('download', fileName) //or any other extension
    //             document.body.appendChild(link)
    //             link.click()
    //         })
    //         .catch(() => {
    //             addToast({ title: 'Erro ao fazer download', type: 'error', duration: 3000 })
    //         })
    // }

    // const handleDownloadDemonstrativoAnalisePdf = (idLote) => {
    //     CobrancaServices.getDemonstrativoAnalisePdf(idLote)
    //         .then((response) => {
    //             const fileName = response.headers['content-disposition'].split('filename=')[1] ?? 'download.pdf'
    //             const url = window.URL.createObjectURL(response.data)
    //             const link = document.createElement('a')
    //             link.href = url
    //             link.setAttribute('download', fileName) //or any other extension
    //             document.body.appendChild(link)
    //             link.click()
    //         })
    //         .catch(() => {
    //             addToast({ title: 'Erro ao fazer download', type: 'error', duration: 3000 })
    //         })
    // }

    // const handleDownloadNotaFiscal = (idLote) => {
    //     CobrancaServices.getNotaFiscalDownloadPorLote(idLote)
    //         .then((response) => {
    //             const fileName = response.headers['content-disposition'].split('filename=')[1] ?? 'download.pdf'
    //             const url = window.URL.createObjectURL(response.data)
    //             const link = document.createElement('a')
    //             link.href = url
    //             link.setAttribute('download', fileName) //or any other extension
    //             document.body.appendChild(link)
    //             link.click()
    //         })
    //         .catch(() => {
    //             addToast({ title: 'Erro ao fazer download', type: 'error', duration: 3000 })
    //         })
    // }

    // useEffect(() => {
    //     if (!data) return
    //     const objectPagination = PaginationHelper.parserPagination<ILoteCobrancaFechamentoQuery>(data, setNumberPage)
    //     setPagination(objectPagination)
    // }, [data])

    function createDropLot(item: ILoteCobrancaFechamentoQuery, index: number) {
        return [
            {
                component: (
                    <div className="itemClose">
                        {/* <div style={{ width: '10px', marginLeft: '5px' }}>
                            {item.situacaoNotaFiscal !== SituacaoNotaFiscal.ENVIADA ? (
                                <input
                                    value={item.loteCobrancaId}
                                    type="checkbox"
                                    id={item.loteCobrancaId}
                                    onChange={() => {
                                        const newLotsCheckeds = [...lotesChecks]

                                        if (!newLotsCheckeds) return

                                        // if (!newLotsCheckeds[index]) return

                                        newLotsCheckeds.find((i) => i.index === index).checked = !newLotsCheckeds.find((i) => i.index === index)
                                            ?.checked
                                        setLotesChecks(newLotsCheckeds)
                                    }}
                                    checked={lotesChecks.find((i) => i.index === index)?.checked}
                                />
                            ) : null}
                        </div> */}
                        <p>{item?.numeroLote}</p>
                        {/* {item.situacaoLote === SituacaoLote.AGUARDANDO_DOCUMENTACAO && (
                            <div className="item-new">
                                <span>Novo</span>
                            </div>
                        )} */}
                    </div>
                )
            },
            {
                component: (
                    <div className="itemClose">
                        <p className={item?.situacaoLote === SituacaoLote.AGUARDANDO_DOCUMENTACAO ? 'text-new-bold' : undefined}>
                            {NumberUtils.maskMoney(item?.valorApresentado)}
                        </p>
                    </div>
                )
            },
            {
                component: (
                    <div className="itemClose">
                        <p className={item?.situacaoLote === SituacaoLote.AGUARDANDO_DOCUMENTACAO ? 'text-new-bold' : undefined}>
                            {NumberUtils.maskMoney(item?.valorApurado)}
                        </p>
                    </div>
                )
            },
            {
                component: (
                    <div className="itemClose">
                        <p className={item?.situacaoLote === SituacaoLote.AGUARDANDO_DOCUMENTACAO ? 'text-new-bold' : undefined}>
                            {NumberUtils.maskMoney(item?.valorGlosado)}
                        </p>
                    </div>
                )
            },
            // {
            //     component: (
            //         <Badge
            //             background={
            //                 item.situacaoNotaFiscal === SituacaoNotaFiscal.NAO_SOLICITADA ? 'rgba(0, 138, 210, 0.16)' : 'rgba(244, 67, 54, 0.16)'
            //             }
            //             color={item.situacaoNotaFiscal === SituacaoNotaFiscal.CONFIRMADA ? 'rgba(0, 138, 210, 1)' : 'rgba(244, 67, 54, 1)'}
            //             text={item.situacaoNotaFiscal}
            //             style={{ padding: '0px 8px' }}
            //         />
            //     )
            // },
            {
                component: (
                    <div>
                        <p>{item?.tipoEnvio === 'ELETRONICO' ? 'Eletrônico' : capitalize(item?.tipoEnvio)}</p>
                    </div>
                )
            }
        ]
    }

    const handleClickGerarNota = (idLote, numeroNota, valorNota, dataEmissao, file) => {
        CobrancaServices.postNotaFiscalParaLotes({
            idLotes: idLote,
            numeroNota: numeroNota,
            valor: NumberUtils.unMaskMoney(valorNota)?.toString(),
            dataEmissao,
            arquivos: [file]
        })
            .then(({ data }) => {
                addToast({ title: 'Nota fiscal enviada com sucesso.', type: 'success' })

                carregarLotes(filtro, numberPage)
            })
            .catch(() => {
                addToast({ title: 'Ocorreu erro ao enviar a nota fiscal. Tente novamente.', type: 'error' })
            })

        setIsOpenModalEnviarNotaFiscal(!isOpenModalEnviarNotaFiscal)
    }

    // const handleClickGerarNotaMultiplosLotes = (numeroNota, valorNota, dataEmissao, file) => {

    //     CobrancaServices.postNotaFiscalParaLotes({
    //         idLotes: idLotes,
    //         numeroNota: numeroNota,
    //         valor: NumberUtils.unMaskMoney(valorNota).toString(),
    //         dataEmissao,
    //         arquivo: file
    //     })
    //         .then(({ data }) => {
    //             addToast({ title: 'Nota fiscal enviada com sucesso.', type: 'success' })

    //             carregarLotes(filtro, numberPage)
    //         })
    //         .catch(() => {
    //             addToast({ title: 'Ocorreu erro ao enviar a nota fiscal. Tente novamente.', type: 'error' })
    //         })

    //     setIsOpenModalEnviarNotaFiscalMultiplosLotes(!isOpenModalEnviarNotaFiscalMultiplosLotes)
    // }

    const [idLoteParaConciliacao, setIdLoteParaConciliacao] = useState<string | string[]>()
    const [isOpenModalConciliacao, setIsOpenModalConciliacao] = useState(false)
    const [isOpenModalConciliacaoMultiplos, setIsOpenModalConciliacaoMultiplos] = useState(false)

    const handleClickConciliarNota = (idLote: string) => {
        setIdLoteParaConciliacao(idLote)
        setIsOpenModalConciliacao(true)
    }
    const handleClickConciliarNotaMultiplos = (idLote: string[]) => {
        setIdLoteParaConciliacao(idLote)
        setIsOpenModalConciliacaoMultiplos(true)
    }

    return (
        <S.Wrapper>
            {!lotes || lotes?.content?.length === 0 ? (
                <NoContent title="No momento não existe nenhum lote fechado" path="/parametros/regras-de-excludencia/nova-regra" />
            ) : (
                <>
                    <div className="contentInput">
                        {/* <Input placeholder="Procurar" handleClickIconRight={handleClickPesquisar} handleOnChange={(e) => setFiltro(e.target.value)} /> */}
                        <div className="contentNumber">
                            <span className="pageNumber">
                                {pagination?.paginaAtual * pagination?.linhasPorPagina + 1} -{' '}
                                {lotes?.content?.length + pagination?.paginaAtual * pagination?.linhasPorPagina}
                                {' de '}
                                {pagination?.totalRegistros}
                            </span>
                        </div>
                    </div>

                    {/* <S.ContentConciliarNotas> */}
                    {/* <div className="checkLotLabel">
                            <input
                                value={'01'}
                                type="checkbox"
                                id={'01'}
                                onChange={() => {
                                    marcarTodos()
                                }}
                                checked={todosLotesSelecionados}
                            />
                        </div>
                        <p>Conciliar nota fiscal para mais de um lote?</p> */}

                    {/* {lotesChecks?.some((i) => i.checked) ? (
                            <Button
                                className="btnEnviar"
                                themeButton="secondary"
                                style={{ padding: '12px 24px' }}
                                onClick={() => {
                                    setIsOpenModalEnviarNotaFiscalMultiplosLotes(true)
                                }}
                            >
                                Enviar NF
                            </Button>
                        ) : null} */}
                    {/* </S.ContentConciliarNotas> */}

                    <S.HeaderLabel>
                        {labels?.map((item, index) => (
                            <div key={index}>
                                <p>{item}</p>
                            </div>
                        ))}
                    </S.HeaderLabel>

                    {lotes?.content?.map((lote, index: number) => {
                        return (
                            <DropLote items={createDropLot(lote, index)} key={index}>
                                <ModalAdicionarNotaFiscal
                                    isOpen={isOpenModalEnviarNotaFiscal}
                                    setIsOpen={setIsOpenModalEnviarNotaFiscal}
                                    fileUpload={fileUpload}
                                    setFileUpload={setFileUpload}
                                    onClick={(numeroNota, valorNota, dataEmissao, file) => {
                                        handleClickGerarNota([lote?.loteCobrancaId], numeroNota, valorNota, dataEmissao, file)
                                    }}
                                    onClickConciliarNotaFiscal={(idLote) => {
                                        handleClickConciliarNota(idLote as string)
                                    }}
                                    idsLoteCobranca={lote?.loteCobrancaId}
                                />
                                <S.ContentDropLot>
                                    <div className="content-left">
                                        <InfoWithIconDropLote
                                            icon="/faturamento/assets/icons/person.svg"
                                            title="Lote enviado por"
                                            description={lote?.nomeUsuario}
                                        />
                                        <InfoWithIconDropLote
                                            icon="/faturamento/assets/icons/calender.svg"
                                            title="Data de envio"
                                            description={DateUtils.formatDateTimePTBR(lote?.dataEnvio)}
                                        />
                                        <InfoWithIconDropLote
                                            icon="/faturamento/assets/icons/calender.svg"
                                            title="Data de processamento"
                                            description={DateUtils.formatDateTimePTBR(lote?.dataProcessamento)}
                                        />
                                    </div>
                                    <S.ContentRight>
                                        <div className="row-one">
                                            {/* <div className="container-item-buttons">
                                                <label className="title">Demonstrativo de análise</label>
                                                <S.RowButtons>
                                                    <ButtonDropLots icon={'/faturamento/assets/icons/download.svg'}>
                                                        <span onClick={() => handleDownloadDemonstrativoAnalisePdf(lote.loteCobrancaId)}>
                                                            Baixar PDF
                                                        </span>
                                                    </ButtonDropLots>
                                                    <ButtonDropLots icon={'/faturamento/assets/icons/download.svg'}>
                                                        <span onClick={() => handleDownloadDemonstrativoAnaliseXml(lote.loteCobrancaId)}>
                                                            Baixar XML
                                                        </span>
                                                    </ButtonDropLots>
                                                    <ButtonDropLots icon={'/faturamento/assets/icons/download.svg'}>
                                                        <span onClick={() => handleDownloadDemonstrativoAnaliseXlsx(lote.loteCobrancaId)}>
                                                            Baixar XLS
                                                        </span>
                                                    </ButtonDropLots>
                                                </S.RowButtons>
                                            </div> */}
                                            <div className="container-item-buttons">
                                                <label className="title">Protocolo de recebimento</label>
                                                <S.RowButtons>
                                                    {lote?.tipoEnvio !== 'MANUAL' && (
                                                        <ButtonDropLots icon={'/faturamento/assets/icons/download.svg'}>
                                                            <span onClick={() => handleDownloadProtocoloRecebimentoXml(lote?.loteCobrancaId)}>
                                                                Baixar XML
                                                            </span>
                                                        </ButtonDropLots>
                                                    )}
                                                    <ButtonDropLots icon={'/faturamento/assets/icons/download.svg'}>
                                                        <span onClick={() => handleDownloadProtocoloRecebimentoPdf(lote?.loteCobrancaId)}>
                                                            Baixar PDF
                                                        </span>
                                                    </ButtonDropLots>
                                                </S.RowButtons>
                                            </div>
                                        </div>
                                        {/* <div className="row-two">
                                            <label className="title">Nota Fiscal</label>
                                            {lote.situacaoNotaFiscal === SituacaoNotaFiscal.ENVIADA ? (
                                                lote.arquivoNotaFiscal && (
                                                    <InfoWithIconDropLote
                                                        icon="/faturamento/assets/icons/pdfIcon.svg"
                                                        title="Arquivo"
                                                        description={lote.arquivoNotaFiscal}
                                                        onClickDescription={() => handleDownloadNotaFiscal(lote.loteCobrancaId)}
                                                    />
                                                )
                                            ) : (
                                                <Button
                                                    themeButton="secondary"
                                                    style={{ padding: '12px 24px' }}
                                                    disabled={lotesChecks.find((i) => i.index === index)?.checked}
                                                    onClick={() => {
                                                        setIsOpenModalEnviarNotaFiscal(true)
                                                    }}
                                                >
                                                    Enviar NF
                                                </Button>
                                            )}
                                        </div> */}
                                    </S.ContentRight>
                                </S.ContentDropLot>
                            </DropLote>
                        )
                    })}

                    <div style={{ marginTop: '40px' }}>
                        <Pagination
                            totalPage={pagination?.totalPaginas}
                            totalRegister={pagination?.totalRegistros}
                            actualPage={pagination?.paginaAtual}
                            setNumberPage={pagination?.setNumberPage}
                        />
                    </div>

                    <ModalAdicionarNotaFiscal
                        isOpen={isOpenModalEnviarNotaFiscalMultiplosLotes}
                        setIsOpen={setIsOpenModalEnviarNotaFiscalMultiplosLotes}
                        fileUpload={fileUpload}
                        setFileUpload={setFileUpload}
                        onClick={(numeroNota, valorNota, dataEmissao, file) => {
                            const idLotes: string[] = lotesChecks?.filter((i) => i?.checked)?.map((i) => i?.idLote)
                            handleClickGerarNota(idLotes, numeroNota, valorNota, dataEmissao, file)
                        }}
                        onClickConciliarNotaFiscal={(idLote) => {
                            handleClickConciliarNotaMultiplos(idLote as string[])
                        }}
                        idsLoteCobranca={lotesChecks?.filter((i) => i?.checked)?.map((i) => i?.idLote)}
                    />
                    <ModalConciliarNotaFiscal
                        isOpen={isOpenModalConciliacao}
                        idLote={[idLoteParaConciliacao as string]}
                        tipoLote="cobranca"
                        setIsOpen={setIsOpenModalConciliacao}
                        competencia={competenciaSelecionada?.competencia}
                    />
                    <ModalConciliarNotaFiscal
                        isOpen={isOpenModalConciliacaoMultiplos}
                        idLote={idLoteParaConciliacao as string[]}
                        tipoLote="cobranca"
                        setIsOpen={setIsOpenModalConciliacaoMultiplos}
                        competencia={competenciaSelecionada?.competencia}
                    />
                </>
            )}
        </S.Wrapper>
    )
}

export default ListaLotesFechados
