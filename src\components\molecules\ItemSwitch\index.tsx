import Switch from 'components/atoms/Switch'
import * as S from './styles'

interface ItemSwitchProps {
    isOn: boolean
    handleToggle: () => void
    text: string
    className?: string
    disabled?: boolean
}

const ItemSwitch = ({ isOn, handleToggle, text, className, disabled = false }: ItemSwitchProps) => {
    return (
        <S.ContentActiveItems className={className} disabled={disabled}>
            <span>{text}</span>
            <S.SwitchContent>
                <Switch isOn={isOn} handleToggle={handleToggle} disabled={disabled} />
            </S.SwitchContent>
        </S.ContentActiveItems>
    )
}

export default ItemSwitch
