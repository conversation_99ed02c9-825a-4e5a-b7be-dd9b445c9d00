import { moduloEnum, situacaoEnum, tipoCalendarioEnum } from './enums'

export interface ICalendarioDTO {
    diaFim: number
    diaInicio: number
    modulo: moduloEnum
    prazoCobranca: number
    prestadorId: string
    tipoCalendario: tipoCalendarioEnum
    uuid: string
}

export interface ICalendarioForm {
    diaFim?: number
    diaInicio: number
    diasEmAberto: number
    modulo: moduloEnum
    prazoCobranca: number
    prestadorId: string
}

export interface ICompetenciaDTO {
    compentencia: Date
    descricaoMes: string
    situacao: situacaoEnum
}

export interface ICompetenciaCalendarioDTO {
    competencia: Date
    dataInicioEnvio: Date
    dataLimiteEnvio: Date
    mesCompetencia: string
}

export interface IDatasCobrancaDTO {
    dataAbertura: Date
    dataFechamento: Date
    dataLimiteEnvio: Date
    dataPagamento: Date
    horarioLimiteEnviolote: ILocalTime
}

export interface ILocalTime {
    hour: number
    minute: number
    nano: number
    second: number
}

export interface ICalendarioHistoricoAlteracaoDTO {
    dataHoraAlteracao: string
    entidade: string
    nomeUsuario: string
    tipoAlteracao: string
}
