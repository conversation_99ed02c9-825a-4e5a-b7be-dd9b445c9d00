import { SituacaoLote } from 'types/common/enums'
import { SituacaoProcessamentoLoteEnum } from './../../../../services/processadorRegrasApi/RegistrosProcessamentoContas/enums'
import styled from 'styled-components'

const ThemeConfigs = {
    [SituacaoProcessamentoLoteEnum.FALHA]: {
        bgColor: 'rgba(244, 67, 54, 0.04)',
        textColor: '#FF584C'
    },
    [SituacaoProcessamentoLoteEnum.AGENDADO]: { bgColor: 'rgba(0, 80, 229, 0.08)', textColor: '#0050E5' },
    [SituacaoProcessamentoLoteEnum.EM_PROCESSAMENTO]: { bgColor: 'rgba(0, 80, 229, 0.08)', textColor: '#0050E5' },
    [SituacaoProcessamentoLoteEnum.PROCESSADO]: { bgColor: 'rgba(0, 80, 229, 0.08)', textColor: '#0050E5' },
    [SituacaoLote.RECUSADO]: { bgColor: 'rgba(0, 80, 229, 0.08)', textColor: '#0050E5' },
    [SituacaoLote.CANCELADO]: { bgColor: 'rgba(0, 80, 229, 0.08)', textColor: '#0050E5' }
}

type ContainerProps = {
    badgeTheme: SituacaoLote | SituacaoProcessamentoLoteEnum
}
export const Container = styled.div<ContainerProps>`
    width: fit-content;
    border-radius: 0.4rem;
    padding: 0.8rem;
    border: 1px solid ${({ badgeTheme }) => ThemeConfigs[badgeTheme]?.textColor};
    background-color: ${({ badgeTheme }) => ThemeConfigs[badgeTheme]?.bgColor};

    > p {
        font-size: 1.2rem;
        line-height: 1.6rem;
        font-weight: 400;
        color: ${({ badgeTheme }) => ThemeConfigs[badgeTheme]?.textColor};
    }
`
