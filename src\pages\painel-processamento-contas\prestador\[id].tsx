import React from 'react'
import Layout from 'components/molecules/Layout'
import { GetServerSideProps } from 'next'
import ProcessamentoContasPrestadorTemplate from 'src/templates/PainelProcessamentoContasTemplate/ListaProcessamentoPrestadores/ProcessamentoContasPrestadorTemplate'
import { SituacaoProcessamentoLoteEnum } from 'src/services/processadorRegrasApi/RegistrosProcessamentoContas/enums'
import { SituacaoLote } from 'types/common/enums'

type ProcessamentoContasPrestadorProps = {
    prestadorId: string
    competencia: string
    situacao: SituacaoProcessamentoLoteEnum | SituacaoLote
}
const ProcessamentoContasPrestador = (props: ProcessamentoContasPrestadorProps) => {
    return <ProcessamentoContasPrestadorTemplate {...props} />
}

ProcessamentoContasPrestador.getLayout = function getLayout(page: JSX.Element) {
    return <Layout isLoggedIn={false}>{page}</Layout>
}

export const getServerSideProps: GetServerSideProps = async (ctx) => {
    const prestadorId = ctx.query?.id
    const situacao = ctx.query?.situacao
    const competencia = ctx.query?.competencia

    return {
        props: {
            prestadorId,
            situacao,
            competencia
        }
    }
}

export default ProcessamentoContasPrestador
