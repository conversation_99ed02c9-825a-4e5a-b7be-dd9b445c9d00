import React, { useEffect, useState } from 'react'
import { ReactSVG } from 'react-svg'
import { useRouter } from 'next/router'
import Item from 'components/atoms/Item'
import Modal from 'components/atoms/Modal'
import Button from 'components/atoms/Button'
import Layout from 'components/molecules/Layout'
import NoContent from 'components/molecules/NoContent'
import { useToast } from 'src/hooks/toast'
import HistoryContent from 'components/molecules/HistoryContent'
import { TitleSection } from 'components/atoms/TitleSection/styles'
import DividerSectionCard from 'components/atoms/DividerSectionCard'
import { CalendarioService } from 'src/services/analiseContasApi/Calendario'
import * as S from './styles'
import { StringUtils } from 'utils/stringUtils'
import { PrestadorService } from 'src/services/faturamentoOperadoraApi/prestadorServices'
import { ICalendarioDTO, ICalendarioHistoricoAlteracaoDTO } from 'src/services/analiseContasApi/Calendario/types'
import { IPage } from 'types/pagination'
import { ICalendarioRecursoGlosaDTO } from 'src/services/analiseContasApi/CalendarioRecursoGlosa/types'
import { CalendarioRecursoGlosaService } from 'src/services/analiseContasApi/CalendarioRecursoGlosa'
import { DateUtils } from 'utils/dateUtils'
import Pagination from 'components/molecules/Pagination'
import dynamic from 'next/dynamic'

const AnimatedLoadingLottie = dynamic(() => import('components/atoms/AnimatedLoadingLottie'), { ssr: false })

const CalendarioFaturamentoTemplates = () => {
    // const router = useRouter()
    const [isModalOpen, setIsModalOpen] = useState(false)
    const [isRecurso, setIsRecurso] = useState(false)
    const [indexItem, setIndexItem] = useState<string>()
    const [loading, setLoading] = useState(true)
    const [requestData, setRequestData] = useState<
        {
            content: ICalendarioDTO[]
        } & IPage
    >()
    const [calendarios, setCalendarios] = useState<Array<ICalendarioDTO & { nomePrestador?: string }>>([])
    const [historicoData, setHistoricoData] = useState<ICalendarioHistoricoAlteracaoDTO[]>()
    const [calendariosRecurso, setCalendariosRecurso] = useState<
        {
            content: Array<ICalendarioRecursoGlosaDTO & { nomePrestador?: string }>
        } & IPage
    >()
    const [pageCalendario, setPageCalendario] = useState<number>(0)
    const [pageCalendarioRecurso, setPageCalendarioRecurso] = useState<number>(0)
    const [pageHistorico, setPageHistorico] = useState<number>(0)
    const { addToast } = useToast()

    const route = useRouter()

    useEffect(() => {
        if (!requestData) return

        const load = async () => {
            const items = await Promise.all(
                requestData?.content?.map(async (calendario) => {
                    if (calendario.tipoCalendario === 'ESPECIFICO') {
                        const { data } = await PrestadorService.getByID(calendario.prestadorId)
                        const nomePrestador = data?.nomeFantasia || data?.nomeCompleto

                        return { ...calendario, nomePrestador }
                    }
                    return calendario
                })
            )

            setCalendarios(items)
        }

        load()
    }, [requestData])

    function mapeandoPeriodos(periodos: ICalendarioDTO, limite?: number) {
        let container = []

        container = [`${periodos?.diaInicio} a ${periodos?.diaFim}`]
        const periodoFormatado = container?.slice(0, limite).join(' / ')

        return periodoFormatado
    }

    const PropsRemoveItem = (idCalendario: string, boolean: boolean, recurso?: boolean) => {
        setIsModalOpen(boolean)
        setIndexItem(idCalendario)
        setIsRecurso(recurso)
    }

    const removeItemGlosa = (uuid: string) => {
        CalendarioRecursoGlosaService.delete({ uuid })
            .then(() => {
                addToast({ title: 'Calendario excluido com sucesso!', type: 'success' })
                setPageCalendarioRecurso(0)
                loadCalendarioRecurso(0)
                setIsRecurso(false)
            })
            .catch(() => addToast({ title: 'Erro ao excluir o calendário', type: 'error' }))
        setIsModalOpen(false)
    }

    const removeItem = () => {
        CalendarioService.delete(indexItem)
            .then(() => {
                addToast({ title: 'Calendario excluido com sucesso!', type: 'success' })
                setPageCalendario(0)
                loadCalendario(0)
            })
            .catch(() => addToast({ title: 'Erro ao excluir o calendário', type: 'error' }))
        setIsModalOpen(false)
    }

    function loadCalendario(page: number) {
        CalendarioService.get({ page, size: 5 })
            .then(({ data }) => {
                setRequestData(data)
                setLoading(false)
            })
            .catch(() => {
                addToast({ title: 'Erro ao carregar os calendários', type: 'error' })
                setLoading(false)
            })
    }

    function loadCalendarioRecurso(page: number) {
        CalendarioRecursoGlosaService.get({ page, size: 5 })
            .then(async ({ data }) => {
                const dataPrestador = data?.content
                    ?.filter((element) => element?.prestadorId !== null)
                    ?.map((element) => PrestadorService.getByID(element.prestadorId).then((prestador) => prestador?.data))

                const resolvPrestador = await Promise.all(dataPrestador)
                return setCalendariosRecurso({
                    ...data,
                    content: data?.content.map((element) => ({
                        ...element,
                        nomePrestador: resolvPrestador
                            ?.filter((prestador) => prestador?.uuid === element?.prestadorId)
                            ?.map((prestador) => prestador?.nomeFantasia || prestador?.nomeCompleto)?.[0]
                    }))
                })
            })
            .catch(() => {
                addToast({ title: 'Erro ao carregar os calendários de glosa', type: 'error' })
                setLoading(false)
            })
    }

    function loadHistorico(page: number) {
        CalendarioService.getHistoricoAlteracoes({ page })
            .then(({ data }) => {
                setHistoricoData(data)
            })
            .catch(() => {
                addToast({ title: 'Erro ao carregar o histórico de alterações', type: 'error' })
                setLoading(false)
            })
    }

    React.useEffect(() => {
        if (pageCalendario !== undefined) loadCalendario(pageCalendario)
    }, [pageCalendario])

    React.useEffect(() => {
        if (pageCalendarioRecurso !== undefined) loadCalendarioRecurso(pageCalendarioRecurso)
    }, [pageCalendarioRecurso])

    React.useEffect(() => {
        if (pageHistorico !== undefined) loadHistorico(pageHistorico)
    }, [pageHistorico])

    return (
        <Layout isLoggedIn={true} title={'Calendário de Faturamento'}>
            {requestData?.content.length === 0 && calendariosRecurso?.content?.length === 0 && (
                <S.Content>
                    <NoContent
                        title={'No momento nenhum calendário de faturamento.'}
                        textButton={'Novo calendário'}
                        path="/calendario-faturamento/novo-calendario"
                    />
                </S.Content>
            )}

            {calendarios?.length !== 0 && (
                <DividerSectionCard>
                    <S.DivHeader>
                        <TitleSection>CALENDÁRIOS</TitleSection>
                        <Button
                            themeButton={'warning'}
                            style={{ width: '21%' }}
                            iconLeft={'/faturamento/assets/icons/plus.svg'}
                            onClick={() => route.push('/calendario-faturamento/novo-calendario')}
                        >
                            Novo calendário
                        </Button>
                    </S.DivHeader>

                    {loading ? (
                        <S.ContainerLottie>
                            <AnimatedLoadingLottie />
                        </S.ContainerLottie>
                    ) : (
                        <>
                            <S.ContentItem>
                                {/* <Item>
                                        <p>Códigos</p>
                                    </Item> */}
                                <Item>
                                    <p>Prestador</p>
                                </Item>
                                <Item>
                                    <p>Módulo</p>
                                </Item>
                                <Item>
                                    <p>Período(s)</p>
                                </Item>
                                <Item>
                                    <p>Data limite atendimento</p>
                                </Item>

                                <Item>
                                    <p>Ações</p>
                                </Item>
                            </S.ContentItem>
                            {calendarios?.map((obj, index) => (
                                <S.ContentItemResult key={`${Math.random()}+${index}`}>
                                    <>
                                        {/* <Item>
                                                <span>{obj?.uuid}</span>
                                            </Item> */}
                                        <Item>
                                            <span>{obj?.tipoCalendario === 'ESPECIFICO' ? obj.nomePrestador : 'GERAL'}</span>
                                        </Item>
                                        <Item>
                                            <span>{StringUtils.ajustandoString(obj?.modulo)}</span>
                                        </Item>
                                        <Item>
                                            <span title={mapeandoPeriodos(obj)}>{mapeandoPeriodos(obj, 3)}</span>
                                        </Item>
                                        <Item>
                                            <span>{obj?.prazoCobranca}</span>
                                        </Item>
                                    </>
                                    <Item>
                                        <S.WrapperIcons>
                                            <S.Icon onClick={() => route.push(`/calendario-faturamento/alterar-calendario/${obj?.uuid}`)}>
                                                <ReactSVG src="/faturamento/assets/icons/pencil-blue.svg" />
                                            </S.Icon>
                                            <S.Icon onClick={() => PropsRemoveItem(obj?.uuid, true)}>
                                                <ReactSVG src="/faturamento/assets/icons/trash-blue.svg" />
                                            </S.Icon>
                                        </S.WrapperIcons>
                                    </Item>
                                </S.ContentItemResult>
                            ))}
                            {requestData?.totalPages > 1 && (
                                <Pagination
                                    totalPage={requestData?.totalPages}
                                    totalRegister={requestData?.totalElements}
                                    actualPage={requestData?.number}
                                    setNumberPage={setPageCalendario}
                                />
                            )}
                        </>
                    )}
                </DividerSectionCard>
            )}

            {calendariosRecurso?.content?.length !== 0 && (
                <DividerSectionCard style={{ marginTop: '24px' }}>
                    <S.DivHeader>
                        <TitleSection>CALENDÁRIOS DE RECURSO DE GLOSA</TitleSection>
                        {calendarios?.length === 0 && (
                            <Button
                                themeButton={'warning'}
                                style={{ width: '21%' }}
                                iconLeft={'/faturamento/assets/icons/plus.svg'}
                                onClick={() => route.push('/calendario-faturamento/novo-calendario')}
                            >
                                Novo calendário
                            </Button>
                        )}
                    </S.DivHeader>
                    {loading ? (
                        <S.ContainerLottie>
                            <AnimatedLoadingLottie />
                        </S.ContainerLottie>
                    ) : (
                        <>
                            <S.ContentItem>
                                {/* <Item>
                                        <p>Códigos</p>
                                    </Item> */}
                                <Item>
                                    <p>Prestador</p>
                                </Item>
                                <Item>
                                    <p>Competência</p>
                                </Item>
                                <Item>
                                    <p>Período(s)</p>
                                </Item>
                                <div></div>
                                <Item>
                                    <p>Ações</p>
                                </Item>
                            </S.ContentItem>
                            {calendariosRecurso?.content?.map((obj, index) => (
                                <S.ContentItemResult key={`${Math.random()}+${index}`}>
                                    <>
                                        {/* <Item>
                                                <span>{obj?.uuid}</span>
                                            </Item> */}
                                        <Item>
                                            <span>{obj?.prestadorId ? obj?.nomePrestador : 'GERAL'}</span>
                                        </Item>
                                        <Item>
                                            <span>{DateUtils.getMonthFullName(obj?.competencia)}</span>
                                        </Item>
                                        <Item>
                                            <span>{`${obj?.dataInicio.split('T')?.[0]?.split('-').reverse().join('/')} a ${obj?.dataTermino
                                                .split('T')?.[0]
                                                ?.split('-')
                                                .reverse()
                                                .join('/')}`}</span>
                                        </Item>
                                        <div></div>
                                    </>
                                    <Item>
                                        <S.WrapperIcons>
                                            <S.Icon onClick={() => PropsRemoveItem(obj?.uuid, true, true)}>
                                                <ReactSVG src="/faturamento/assets/icons/trash-blue.svg" />
                                            </S.Icon>
                                        </S.WrapperIcons>
                                    </Item>
                                </S.ContentItemResult>
                            ))}
                        </>
                    )}
                    {calendariosRecurso?.totalPages > 1 && (
                        <Pagination
                            totalPage={calendariosRecurso?.totalPages}
                            totalRegister={calendariosRecurso?.totalElements}
                            actualPage={calendariosRecurso?.number}
                            setNumberPage={setPageCalendarioRecurso}
                        />
                    )}
                </DividerSectionCard>
            )}
            <HistoryContent data={historicoData} title={'HISTÓRICO'} />

            <Modal
                isOpen={isModalOpen}
                style={{ width: '45vw' }}
                onClose={() => {
                    setIsModalOpen(!isModalOpen)
                    setIsRecurso(false)
                }}
            >
                <S.ModalContainer>
                    <S.ModalTitle>Deletar calendário!</S.ModalTitle>
                    <S.ModalSubtitle>{`Você está preste a excluir o calendário. Tem certeza que deseja excluir este calendário?`}</S.ModalSubtitle>
                    <div>
                        <S.ContainerButtons>
                            <Button
                                typeButton="text"
                                style={{
                                    backgroundColor: 'transparent',
                                    borderColor: 'gray',
                                    color: 'black',
                                    width: '40%'
                                }}
                                className="Button back"
                                onClick={() => {
                                    setIsModalOpen(false)
                                    setIsRecurso(false)
                                }}
                            >
                                Cancelar
                            </Button>
                            <Button
                                className="btn-cancelar-guia"
                                themeButton="danger"
                                style={{
                                    width: '30%'
                                }}
                                iconLeft="/faturamento/assets/icons/ic-close-mono.svg"
                                onClick={() => (isRecurso ? removeItemGlosa(indexItem) : removeItem())}
                            >
                                Confirmar
                            </Button>
                        </S.ContainerButtons>
                    </div>
                </S.ModalContainer>
            </Modal>
        </Layout>
    )
}

export default CalendarioFaturamentoTemplates
