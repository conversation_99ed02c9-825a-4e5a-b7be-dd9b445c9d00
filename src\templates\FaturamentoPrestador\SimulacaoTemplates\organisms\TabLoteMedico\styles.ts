/* eslint-disable prettier/prettier */
import styled from 'styled-components'
export const Container = styled.div`
    padding: 24px;
    background: #ffffff;
    display: flex;
    flex-direction: column;
    gap: 32px;

    .contentHeader {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
    }

    .contentButton {
        min-width: 176px;
        padding-left: 12px;
    }
`
export const HeaderSearch = styled.div`
    /* padding: 24px; */
    background: #ffffff;
    display: flex;
    flex-direction: row;
    gap: 32px;
    justify-content: space-between;
`
export const Search = styled.div`
    max-width: 300px;
    background: #ffffff;
    /* padding: 24px; */
    display: flex;
    flex-direction: column;
    gap: 32px;
`
export const AddButton = styled.div`
    /* padding: 24px; */
    background: #ffffff;
    display: flex;
    flex-direction: column;
    gap: 32px;
`

