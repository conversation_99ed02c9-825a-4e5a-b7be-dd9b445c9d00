import moment from 'moment'

export const unMaskDate = (date: string) => {
    if (date) {
        return `${date.substring(6, 10)}-${date.substring(3, 5)}-${date.substring(0, 2)}`
    }
}

export class DateUtils {
    static maskDate = (value: string) => {
        if (!value) return ''

        if (value === '') return ''

        if (value === null) return ''

        let date = value.replace(/[^\d]/g, '')

        date = date.substring(0, 8)

        if (date.length > 2) {
            date = date.replace(/^(\d{2})(\d*)/, '$1/$2')
        }

        if (date.length > 5) {
            date = date.replace(/(.{5})(\d*)/, '$1/$2')
        }

        return date
    }

    static maskTime = (value: string): string => {
        if (!value) return ''

        if (value === '') return ''

        if (value === null) return ''

        let time = value.replace(/[^\d]/g, '')

        time = time.substring(0, 4)

        if (time.length > 2) {
            time = time.replace(/^(\d{2})(\d*)/, '$1:$2')
        }

        return time
    }

    static formatDatePTBR = (date: string) => {
        if (!date) return null

        if (date === '') return null

        return new Date(date).toLocaleDateString('pt-BR', {
            timeZone: 'UTC',

            day: '2-digit',

            month: '2-digit',

            year: 'numeric'
        })
    }

    static formatDateTimePTBR = (date: string): string => {
        if (!date) return null

        if (date === '') return null

        return new Date(date).toLocaleDateString('pt-BR', {
            timeZone: 'UTC',
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            minute: '2-digit',
            hour: '2-digit'
        })
    }

    static isValidDate = (date: string): boolean => {
        if (!date) return false

        if (date === '') return false

        return moment(date, 'DD/MM/YYYY').isValid()
    }

    static diffYearsFromNow = (date: string): number => {
        if (!this.isValidDate(date)) return 0

        return moment().diff(moment(date, 'DD/MM/YYYY'), 'years', false)
    }

    static isAfterNow = (date: string): boolean => {
        if (!this.isValidDate(date)) return false

        return moment(date, 'DD/MM/YYYY').isAfter(moment())
    }

    static unMaskDate = (date: string) => {
        if (date) {
            return `${date.substring(6, 10)}-${date.substring(3, 5)}-${date.substring(0, 2)}`
        }
    }

    static formatDateDataPicker = (input: string | undefined, format: 'dd' | 'dd/mm/yyyy' | 'mm/dd/yyyy' | 'yyyy-mm-dd' | 'yyyy-mm-dd'): string => {
        if (input === undefined) {
            return ''
        } else {
            const datePart = input.match(/\d+/g)
            let year
            let month
            let day
            if (datePart !== null) {
                year = datePart[2]

                month = datePart[0]
                day = datePart[1]

                if (month?.length === 1) {
                    month = '0' + month
                }

                if (day?.length === 1) {
                    day = '0' + day
                }
            }
            // console.log(day, month, year)
            if (format === 'mm/dd/yyyy') {
                return month + '/' + day + '/' + year
            } else if (format === 'yyyy-mm-dd') {
                return year + '-' + month + '-' + day
            } else if (format === 'dd') {
                return day
            } else {
                return day + '/' + month + '/' + year
            }
        }
    }

    static getMonthName = (competencia: string): string => {
        const mes = competencia?.substring(5, 7)

        switch (mes) {
            case '01':
                return 'JAN'
            case '02':
                return 'FEV'
            case '03':
                return 'MAR'
            case '04':
                return 'ABR'
            case '05':
                return 'MAI'
            case '06':
                return 'JUN'
            case '07':
                return 'JUL'
            case '08':
                return 'AGO'
            case '09':
                return 'SET'
            case '10':
                return 'OUT'
            case '11':
                return 'NOV'
            case '12':
                return 'DEZ'
        }
    }

    static getMonthFullName = (competencia: string) => {
        const mes = competencia?.substring(5, 7)

        switch (mes) {
            case '01':
                return 'Janeiro'
            case '02':
                return 'Fevereiro'
            case '03':
                return 'Março'
            case '04':
                return 'Abril'
            case '05':
                return 'Maio'
            case '06':
                return 'Junho'
            case '07':
                return 'Julho'
            case '08':
                return 'Agosto'
            case '09':
                return 'Setembro'
            case '10':
                return 'Outubro'
            case '11':
                return 'Novembro'
            case '12':
                return 'Dezembro'
        }
    }

    //RECEBE YYYY-MM-DD
    static parseDataToMMYYYY(date: string, type: 'DD/MM/YYYY' | 'MM/YYYY', divider?: string) {
        if (!date) return
        const dataSetada = date?.split('-')
        if (type === 'MM/YYYY') {
            return `${dataSetada?.[1]}/${dataSetada?.[0]}`
        }
        if (type === 'DD/MM/YYYY') {
            return `${dataSetada?.[2]}${divider ? divider : '/'}${dataSetada?.[1]}${divider ? divider : '/'}${dataSetada?.[0]}`
        }
    }

    // PARSE DATE TIMESTAMP

    static parseDateDDMMYYYY(data: string, dataEHora?: boolean, divider?: string) {
        if (data) {
            const dataSemHoras = data.split('T')[0].split('-')

            if (dataEHora) {
                const horasSemData = data.split('T')[1].split('.')[0]
                return `${dataSemHoras[2]}${divider ? divider : '/'}${dataSemHoras[1]}${divider ? divider : '/'}${dataSemHoras[0]} ${horasSemData}`
            } else {
                return `${dataSemHoras[2]}${divider ? divider : '/'}${dataSemHoras[1]}${divider ? divider : '/'}${dataSemHoras[0]}`
            }
        }
    }

    static isValidTime(time: string) {
        let hora = ''
        let minuto = ''
        let horaValidada = ''
        let minutoValidado = ''

        if (time?.length < 2) {
            return time
        }
        if (time?.length === 2) {
            hora = time
            horaValidada = Number(hora) <= 23 ? hora : '23'
            return horaValidada
        }
        if (time?.length === 3) {
            return time
        }
        if (time?.length <= 5 && time?.length >= 4) {
            hora = time.split(':')[0]
            minuto = time.split(':')[1] ? time.split(':')[1] : ''
            horaValidada = Number(hora) <= 23 ? hora : '23'
            minutoValidado = Number(minuto) <= 59 ? minuto : '59'
            return `${horaValidada}:${minutoValidado}`
        }
    }

    // SEPARA DATA E HORA

    static extrairDataOuHora(dataString: string, opcao: 'data' | 'hora'): string {
        if (dataString) {
            const data = new Date(dataString)

            if (isNaN(data.getTime())) {
                return 'Data inválida'
            }

            if (opcao === 'data') {
                const dataFormatada = data.toLocaleDateString()
                return dataFormatada
            } else if (opcao === 'hora') {
                const horaFormatada = data.toLocaleTimeString()
                return horaFormatada
            } else {
                return "Opção inválida. Use 'data' ou 'hora'."
            }
        }
    }

    // RECEBE O VALOR DE DATA NO FORMATO QUE JA VEM DO INPUT DATE UTILIZADO NO SISTEMA:
    //  Fri Oct 20 2023 00:00:00 GMT-0300 (Horário Padrão de Brasília)

    // E DEVOLVE NO FORMATO QUE O BACK ESPERA RECEBER:
    //  2023-10-20T03:00:00.000Z

    static converterData(dataOriginal) {
        const data = new Date(dataOriginal + ' UTC') // Força interpretação como UTC

        if (!isNaN(data.getTime())) {
            const ano = data.getUTCFullYear()
            const mes = String(data.getUTCMonth() + 1).padStart(2, '0')
            const dia = String(data.getUTCDate()).padStart(2, '0')
            const hora = String(data.getUTCHours()).padStart(2, '0')
            const minuto = String(data.getUTCMinutes()).padStart(2, '0')
            const segundo = String(data.getUTCSeconds()).padStart(2, '0')
            const milissegundo = String(data.getUTCMilliseconds()).padStart(3, '0')

            return `${ano}-${mes}-${dia}T${hora}:${minuto}:${segundo}.${milissegundo}Z`
        } else {
            // Data inválida
            return dataOriginal
        }
    }

    static converterToTimestamp(dataOriginal: string, horaTratada?: string) {
        const data = new Date(dataOriginal)

        if (!isNaN(data.getTime())) {
            const ano = data.getUTCFullYear()
            const mes = String(data.getUTCMonth() + 1).padStart(2, '0')
            const dia = String(data.getUTCDate()).padStart(2, '0')
            const hora = String(data.getUTCHours() - 3).padStart(2, '0')
            const minuto = String(data.getUTCMinutes()).padStart(2, '0')
            const segundo = String(data.getUTCSeconds()).padStart(2, '0')
            const milissegundo = String(data.getUTCMilliseconds()).padStart(3, '0')

            if (horaTratada) {
                return `${ano}-${mes}-${dia}T${horaTratada}:00.000Z`
            }
            return `${ano}-${mes}-${dia}T${hora}:${minuto}:${segundo}.${milissegundo}Z`
        } else {
            // Data inválida
            return null
        }
    }

    static getAge(dateString) {
        const today = new Date()
        const birthDate = new Date(dateString)
        let age = today.getFullYear() - birthDate.getFullYear()
        const m = today.getMonth() - birthDate.getMonth()
        if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
            age--
        }
        return age
    }

    static formatDuracao(dataInicial: Date, dataFinal = new Date()) {
        const diff = dataFinal.getTime() - dataInicial.getTime()
        const days = Math.floor((diff / (1000 * 60 * 60 * 24)) % 365)
        const hours = Math.floor((diff / (1000 * 60 * 60)) % 24)
        const minutes = Math.floor((diff / (1000 * 60)) % 60)
        const parts = []
        days > 0 && parts.push(`${days} ${days === 1 ? 'dia' : 'dias'}`)
        hours > 0 && parts.push(`${hours} ${hours === 1 ? 'hora' : 'horas'}`)
        minutes > 0 && parts.push(`${minutes} min`)
        return parts.join(' ')
    }
}
