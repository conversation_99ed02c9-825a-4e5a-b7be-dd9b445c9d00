import { useEffect, useState } from 'react'
import * as S from './styles'

import AddIcon from '@mui/icons-material/Add'
import SearchIcon from '@mui/icons-material/Search'
import Autocomplete from '@mui/lab/Autocomplete'
import Chip from '@mui/material/Chip'
import IconButton from '@mui/material/IconButton'
import TextField from '@mui/material/TextField'
import SuccessBanner from 'components/atoms/Alert'
import Button from 'components/atoms/Button'
import Link from 'next/link'
import { useRouter } from 'next/router'

import { Alert, Box, Card, CardContent, CircularProgress, Grid, Radio, Typography } from '@mui/material'
import Modal from 'components/atoms/Modal'
import { useToast } from 'src/hooks/toast'
import { capitalize, getMessageErrorFromApiResponse } from 'utils/stringUtils'
import Pagination from 'components/molecules/Pagination'
import moment from 'moment'
import 'moment/locale/pt-br'
import { ReactSVG } from 'react-svg'
import { CobrancaServices } from 'src/services/faturamentoPrestadorApi/cobrancaServices'
import { LoteManualService } from 'src/services/faturamentoPrestadorApi/lote-manual'
import { TipoLoteGuiaEnum } from 'src/services/faturamentoPrestadorApi/lote-manual-guia/enuns'
import { StatusCobrancaEnum, TipoLoteManualEnum } from 'src/services/faturamentoPrestadorApi/lote-manual/enuns'
import { Erro, IGuiaLoteDTO, ILoteManualDTO, IQuantidadeStatusCobranca } from 'src/services/faturamentoPrestadorApi/lote-manual/types'
import { TipoEnvioDoc, TipoEnvioNotaFiscal } from 'types/common/enums'
import { IPagination } from 'types/pagination'
import { DateUtils } from 'utils/dateUtils'
import { currencyMaskBRL } from 'utils/helpers/currencyMaskBRL'
import { PaginationHelper } from 'utils/helpers/paginationHelper'
import maskCpf from 'utils/masks/formatCPF'
import LocDocCopmpForm from '../../CobrancaTemplates/organisms/LotDocCompForm'
import LotNfForm, { NfFormType } from '../../CobrancaTemplates/organisms/LotNfForm'
import dynamic from 'next/dynamic'
const AnimatedLoadingLottie = dynamic(() => import('components/atoms/AnimatedLoadingLottie'), { ssr: false })

moment.locale('pt-br')

interface NovoLoteTemplateProps {
    type: 'Médico' | 'Odonto'
    loteId?: string
}

enum EnumError {
    INFO = 'info',
    WARN = 'warning',
    ERROR = 'error'
}

enum EnumTipoLote {
    CONSULTA = 'Consulta',
    HONORARIO = 'Honorario',
    SPSADT = 'SPSADT',
    TRATAMENTO_ODONTOLOGICO = 'Tratamento odontológico',
    RECURSO_GLOSA = 'Recurso de glosa',
    RESUMO_INTERNACAO = 'Resumo de internação',
    INTERNACAO = 'Internação'
}

const CustomCard = ({ title, description, value, selected, onClick }) => {
    const handleSetOption = () => {
        onClick({
            title,
            value,
            description
        })
    }

    return (
        <Card
            onClick={handleSetOption}
            sx={{
                cursor: 'pointer',
                boxShadow: selected === value ? '0px 0px 0px 2px #2B45D4 !important' : 'none',
                border: '2px solid',
                borderRadius: '8px',
                backgroundColor: selected === value ? '#2B45D404' : '#ffffff'
            }}
        >
            <CardContent style={{ paddingBottom: '16px' }}>
                <Box display="flex" justifyContent="flex-start" alignItems="center">
                    <Radio
                        value={value}
                        checked={selected === value}
                        onChange={handleSetOption}
                        color="primary"
                        sx={{
                            '& .MuiSvgIcon-root': {
                                fontSize: 22,
                                color: selected === value ? '#2B45D4' : '#00000056'
                            }
                        }}
                    />
                    <Box marginLeft={2} width="fit-content">
                        <Typography variant="h6" component="div" fontWeight="bold" fontSize={14} color={selected === value ? '#243AB2' : '#00000088'}>
                            {title}
                        </Typography>
                        <Typography
                            fontSize={12}
                            variant="body2"
                            sx={{ overflow: 'hidden', whiteSpace: 'nowrap' }}
                            color={selected === value ? '#243AB2' : '#00000056'}
                        >
                            {description}
                        </Typography>
                    </Box>
                </Box>
            </CardContent>
        </Card>
    )
}

const ChipInput: any = ({ onSearch, chips, setChips, loading, viewAdd }) => {
    const [inputValue, setInputValue] = useState('')

    const handleKeyDown = (event: any) => {
        if (event?.key === 'Enter') {
            onSearch(chips)
        }

        if (event?.key === ' ') {
            event?.preventDefault()
            const newChip = inputValue.trim()
            if (newChip && !chips?.includes(newChip)) {
                setChips([...chips, newChip])
                setInputValue('')
            }
        }
    }

    const handleSearch = () => {
        onSearch(chips)
    }

    return (
        <Box margin="24px 0">
            <Autocomplete
                multiple
                value={chips}
                inputValue={inputValue}
                freeSolo
                disableClearable
                options={[]}
                onInputChange={(_, value) => setInputValue(value)}
                renderTags={(value, getTagProps) =>
                    value?.map((option, index) => (
                        <Chip
                            key={index}
                            label={option}
                            {...getTagProps({ index })}
                            sx={{
                                '& .MuiChip-labelMedium': {
                                    fontSize: '14px'
                                }
                            }}
                        />
                    ))
                }
                renderInput={(params) => (
                    <TextField
                        {...params}
                        onKeyDown={handleKeyDown}
                        label=""
                        sx={{
                            '& .MuiOutlinedInput-root': {
                                fontSize: '14px',
                                padding: '8px 16px',
                                paddingRight: '20px',
                                borderRadius: '100px',

                                '& fieldset': {
                                    borderColor: '#00000016'
                                },
                                '&:hover fieldset': {
                                    borderColor: '#2B45D4'
                                },
                                '&.Mui-focused fieldset': {
                                    borderColor: '#2B45D4'
                                }
                            }
                        }}
                        placeholder="Digite o número da guia"
                        InputProps={{
                            ...params?.InputProps,
                            endAdornment: (
                                <Box position="absolute" right={0} padding={2}>
                                    {params?.InputProps?.endAdornment}
                                    <IconButton size="medium" onClick={handleSearch} sx={{ backgroundColor: '#ffcc02' }}>
                                        {loading ? <CircularProgress size={16} sx={{ color: '#2B45D4' }} /> : viewAdd ? <AddIcon /> : <SearchIcon />}
                                    </IconButton>
                                </Box>
                            )
                        }}
                    />
                )}
                onChange={(_, value) => setChips(value)}
            />

            <S.TextAlert>
                <ReactSVG src="/faturamento/assets/icons/info.svg" />
                <span>Para adicionar uma guia, informe o número da guia na operadora e pressione a tecla espaço.</span>
            </S.TextAlert>
        </Box>
    )
}

const batchTypes = [
    {
        title: 'SP/SADT',
        description: 'Lote com as guias de SADT, Quimioterapia, Radioterapia e OPME.',
        value: 'SPSADT'
    },
    {
        title: 'Consulta',
        description: 'Lote com guias de consulta.',
        value: 'CONSULTA'
    },
    {
        title: 'Internação',
        description: 'Lote com guias de solicitações de internação e prorrogação de internação.',
        value: 'RESUMO_INTERNACAO'
    },
    {
        title: 'Honorários',
        description: 'Lote com guias que contenham códigos de honorários.',
        value: 'HONORARIO'
    }
]

type TipoLote = {
    title?: string
    value?: string
    description?: string
    tipoLote?: string
}

enum EnumIcon {
    INFO = 'info-v2-icon.svg',
    WARN = 'alert-v2-icon.svg',
    ERROR = 'error-v2-icon.svg'
}

const nfFormInitialValues = {
    number: '',
    services: { label: 'Produção Médico Hospitalar', value: 'PRODUCAO_MEDICO_HOSPITALAR' },
    date: '',
    nfValue: '',
    nfFiles: []
}

const NovoLoteTemplate = ({ type, loteId }: NovoLoteTemplateProps) => {
    const { query, push, replace, reload } = useRouter()
    const { addToast } = useToast()

    const [chips, setChips] = useState([])
    const [showSuccessBanner, setShowSuccessBanner] = useState<boolean>()
    const [loading, setLoading] = useState(false)
    const [refresh, setRefresh] = useState(false)
    const [loadingLote, setLoadingLote] = useState(false)
    const [loadingSearchLote, setLoadingSearchLote] = useState(false)
    const [modalOpen, setModalOpen] = useState(false)

    const [warningAlert, setWarningAlert] = useState(false)
    const [identificadorLoteCobranca, setIdentificadorLoteCobranca] = useState<number>()
    const [errorAlert, setErrorAlert] = useState(false)
    const [tipoLote, setTipoLote] = useState<TipoLote>({
        title: null,
        value: null,
        description: null,
        tipoLote: null
    })
    const [guiaState, setGuiaState] = useState<IGuiaLoteDTO[]>([])
    const [disabled, setDisabled] = useState<boolean>()
    const [pagination, setPagination] = useState<IPagination>()
    const [numberPage, setNumberPage] = useState<number>(0)
    const [erros, setErros] = useState<Erro[]>([])
    const [statusCobranca, setStatusCobranca] = useState<StatusCobrancaEnum>(StatusCobrancaEnum.AGUARDANDO_ANALISE)
    const [lote, setLote] = useState<ILoteManualDTO>()
    const [showFilters, setShowFilters] = useState<boolean>(false)
    const [quantidadeStatusCobranca, setQuantidadeStatusCobranca] = useState<IQuantidadeStatusCobranca[]>([])
    const competencia = query?.competencia
    const data_competencia = moment(query?.competencia).format('MMMM [de] YYYY')

    const [nfFormValues, setNfFormValues] = useState<NfFormType>(nfFormInitialValues)
    const [docFormValues, setDocFormValues] = useState<File[]>([])

    const [isPreSendNF, setIsPreSendNF] = useState(true)
    const [loadingConfigs, setLoadingConfigs] = useState(true)
    // const router = useRouter()

    useEffect(() => {
        // Verifica se tem lote id na rota
        if (getLotUuid()) {
            // EDIÇÃO
            getGuias(getLotUuid())
        } else {
            // CRIAÇÃO
            getGuias(lote?.uuid)
        }
    }, [statusCobranca, numberPage, refresh])

    const handleSetTipoLote = (tipo: any) => {
        getUltimoLote()
        setErros([])
        setQuantidadeStatusCobranca([])
        setTipoLote(tipo)
    }

    const getUltimoLote = () => {
        setShowFilters(false)

        if (getLotUuid()) {
            LoteManualService.getLotes(getLotUuid())
                .then(({ data }) => {
                    if (data) {
                        setTipoLote({
                            title: data?.tipoLote,
                            value: data?.tipoLote,
                            description: '',
                            tipoLote: data?.tipoLote
                        })

                        getGuias(getLotUuid())
                    } else {
                        setGuiaState([])
                    }
                })
                .catch((err) => {
                    addToast({
                        type: 'error',
                        duration: 10000,
                        title: '',
                        description: getMessageErrorFromApiResponse(err)
                    })
                })
        }
    }

    const getQuantidadeStatusCobranca = (uuid: string) => {
        LoteManualService.getQuantidadeStatusCobranca(uuid)
            .then(({ data }) => {
                setQuantidadeStatusCobranca(data)
            })
            .catch((err) => {
                addToast({
                    type: 'error',
                    duration: 10000,
                    title: '',
                    description: getMessageErrorFromApiResponse(err)
                })
            })
    }
    const handleDeleteGuia = async (uuidGuia: string) => {
        const loteUuid = getLotUuid()

        LoteManualService.deleteGuia(loteUuid, uuidGuia)
            .then(() => {
                getGuias(loteUuid)

                if (guiaState?.length === 1) {
                    replace('/prestador/medico/cobranca/')
                }
            })
            .catch((err) => {
                addToast({
                    type: 'error',
                    duration: 10000,
                    title: 'Erro ao excluir guia',
                    description: getMessageErrorFromApiResponse(err)
                })
            })
    }

    const createLoteGuias = (listNumeroGuia: any) => {
        setLoadingSearchLote(true)

        if (getLotUuid()) {
            LoteManualService.patch(getLotUuid(), listNumeroGuia)
                .then(({ data }) => {
                    // TODO: ANALISAR SE O CORRETO É LOTEID OU LOTE?.UUID
                    getGuias(getLotUuid())
                    if (data?.erros?.length) {
                        setErros(data?.erros)
                        setWarningAlert(true)
                    }
                })
                .catch((err) => {
                    addToast({
                        type: 'error',
                        title: 'Erro ao executar esta ação.',
                        duration: 10000,
                        description: getMessageErrorFromApiResponse(err) || 'Erro desconhecido'
                    })
                    console.log('teste')
                })
                .finally(() => {
                    setChips([])
                    setRefresh(true)
                    setLoadingSearchLote(false)
                })
        } else {
            LoteManualService.post({
                competencia: String(competencia),
                tipoLote: TipoLoteManualEnum[tipoLote?.value],
                listNumeroGuia: listNumeroGuia
            })
                .then(({ data }) => {
                    setLote(data)
                    getGuias(data?.uuid)

                    if (data?.erros?.length) {
                        setErros(data?.erros)
                        setWarningAlert(true)
                    }

                    replace(`/prestador/medico/cobranca/novo-lote?loteId=${data?.uuid}&competencia=${competencia}`)
                })
                .catch((err) => {
                    // tipagem ILoteManualDTO
                    const response = err?.response?.data

                    if (response?.erros?.length > 0) {
                        setErros(response?.erros)
                        setWarningAlert(true)
                    } else {
                        addToast({
                            type: 'error',
                            title: err?.response?.data?.message || 'Erro ao executar esta ação.',
                            duration: 10000,
                            description: err?.response?.data && err?.response?.data?.[0]?.mensagem
                        })
                    }
                })
                .finally(() => {
                    setLoading(false)
                    setLoadingSearchLote(false)
                    setChips([])
                })
        }
    }

    const getGuias = (uuid?: string) => {
        setLoading(true)

        if (uuid) {
            LoteManualService.getGuias(uuid, {
                statusCobranca,
                size: 5,
                page: numberPage
            })
                .then(({ data }) => {
                    setShowFilters(true)
                    setPagination(PaginationHelper.parserPagination<IGuiaLoteDTO>(data, (page) => setNumberPage(page)))
                    setGuiaState(data?.content)

                    getQuantidadeStatusCobranca(uuid)
                })
                .catch((err) => {
                    addToast({
                        type: 'error',
                        title: 'Erro ao executar esta ação.',
                        duration: 10000,
                        description: err?.response?.data && err?.response?.data?.[0]?.mensagem
                    })
                })
                .finally(() => {
                    setLoading(false)
                })
        }
    }

    const criarNovoLote = async () => {
        setLoadingLote(true)
        LoteManualService.enviarCobranca({
            uuid: getLotUuid(),
            docComp: docFormValues,
            ...(isPreSendNF && {
                notaFiscal: {
                    numeroNota: nfFormValues?.number,
                    servicoPrestado: nfFormValues?.services?.value,
                    dataEmissao: DateUtils.formatDateDataPicker(nfFormValues?.date, 'yyyy-mm-dd'),
                    valor: nfFormValues?.nfValue,
                    nfFiles: nfFormValues?.nfFiles,
                    conciliarNotaFiscal: nfFormValues?.reconcileNf
                }
            })
        })
            .then(({ data }) => {
                setErrorAlert(false)
                setShowSuccessBanner(true)
                setGuiaState([])
                setQuantidadeStatusCobranca([])
                setIdentificadorLoteCobranca(data?.identificadorLoteCobranca)
                setNfFormValues(nfFormInitialValues)
                setDocFormValues([])
            })
            .catch((err) => {
                addToast({
                    type: 'error',
                    title: 'Erro',
                    duration: 10000,
                    description: getMessageErrorFromApiResponse(err) || 'Erro desconhecido'
                })
            })
            .finally(() => {
                setModalOpen(false)
                setLoadingLote(false)
                setChips([])
            })
    }

    const verificarVencimento = (dataString: string) => {
        const dataVencimento = new Date(dataString)
        const dataAtual = new Date()

        // FIXME: Esta lógica precisa de ajustes no front/back, pois não corresponde ao figma.

        if (dataVencimento < dataAtual) {
            return (
                <p style={{ color: 'red' }} className="flex-content" title={'Esta guia venceu nas ultimas 24h'}>
                    {dataVencimento?.toLocaleDateString('pt-BR')} <ReactSVG src="/faturamento/assets/icons/warning.svg" wrapper="div" />
                </p>
            )
        } else {
            return <p>{dataVencimento?.toLocaleDateString('pt-BR')}</p>
        }
        //ALERTA LARANJA
        // else {
        //     const diferencaEmMs = dataVencimento.getTime() - dataAtual.getTime()
        //     const diferencaEmDias = Math.floor(diferencaEmMs / (1000 * 60 * 60 * 24))
        //     return (
        //         <p style={{ color: 'orange' }} title={'Esta guia está próxima do vencimento'}>
        //             {dataVencimento.toLocaleDateString('pt-BR')}
        //         </p>
        //     )
        // }
    }

    const verificaGuiasAnalisadas = () => {
        const value = guiaState?.some((item) => {
            if (item?.statusCobranca === StatusCobrancaEnum.ANALISADA) {
                return true
            } else {
                return false
            }
        })

        return value
    }

    const getConfigTipoEnvioNf = (competence: string) => {
        setLoadingConfigs(true)
        CobrancaServices.getTipoEnvioNfPrestador(competence)
            .then(({ data: { tipoEnvioNotaFiscal } }) => {
                setIsPreSendNF(tipoEnvioNotaFiscal === TipoEnvioNotaFiscal.PRE_PROCESSAMENTO)
                setLoadingConfigs(false)
            })
            .catch(() => {
                addToast({ title: 'Ocorreu erro ao buscar as configurações do prestador', type: 'error', duration: 3000 })
            })
    }

    const getLotUuid = () => lote?.uuid || loteId

    useEffect(() => {
        handleSetTipoLote(tipoLote)
    }, [loteId, lote?.uuid])

    useEffect(() => {
        const todaAsGuiasEstaoAnalisadas =
            quantidadeStatusCobranca?.find((e) => e?.statusCobranca === StatusCobrancaEnum.ANALISADA)?.quantidadeGuias > 0 &&
            quantidadeStatusCobranca?.find((e) => e?.statusCobranca === StatusCobrancaEnum.AGUARDANDO_ANALISE)?.quantidadeGuias === 0
        const notaFiscalValida = (isPreSendNF && nfFormValues?.nfIsValid) || !isPreSendNF

        setDisabled(!todaAsGuiasEstaoAnalisadas || !notaFiscalValida || !docFormValues?.length)
    }, [quantidadeStatusCobranca, nfFormValues, isPreSendNF, docFormValues])

    useEffect(() => {
        getConfigTipoEnvioNf(String(competencia))
    }, [])

    return (
        <S.Container>
            <S.Title>Criar lote manual</S.Title>

            <S.SubTitle>Este lote será criado e enviado na competência vigente ({capitalize(data_competencia)})</S.SubTitle>

            {showSuccessBanner && (
                <Box marginTop="16px">
                    <SuccessBanner
                        color="success"
                        title="Lote gerado"
                        description={
                            <Typography fontWeight="400" fontSize={14} color="#00000088">
                                Para acompanhar faturamento do Lote {identificadorLoteCobranca || '-'} acesse a {''}
                                <Link href="/prestador/medico/cobranca">Cobrança médica</Link>
                            </Typography>
                        }
                    />
                </Box>
            )}

            {loadingConfigs ? (
                <AnimatedLoadingLottie style={{ width: '240px', margin: 'auto' }} />
            ) : (
                <>
                    <S.SelectBatchType>
                        <S.SectionTitle>Selecione o tipo de lote</S.SectionTitle>

                        <Grid container margin="24px 0">
                            {batchTypes?.map((data, index) => (
                                <Grid
                                    item
                                    sm={6}
                                    xs={12}
                                    key={index}
                                    sx={{
                                        paddingLeft: index % 2 !== 0 ? '4px' : '0px',
                                        paddingRight: index % 2 === 0 ? '4px' : '0px',
                                        paddingBottom: index < 2 ? '8px' : '0px'
                                    }}
                                >
                                    <CustomCard
                                        title={data?.title}
                                        value={data?.value}
                                        selected={tipoLote?.value}
                                        onClick={handleSetTipoLote}
                                        description={data?.description}
                                    />
                                </Grid>
                            ))}
                        </Grid>
                        {tipoLote?.value?.length > 0 && (
                            <>
                                <S.SectionTitle>Quais guias você gostaria de adicionar neste lote</S.SectionTitle>

                                <ChipInput
                                    onSearch={createLoteGuias}
                                    chips={chips}
                                    setChips={setChips}
                                    loading={loadingSearchLote}
                                    viewAdd={guiaState?.length}
                                />

                                {warningAlert && erros?.length > 0 && (
                                    <Box marginBottom="24px">
                                        {erros.map((erro, index) => (
                                            <S.WrapperBtn key={index}>
                                                <Alert
                                                    severity={EnumError[erro?.tipo]}
                                                    icon={<ReactSVG src={`/faturamento/assets/icons/${EnumIcon[erro?.tipo]}`} />}
                                                    onClose={(e) => setWarningAlert(false)}
                                                    sx={{
                                                        borderRadius: '8px',
                                                        '& .MuiAlert-message': {
                                                            alignSelf: 'center',
                                                            fontSize: '14px'
                                                        }
                                                    }}
                                                >
                                                    <Typography key={`guia-com-erro-${index}`} fontSize="14px">
                                                        Guia {erro?.numeroGuia} - {erro?.mensagem}
                                                    </Typography>
                                                </Alert>
                                            </S.WrapperBtn>
                                        ))}
                                    </Box>
                                )}

                                {showFilters &&
                                !loading &&
                                (quantidadeStatusCobranca?.find((e) => e?.statusCobranca === StatusCobrancaEnum.ANALISADA)?.quantidadeGuias >= 1 ||
                                    quantidadeStatusCobranca?.find((e) => e?.statusCobranca === StatusCobrancaEnum.AGUARDANDO_ANALISE)
                                        ?.quantidadeGuias >= 1) ? (
                                    <S.FilterCard>
                                        <S.CardItem
                                            isFocus={statusCobranca === StatusCobrancaEnum.AGUARDANDO_ANALISE}
                                            onClick={() => {
                                                setStatusCobranca(StatusCobrancaEnum.AGUARDANDO_ANALISE)
                                            }}
                                        >
                                            <S.CardName>Aguardando análise</S.CardName>
                                            <S.CardQuantity isFocus={statusCobranca === StatusCobrancaEnum.AGUARDANDO_ANALISE} status="waiting">
                                                {
                                                    quantidadeStatusCobranca?.find((e) => e?.statusCobranca === StatusCobrancaEnum.AGUARDANDO_ANALISE)
                                                        ?.quantidadeGuias
                                                }
                                            </S.CardQuantity>
                                        </S.CardItem>
                                        <S.CardItem
                                            isFocus={statusCobranca === StatusCobrancaEnum.ANALISADA}
                                            onClick={() => {
                                                setStatusCobranca(StatusCobrancaEnum.ANALISADA)
                                            }}
                                        >
                                            <S.CardName>Analisadas</S.CardName>
                                            <S.CardQuantity isFocus={statusCobranca === StatusCobrancaEnum.ANALISADA} status="charged">
                                                {
                                                    quantidadeStatusCobranca?.find((e) => e?.statusCobranca === StatusCobrancaEnum.ANALISADA)
                                                        ?.quantidadeGuias
                                                }
                                            </S.CardQuantity>
                                        </S.CardItem>
                                    </S.FilterCard>
                                ) : null}

                                {loading ? (
                                    <AnimatedLoadingLottie style={{ width: '240px', margin: 'auto' }} />
                                ) : (
                                    <>
                                        {guiaState?.length ? (
                                            <>
                                                <S.HeaderList>
                                                    <S.Grid collumns="1fr 2fr 1fr 1fr 1fr .2fr">
                                                        <h3>Guia</h3>
                                                        <h3>Beneficiário</h3>
                                                        <h3>Vencimento da guia</h3>
                                                        <h3>Valor cobrado </h3>
                                                        <h3>Situação</h3>
                                                        <h3>Ação </h3>
                                                    </S.Grid>
                                                </S.HeaderList>

                                                <S.WrapperList>
                                                    {guiaState?.map((item, index) => (
                                                        <S.CardGuia key={index}>
                                                            <S.Grid collumns="1fr 2fr 1fr 1fr 0.89fr .2fr">
                                                                <S.Item>
                                                                    <span>{item?.numeroGuiaPrestador}</span>
                                                                    <p>{TipoLoteGuiaEnum[tipoLote?.value]}</p>
                                                                </S.Item>

                                                                <S.Item>
                                                                    <span>{capitalize(item?.dadosBeneficiario?.nome)}</span>
                                                                    <p>
                                                                        {item?.dadosBeneficiario?.cpfCnpj
                                                                            ? maskCpf(item?.dadosBeneficiario?.cpfCnpj)
                                                                            : '-'}
                                                                    </p>
                                                                </S.Item>
                                                                <S.Item>
                                                                    <span>{verificarVencimento(item?.dataValidadeSenha)}</span>
                                                                </S.Item>

                                                                <S.Item>
                                                                    <span>{currencyMaskBRL(item?.valorApresentado)}</span>
                                                                </S.Item>

                                                                <S.Item>
                                                                    {item?.statusCobranca === StatusCobrancaEnum.AGUARDANDO_ANALISE && (
                                                                        <S.Badge type="waiting">
                                                                            <p>Aguardando análise</p>
                                                                        </S.Badge>
                                                                    )}
                                                                    {item?.statusCobranca === StatusCobrancaEnum.ANALISADA && (
                                                                        <S.Badge type="ready">
                                                                            <p>Analisada</p>
                                                                        </S.Badge>
                                                                    )}
                                                                </S.Item>

                                                                <S.WrapperButtons>
                                                                    <S.IconButton
                                                                        title="Editar"
                                                                        onClick={() => {
                                                                            push(
                                                                                `/prestador/${type
                                                                                    ?.toLowerCase()
                                                                                    .replace('é', 'e')}/cobranca/novo-lote/resumo-guia/${item?.uuid}`
                                                                            )?.then(() => reload())
                                                                        }}
                                                                    >
                                                                        <ReactSVG src={'/faturamento/assets/icons/eyes.svg'} />
                                                                    </S.IconButton>
                                                                    <S.IconButton title="Remover" onClick={() => handleDeleteGuia(item?.uuid)}>
                                                                        <ReactSVG src={'/faturamento/assets/icons/trash-red.svg'} />
                                                                    </S.IconButton>
                                                                </S.WrapperButtons>
                                                            </S.Grid>
                                                        </S.CardGuia>
                                                    ))}
                                                    <S.WrapperPagination>
                                                        <Pagination
                                                            totalPage={pagination?.totalPaginas}
                                                            totalRegister={pagination?.totalRegistros}
                                                            actualPage={pagination?.paginaAtual}
                                                            setNumberPage={pagination?.setNumberPage}
                                                        />
                                                        <p>
                                                            {pagination?.paginaAtual * pagination?.linhasPorPagina + 1} -{' '}
                                                            {guiaState?.length + pagination?.paginaAtual * pagination?.linhasPorPagina}
                                                            {' de '}
                                                            {pagination?.totalRegistros}
                                                        </p>
                                                    </S.WrapperPagination>
                                                </S.WrapperList>
                                            </>
                                        ) : (
                                            <>
                                                {showFilters ? (
                                                    <S.NotFound>
                                                        <ReactSVG
                                                            src={'/faturamento/assets/imgs/mai-illustration-walking.svg'}
                                                            role={'figure'}
                                                            wrapper="div"
                                                        />
                                                        <S.NotFountText>Nenhum registro encontrado</S.NotFountText>
                                                    </S.NotFound>
                                                ) : null}
                                            </>
                                        )}
                                    </>
                                )}
                            </>
                        )}
                    </S.SelectBatchType>

                    {guiaState?.length > 0 && !verificaGuiasAnalisadas() && (
                        <S.Info>
                            <Alert
                                severity={'info'}
                                // onClose={(e) => setWarningAlert(false)}
                                icon={<ReactSVG src={`/faturamento/assets/icons/${EnumIcon.INFO}`} />}
                                sx={{
                                    borderRadius: '8px',
                                    '& .MuiAlert-message': {
                                        alignSelf: 'center',
                                        fontSize: '14px'
                                    }
                                }}
                            >
                                <Typography key={`guia-com-info}`} fontSize="14px">
                                    O lote ainda não pode ser enviado para cobrança, pois existem guias com a situação <b>“Aguardando análise”.</b>
                                </Typography>
                            </Alert>

                            {/* =========================== */}

                            {/* <S.Alert>
                        <ReactSVG src="/faturamento/assets/icons/info.svg" wrapper="span" />
                        <Typography key={`guia-com-info}`} fontSize="14px">
                            O lote ainda não pode ser enviado para cobrança, pois existem guias com a situação <b>“Aguardando análise”.</b>
                        </Typography>
                    </S.Alert> */}
                        </S.Info>
                    )}

                    {isPreSendNF && (
                        <S.Box style={{ marginTop: '2.4rem' }}>
                            <S.BoxHeader>
                                <S.BoxTitle>Envio da nota fiscal</S.BoxTitle>
                                <S.BoxSubtitle>Realize o envio dos arquivos .xml e .pdf da nota fiscal</S.BoxSubtitle>
                            </S.BoxHeader>
                            <LotNfForm data={nfFormValues} setData={setNfFormValues} />
                        </S.Box>
                    )}

                    <S.Box style={{ marginTop: '2.4rem' }}>
                        <S.BoxHeader>
                            <S.BoxTitle>Documentos complementares</S.BoxTitle>
                            <S.BoxSubtitle>
                                Por favor, anexe abaixo todos os documentos complementares necessários para apoiar a análise da conta pela operadora.{' '}
                            </S.BoxSubtitle>
                        </S.BoxHeader>
                        <LocDocCopmpForm data={docFormValues} setData={setDocFormValues} />
                    </S.Box>

                    {tipoLote?.value?.length > 0 && (
                        <Grid container margin="24px 0" padding="16px" bgcolor="#ffffff" borderRadius="8px">
                            <Grid item xs={12} display="flex" justifyContent="space-between" alignItems="center">
                                <S.SelectedGuidesContainer>
                                    <S.SelectedGuidesLabel>Guias Analisadas</S.SelectedGuidesLabel>
                                    <S.SelectedGuidesCounter>
                                        {quantidadeStatusCobranca?.find((e) => e?.statusCobranca === StatusCobrancaEnum.ANALISADA)?.quantidadeGuias ||
                                            0}
                                        /100
                                    </S.SelectedGuidesCounter>
                                </S.SelectedGuidesContainer>

                                <Button
                                    typeButton="flat"
                                    themeButton="warning"
                                    positionButton="center"
                                    className="button-primary"
                                    style={{ width: 'fit-content' }}
                                    onClick={() => {
                                        setModalOpen((state) => !state)
                                    }}
                                    disabled={disabled}
                                >
                                    Enviar lote
                                </Button>
                            </Grid>
                        </Grid>
                    )}
                </>
            )}

            <Modal isOpen={modalOpen} onClose={() => setModalOpen(false)}>
                <Box display="flex" justifyContent="center" alignItems="center" height="auto">
                    <Box bgcolor="#ffffff" width="520px">
                        <Typography variant="h6" component="div" fontWeight="600" fontSize="27px" color="#000000E0">
                            Enviar lote de {EnumTipoLote[tipoLote?.tipoLote]} ?
                        </Typography>
                        <Typography variant="h6" component="div" marginTop={'10px'} fontWeight="400" fontSize="16px" color="#707070">
                            Ao gerar o lote, as{' '}
                            {quantidadeStatusCobranca?.find((e) => e?.statusCobranca === StatusCobrancaEnum.ANALISADA)?.quantidadeGuias} guias
                            selecionadas serão enviadas para processamento.
                        </Typography>
                        <Box display="flex" flexDirection="row" justifyContent="flex-end" marginTop="16px">
                            <Button
                                disabled={false}
                                typeButton="text"
                                themeButton="gray"
                                positionButton="center"
                                className="button-primary"
                                onClick={() => setModalOpen((state) => !state)}
                                style={{ width: 'fit-content', marginRight: 20 }}
                            >
                                Cancelar
                            </Button>
                            <Button
                                disabled={loadingLote}
                                typeButton="flat"
                                themeButton="warning"
                                positionButton="center"
                                className="button-primary"
                                onClick={criarNovoLote}
                                style={{ width: 'fit-content' }}
                            >
                                {loadingLote ? <CircularProgress size={16} sx={{ color: '#2B45D4' }} /> : <span>Confirmar</span>}
                            </Button>
                        </Box>
                    </Box>
                </Box>
            </Modal>
        </S.Container>
    )
}

export default NovoLoteTemplate
