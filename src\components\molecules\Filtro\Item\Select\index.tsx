/* eslint-disable react-hooks/exhaustive-deps */
import AsyncPaginateSelect from 'components/atoms/AsyncPaginateSelect'
import { IElements, IRetorno } from 'components/atoms/AsyncPaginateSelect/functions'
import { useEffect, useState } from 'react'
import { useFiltroContext } from '../../Root'
interface ISelect<T, K> {
    loadOptions: (
        value: string,
        prev: T,
        {
            page
        }: {
            page: number
        },
        dep?: string | number
    ) => Promise<IRetorno<T, T[keyof T], T[keyof T]>>
    field: string
    placeholder: string
    fieldDeph?: string
    required?: boolean
}

export default function Select<T>({ loadOptions, field, placeholder, fieldDeph, required }: ISelect<T, string>) {
    const { setFieldValue, filter } = useFiltroContext()
    const [value, setValue] = useState<IElements<T, T[keyof T], T[keyof T]>>()

    const handleOnChange = (selected: IElements<T, T[keyof T], T[keyof T]>) => {
        setValue(selected)
        setFieldValue(selected?.value, field)
    }

    const handleLoadOptions = (
        value: string,
        prev: T,
        {
            page
        }: {
            page: number
        }
    ) => {
        return loadOptions(value, prev, { page }, filter?.[fieldDeph])
    }

    useEffect(() => {
        if (filter === undefined && value?.value !== undefined && String(value?.label) !== '') {
            setValue({ label: '', value: undefined } as unknown as IElements<T, T[keyof T], T[keyof T]>)
        }
    }, [filter])

    useEffect(() => {
        if (fieldDeph) setValue({ label: '', value: undefined } as unknown as IElements<T, T[keyof T], T[keyof T]>)
    }, [filter?.[fieldDeph]])

    return (
        <AsyncPaginateSelect
            isClearable
            value={value}
            label={placeholder}
            onChange={handleOnChange}
            loadOptions={handleLoadOptions}
            key={filter?.[fieldDeph]}
            disabled={fieldDeph ? !filter?.[fieldDeph] : false}
            defaultOptions={fieldDeph ? !!filter?.[fieldDeph] : true}
            required={required}
        />
    )
}
