/* eslint-disable no-unsafe-optional-chaining */
import React, { useState, useEffect } from 'react'
import * as S from './styles'
import { Fab, FormControl, InputLabel, MenuItem, Select, TextField } from '@mui/material'
import { IResumoComposicao } from 'src/services/composicaoPagamentoApi/resumo-composicao'
import { useRouter } from 'next/router'
import { useToast } from 'src/hooks/toast'
import { ComposicaoPagamento } from 'src/services/composicaoPagamentoApi'
import { AcaoHistorico, EtapaHistorico, Historico as Historic, IOpcoesFiltrosHistorico } from 'src/services/composicaoPagamentoApi/historico'
import { ReactSVG } from 'react-svg'
import { currencyMaskBRL } from 'utils/helpers/currencyMaskBRL'
import Button from 'components/atoms/Button'
import NoContent from 'components/molecules/NoContent'
import SearchIcon from '@mui/icons-material/Search'
import moment from 'moment'
import InputDate from 'components/molecules/InputDate'
import Selectable from 'components/molecules/Select'
import dynamic from 'next/dynamic'

const AnimatedLoadingLottie = dynamic(() => import('components/atoms/AnimatedLoadingLottie'), { ssr: false })

type FilterOptionsSelected = {
    usuario?: string
    acao?: string
    etapa?: string
    data_inicial?: string
    data_final?: string
}

const Historico = ({ resumo }: { resumo: IResumoComposicao }) => {
    const router = useRouter()
    const { addToast } = useToast()

    const id = router.query.id as string
    const [competencias, setCompetencias] = useState([])
    const [historico, setHistorico] = useState<Historic[]>([])
    const [competenciaSelected, setCompetenciaSelected] = useState<string>('')
    const [isLoading, setIsLoading] = useState<boolean>(true)
    const [options, setOptions] = useState<IOpcoesFiltrosHistorico>({
        acao: [],
        etapa: [],
        usuarios: []
    })
    const [optionsSelected, setOptionsSelected] = useState<FilterOptionsSelected>({
        acao: null,
        etapa: null,
        usuario: null,
        data_inicial: null,
        data_final: null
    })
    const [dataInicial, setDataInicial] = useState<string>('2023-08-01')

    // const parseCompetencia = (options: string[]) => {
    //     return options?.map((option) => {
    //         return {
    //             label: option,
    //             value: convertMonthYearToFirstDate(option)
    //         }
    //     })
    // }

    useEffect(() => {
        console.log(optionsSelected)
    }, [optionsSelected])

    const formatarPreposicao = (acao) => {
        switch (acao) {
            case 'aprovou':
                return 'a'
            case 'criou':
                return 'a'
            case 'desfez a aprovação':
                return 'da'
            case 'desfez a liberação':
                return 'de'
            case 'enviou':
                return 'para'
            case 'liberou':
                return ''
            default:
                return ''
        }
    }

    useEffect(() => {
        setIsLoading(true)
        if (!id) return
        ComposicaoPagamento.getHistorico({ id, filters: optionsSelected })
            .then(({ data }) => {
                const options = data['opcoes-filtros']
                setOptions((data) => ({
                    ...options,
                    data_inicio: options?.dataInicial ? options?.dataInicial : '',
                    data_final: options?.dataFinal ? options?.dataFinal : ''
                }))
                setHistorico(data?.historico)
            })
            .finally(() => setIsLoading(false))
    }, [id])

    return (
        <>
            {/* Filtro Histórico */}
            <S.WrapperInfo>
                <FormControl className='select-user"'>
                    <InputLabel shrink id="select-user" className="select-user">
                        Usuário
                    </InputLabel>
                    <Select
                        displayEmpty
                        defaultValue={optionsSelected?.usuario}
                        value={optionsSelected?.usuario}
                        labelId="select-user"
                        className="select-user"
                        onChange={(e) => {
                            setOptionsSelected((prev) => ({ ...prev, usuario: e.target.value }))
                        }}
                    >
                        {options?.usuarios &&
                            [
                                { label: 'Todos', value: null },
                                ...Object?.entries(options?.usuarios).map(([_, value]) => ({
                                    label: value,
                                    value: value
                                }))
                            ].map((item, index) => (
                                <MenuItem key={index} value={item?.value}>
                                    {item?.label}
                                </MenuItem>
                            ))}
                    </Select>
                </FormControl>
                <FormControl className='select-stage"'>
                    <InputLabel shrink id="select-stage" className="select-stage">
                        Ação
                    </InputLabel>
                    <Select
                        displayEmpty
                        defaultValue={optionsSelected?.acao}
                        value={optionsSelected?.acao}
                        labelId="select-stage"
                        className="select-stage"
                        onChange={(e) => {
                            setOptionsSelected((prev) => ({ ...prev, acao: e.target.value }))
                        }}
                    >
                        {options?.acao &&
                            [
                                { label: 'Todos', value: null },
                                ...Object?.entries(options?.acao).map(([_, value]) => ({
                                    label: value,
                                    value: value
                                }))
                            ].map((item, index) => (
                                <MenuItem key={index} value={item?.value}>
                                    {item?.label}
                                </MenuItem>
                            ))}
                    </Select>
                </FormControl>
                <FormControl className='simple-select"'>
                    <InputLabel shrink id="select-action" className="select-action">
                        Etapa
                    </InputLabel>
                    <Select
                        displayEmpty
                        defaultValue={optionsSelected?.etapa}
                        value={optionsSelected?.etapa}
                        labelId="select-action"
                        className="select-action"
                        onChange={(e) => {
                            setOptionsSelected((prev) => ({ ...prev, etapa: e.target.value }))
                        }}
                    >
                        {options?.etapa &&
                            [
                                { label: 'Todos', value: null },
                                ...Object?.entries(options?.etapa).map(([_, value]) => ({
                                    label: value,
                                    value: value
                                }))
                            ].map((item, index) => (
                                <MenuItem key={index} value={item?.value}>
                                    {item?.label}
                                </MenuItem>
                            ))}
                    </Select>
                </FormControl>
                <TextField
                    InputLabelProps={{ shrink: true }}
                    label="Data Inicial"
                    type="date"
                    defaultValue={options?.data_inicio}
                    value={options?.data_inicio}
                    onChange={({ target }) => {
                        setOptions((prev) => ({ ...prev, data_inicio: target.value }))
                        setOptionsSelected((prev) => ({ ...prev, data_inicio: target.value }))
                    }}
                />
                <TextField
                    InputLabelProps={{ shrink: true }}
                    label="Data Final"
                    type="date"
                    defaultValue={options?.data_final}
                    value={options?.data_final}
                    onChange={({ target }) => {
                        setOptions((prev) => ({ ...prev, data_final: target.value }))
                        setOptionsSelected((prev) => ({ ...prev, data_final: target.value }))
                    }}
                />
                <S.FabCustomButton
                    onClick={() =>
                        ComposicaoPagamento.getHistorico({
                            id,
                            filters: {
                                ...optionsSelected,
                                acao: AcaoHistorico[optionsSelected?.acao],
                                etapa: EtapaHistorico[optionsSelected?.etapa]
                            }
                        }).then(({ data }) => setHistorico(data?.historico))
                    }
                >
                    <SearchIcon fontSize="medium" />
                </S.FabCustomButton>
            </S.WrapperInfo>

            {/* Content */}
            {isLoading ? (
                <S.ContainerLottie>
                    <AnimatedLoadingLottie />
                </S.ContainerLottie>
            ) : options?.usuarios?.length === 0 || options?.usuarios === undefined ? (
                <div style={{ marginTop: '48px' }}>
                    <NoContent title="Não há registros de histórico" />
                </div>
            ) : (
                <S.Wrapper>
                    {historico?.length === 0 ? (
                        <div style={{ marginTop: '48px' }}>
                            <NoContent title="Não há registros de histórico com os parâmetros informados." />
                        </div>
                    ) : (
                        historico.map((historico, index) => (
                            <S.RowHistorico key={index}>
                                <div style={{ height: '100%', display: 'flex', alignItems: 'center' }}>
                                    <ReactSVG src="/faturamento/assets/icons/user.mono.svg" alt="icon" aria-label="user" />
                                </div>
                                <S.RowHistoricoInfoWrapper key={index}>
                                    <p>
                                        <span className="strong">{historico?.usuario}</span> <span className="blue-color">{historico?.acao}</span>{' '}
                                        {formatarPreposicao(historico?.acao)} <span className="blue-color">{historico?.etapa} </span>{' '}
                                        {historico?.data_criacao}
                                    </p>
                                    {historico?.acao === 'aprovou' || historico?.acao === 'desfez a aprovação' ? (
                                        <S.StatusWrapper>
                                            <S.Badge status="INICIAL">{historico?.acao === 'aprovou' ? 'Aguardando aprovação' : 'Aprovado'}</S.Badge>
                                            <ReactSVG src="/faturamento/assets/icons/arrow-right.svg" alt="icon" aria-label="arrow-right" />
                                            <S.Badge status="FINAL">{historico?.acao === 'aprovou' ? 'Aprovado' : 'Aguardando aprovação'}</S.Badge>
                                        </S.StatusWrapper>
                                    ) : (
                                        <S.StatusWrapper>
                                            <S.Badge status="INICIAL">{currencyMaskBRL(historico?.valor_inicial)}</S.Badge>
                                            <ReactSVG src="/faturamento/assets/icons/arrow-right.svg" alt="icon" aria-label="arrow-right" />
                                            <S.Badge status="FINAL">{currencyMaskBRL(historico?.valor_final)}</S.Badge>
                                        </S.StatusWrapper>
                                    )}
                                </S.RowHistoricoInfoWrapper>
                            </S.RowHistorico>
                        ))
                    )}
                </S.Wrapper>
            )}
        </>
    )
}

export default Historico
