import * as S from './styles'
import moment from 'moment'
import React, { useState, useEffect, useCallback } from 'react'
import { IPagination } from 'types/common/pagination'
import { IPrestadorAnaliseQuery } from 'types/cobrancaPrestador/prestadorAnalise'
import { useAuth } from 'src/hooks/auth'
import { CobrancaServices, IGetCobrancaProps, situacaoLoteEnum } from 'src/services/faturamentoPrestadorApi/cobrancaServices'
import { NumberUtils } from 'utils/numberUtils'
import { capitalize } from 'utils/stringUtils'
import { PaginationHelper } from 'utils/helpers/paginationHelper'
import DropLote from 'components/molecules/DropLote'
import NoContent from 'components/molecules/NoContent'
import Pagination from 'components/molecules/Pagination'
import ButtonDropLots from 'components/molecules/Buttons/ButtonDownloadLots'
import { ReactSVG } from 'react-svg'
import DropLoteCard from '../DropLoteCards'
import { useToast } from 'src/hooks/toast'
import { useRouter } from 'next/router'
import { tiposLotesEnum } from '../TabLoteMedico/enuns'
import Alert from 'components/atoms/Alert'
import AlertFlat from 'components/atoms/AlertFlat'
import Checkbox from 'components/atoms/CheckBox'
import { ILoteCobrancaFechamentoQuery } from 'types/cobrancaPrestador/visaoFechamento'
import { GeracaoRecursoGlosa } from 'src/services/faturamentoPrestadorApi/geracao-recurso-glosa'
import { ModuloEnum, TipoEnvioEnum } from 'src/services/faturamentoPrestadorApi/geracao-recurso-glosa/enuns'
import { IPage } from 'types/pagination'
import { IDetalhesLote, IGetInfoRecurso, IGetLotes } from 'src/services/faturamentoPrestadorApi/geracao-recurso-glosa/types'
import LoteInfoContent from './LoteInfoContent'
import { Box, CircularProgress } from '@mui/material'
import { retiraSequencial } from 'utils/functions'

type ListaLotesEmAnaliseProps = {
    competenciaSelecionada: IPrestadorAnaliseQuery
    searchLote: string
    forceUpdate: boolean
    tipoLote: tiposLotesEnum
    setLotes: any
    lotes: any
    refreshList: boolean
    filtro?: string
    checkboxFilter: ICheckeboxFilterAnalisado
    setCheckboxFilter: React.Dispatch<React.SetStateAction<ICheckeboxFilterAnalisado>>
    loadingLotes: boolean
    setLoadingLotes: React.Dispatch<React.SetStateAction<boolean>>
}

export interface ICheckeboxFilterAnalisado {
    lotesSemGlosa: boolean
    lotesGlosaNaoRecursado: boolean
    lotesGlosaRecursado: boolean
    lotesRecurso: boolean
}

const ListaLotesAnalisados = ({
    competenciaSelecionada,
    searchLote,
    forceUpdate,
    tipoLote,
    lotes,
    setLotes,
    filtro,
    checkboxFilter,
    setCheckboxFilter,
    loadingLotes,
    setLoadingLotes,
    refreshList
}: ListaLotesEmAnaliseProps) => {
    const { prestadorVinculado } = useAuth()
    const { addToast } = useToast()
    const [pagination, setPagination] = useState<IPagination>()
    const [numberPage, setNumberPage] = useState(0)

    const labels: string[] = ['Lote', 'Valor apresentado', 'Valor glosado', 'Valor apurado', 'Envio']

    function createDropLot(item: IGetLotes) {
        return [
            {
                component: (
                    <div>
                        <div style={{ display: 'flex', gap: '8px' }}>
                            <p>{item?.identificadorLote}</p>
                            {/* {item?.loteNovo && (
                                <S.NewTag>
                                    <span>Novo</span>
                                </S.NewTag>
                            )} */}
                        </div>
                    </div>
                )
            },
            {
                component: (
                    <div>
                        <p>{NumberUtils.maskMoney(item?.valorApresentado)}</p>
                    </div>
                )
            },
            {
                component: (
                    <div>
                        <p>{NumberUtils.maskMoney(item?.valorGlosado)}</p>
                    </div>
                )
            },
            {
                component: (
                    <div>
                        <p>{NumberUtils.maskMoney(item?.valorApurado)}</p>
                    </div>
                )
            },
            {
                component: (
                    <div>
                        <p>{item.tipoEnvio === 'ELETRONICO' ? 'Eletrônico' : capitalize(item.tipoEnvio)}</p>
                    </div>
                )
            }
        ]
    }

    const carregarLotes = () => {
        setLoadingLotes(true)
        if (competenciaSelecionada?.competencia && prestadorVinculado?.uuid) {
            GeracaoRecursoGlosa.getlotesAnalisados({
                identificadorLote: retiraSequencial(searchLote),
                competencia: competenciaSelecionada?.competencia,
                prestadorId: prestadorVinculado?.uuid,
                modulo: ModuloEnum.MEDICO,
                lotesGlosasNaoRecursadas: checkboxFilter?.lotesGlosaNaoRecursado,
                lotesGlosasRecursadas: checkboxFilter?.lotesGlosaRecursado,
                lotesRecursoGlosa: checkboxFilter?.lotesRecurso,
                lotesSemGlosa: checkboxFilter?.lotesSemGlosa,
                page: numberPage,
                size: 10
            })
                .then(({ data }) => {
                    setLotes(data)

                    const objectPagination = PaginationHelper.parserPagination<IGetLotes>(data, setNumberPage)
                    setPagination(objectPagination)
                })
                .catch((err) => {
                    addToast({
                        title: err?.message ? err?.message : 'Ocorreu um erro ao buscar as informações',
                        type: 'error',
                        duration: 3000
                    })
                })
                .finally(() => setLoadingLotes(false))
        }
    }

    useEffect(() => {
        if (forceUpdate === false) {
            carregarLotes()
        }
    }, [forceUpdate, competenciaSelecionada])

    useEffect(() => {
        carregarLotes()
    }, [numberPage, checkboxFilter, refreshList])

    return (
        <>
            <S.CheckboxWrapper>
                <Checkbox
                    checked={checkboxFilter.lotesSemGlosa}
                    onCheck={() => setCheckboxFilter({ ...checkboxFilter, lotesSemGlosa: !checkboxFilter.lotesSemGlosa })}
                    label="Lotes sem glosa"
                />

                <Checkbox
                    checked={checkboxFilter.lotesGlosaNaoRecursado}
                    onCheck={() => setCheckboxFilter({ ...checkboxFilter, lotesGlosaNaoRecursado: !checkboxFilter.lotesGlosaNaoRecursado })}
                    label="Lotes com glosa não recursados"
                />

                <Checkbox
                    checked={checkboxFilter.lotesGlosaRecursado}
                    onCheck={() => setCheckboxFilter({ ...checkboxFilter, lotesGlosaRecursado: !checkboxFilter.lotesGlosaRecursado })}
                    label="Lotes com glosas recursados"
                />

                <Checkbox
                    checked={checkboxFilter.lotesRecurso}
                    onCheck={() => setCheckboxFilter({ ...checkboxFilter, lotesRecurso: !checkboxFilter.lotesRecurso })}
                    label="Lotes de recurso de glosa"
                />
            </S.CheckboxWrapper>
            {loadingLotes ? (
                <Box sx={{ display: 'flex', minHeight: 218, alignItems: 'center', justifyContent: 'center' }}>
                    <CircularProgress sx={{ color: '#2B45D4' }} />
                </Box>
            ) : (
                <S.Wrapper>
                    {!lotes || lotes?.content.length === 0 ? (
                        <NoContent title="No momento não existe nenhum lote analisado" path="/parametros/regras-de-excludencia/nova-regra" />
                    ) : (
                        <>
                            <div className="contentInput">
                                <div className="contentNumber">
                                    <span className="pageNumber">
                                        {pagination?.paginaAtual * pagination?.linhasPorPagina + 1} -{' '}
                                        {lotes?.content?.length + pagination?.paginaAtual * pagination?.linhasPorPagina}
                                        {' de '}
                                        {pagination?.totalRegistros}
                                    </span>
                                </div>
                            </div>

                            <S.HeaderLabel>
                                {labels.map((item, index) => (
                                    <div key={index}>
                                        <p>{item}</p>
                                    </div>
                                ))}
                            </S.HeaderLabel>
                            {lotes?.content?.map((lote: IGetLotes, index) => {
                                return (
                                    <DropLote items={createDropLot(lote)} key={index}>
                                        <LoteInfoContent loteListInfo={lote} getList={carregarLotes} />
                                    </DropLote>
                                )
                            })}
                            <div style={{ marginTop: '40px' }}>
                                <Pagination
                                    totalPage={pagination?.totalPaginas}
                                    totalRegister={pagination?.totalRegistros}
                                    actualPage={pagination?.paginaAtual}
                                    setNumberPage={pagination?.setNumberPage}
                                />
                            </div>
                        </>
                    )}
                </S.Wrapper>
            )}
        </>
    )
}

export default ListaLotesAnalisados
