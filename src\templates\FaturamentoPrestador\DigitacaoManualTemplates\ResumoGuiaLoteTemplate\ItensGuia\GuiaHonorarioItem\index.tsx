import { useEffect, useState } from 'react'

import AccordionCustom from 'components/molecules/Accordion'

import { FormControl, MenuItem, Select, TextField } from '@mui/material'
import Button from 'components/atoms/Button'
import InputDate from 'components/molecules/InputDate'
import ModalDetalhesItemGuia from 'components/organisms/ModalDetalheItemGuia'
import moment from 'moment'
import { ReactSVG } from 'react-svg'
import { useToast } from 'src/hooks/toast'
import { GuiaLoteManualService } from 'src/services/faturamentoPrestadorApi/lote-manual-guia'
import { createPayloadToCobrar } from 'src/services/faturamentoPrestadorApi/lote-manual-guia/payload'
import { IEquipeExecucao, IGuiaLoteManualDTO, IListProcedimentosDTO } from 'src/services/faturamentoPrestadorApi/lote-manual-guia/types'
import { DateUtils } from 'utils/dateUtils'
import { handleFieldsChange } from 'utils/form-utils'
import { formatMonetary } from 'utils/masks/formatMonetary'
import { getMessageErrorFromApiResponse } from 'utils/stringUtils'
import ProfessionalField from './ProfessionalField'
import { Dispatch, SetStateAction } from 'react'
import { NumberUtils } from 'utils/numberUtils'
import { grauParticipacaoOptions } from './ProfessionalField/grauParticipacaoMock'
import { StatusCobrancaEnum } from 'src/services/faturamentoPrestadorApi/lote-manual/enuns'
import ModalInfoCobranca from '../../../organisms/ModalInfoCobranca'
import { TipoLoteManualGuiaEnum } from 'src/services/faturamentoPrestadorApi/lote-manual-guia/enuns'
import * as S from './styles'
import { IPrestadorDTO } from 'types/prestador'

type GuiaHonorarioProps = {
    data: IListProcedimentosDTO
    guideData: IGuiaLoteManualDTO
    setRefresh?: (content: boolean) => void
    refresh?: boolean
    setStatusCobranca: Dispatch<SetStateAction<StatusCobrancaEnum>>
    dadosPrestador: IPrestadorDTO
    periodoRegistrado: boolean
    setPeriodoRegistrado: Dispatch<SetStateAction<boolean>>
}

type t = {
    name: string
}

export interface IFieldValues {
    grauParticipacao?: string
    viaAcesso?: string
    horaIni?: string
    horaFim?: string
    quantidadeCobrada?: number
    quantidadeAutorizada?: number
    dataExecucao?: string
    equipeExecucao?: IEquipeExecucao[]
    quantidadeExecutada?: number
    uuid?: string
    valorUnitario?: number
    valorTotal?: number
}

function GuiaHonorarioItem({
    data,
    guideData,
    setRefresh,
    refresh,
    setStatusCobranca,
    dadosPrestador,
    setPeriodoRegistrado,
    periodoRegistrado
}: GuiaHonorarioProps) {
    const [isOpen, setIsOpen] = useState<boolean>()
    const [isEditable, setIsEditable] = useState<boolean>(false)
    const [btnDisable, setBtnDisable] = useState<boolean>(false)
    const [noValue, setNoValue] = useState<boolean>(false)
    const [btnDisableEmptyProfessional, setBtnDisableEmptyProfessional] = useState<boolean>(false)
    const [showAlerMessage, setShowAlerMessage] = useState<boolean>(false)
    const [isOpenVerifyModal, setIsOpenVerifyModal] = useState<boolean>(false)
    const [value, setValue] = useState<number>(0)
    const [saveTotalValue, setSaveTotalValue] = useState<number[]>([])
    const [totalValue, setTotalValue] = useState<number>(0)
    const { addToast } = useToast()

    const [fieldValues, setFieldValues] = useState<IFieldValues>({
        viaAcesso: 'UNICA',
        horaIni: '',
        horaFim: '',
        dataExecucao: null,
        equipeExecucao: [] as IEquipeExecucao[],
        quantidadeExecutada: null,
        uuid: null,
        valorUnitario: null
    })

    const handleClickToPatch = () => {
        const payload = createPayloadToCobrar({
            data: data,
            fieldValues: { ...fieldValues, valorUnitario: value },
            uuidItem: data?.uuid
        })

        setBtnDisable(true)

        GuiaLoteManualService?.patchProcedimento(payload, guideData?.uuid)
            .then(({ data }) => {
                addToast({
                    type: 'success',
                    title: 'Analisado com sucesso!',
                    duration: 4000
                })

                setIsEditable(false)
                setRefresh(!refresh)

                setStatusCobranca(data?.statusCobrancaGuia)
            })
            .catch((err) => {
                addToast({
                    type: 'error',
                    title: 'Erro ao tentar analisar o item',
                    duration: 5000,
                    description: getMessageErrorFromApiResponse(err)
                })
            })
            .finally(() => {
                setBtnDisableEmptyProfessional(false)
                setBtnDisable(false)
            })
    }

    //

    const handleClickAnalisar = () => {
        if (
            guideData?.periodoCobrancaRegistrado ||
            periodoRegistrado ||
            (guideData?.tipo !== TipoLoteManualGuiaEnum.HONORARIOS && guideData?.tipo !== TipoLoteManualGuiaEnum.RESUMO_INTERNACAO)
        ) {
            handleClickToPatch()
        } else {
            setIsOpenVerifyModal(true)
        }
    }

    // VERIFICA SE OS CAMPOS DE HORA ESTÃO PREENCHIDOS

    function isEmpty(fieldValues: IFieldValues) {
        return fieldValues.horaIni && fieldValues.horaFim && fieldValues.dataExecucao && fieldValues?.viaAcesso
    }

    // VERIFICA SE O CAMPO DE PROFISSIONAL E GRAU ESTÁ PREENCHIDO

    function isEmptyProfessional(data: IEquipeExecucao[]) {
        return data.some((item) => !item?.uuid)
    }

    useEffect(() => {
        if (data) {
            setFieldValues({
                ...fieldValues,
                equipeExecucao:
                    data?.equipeExecucao?.length === 0
                        ? [{} as IEquipeExecucao]
                        : data?.equipeExecucao?.map((item) => {
                              return {
                                  ...item,
                                  savedProfessional: true
                              }
                          }),
                horaIni: DateUtils?.extrairDataOuHora(data?.dataInicioExecucao, 'hora'),
                horaFim: DateUtils?.extrairDataOuHora(data?.dataFinalExecucao, 'hora'),
                dataExecucao: data?.dataInicioExecucao || data?.dataFinalExecucao,
                viaAcesso: data?.viaAcesso || 'UNICA',
                quantidadeExecutada: data?.quantidadeExecutada || null
            })
            setValue(data?.valorUnitario)
        }
    }, [data])

    useEffect(() => {
        if (value) {
            setFieldValues({
                ...fieldValues,
                valorUnitario: value
            })
        }
    }, [value])

    useEffect(() => {
        if (data?.valorTotal) {
            setTotalValue(data?.valorTotal)
        }
    }, [data?.valorTotal])

    useEffect(() => {
        const emptyProfessional = isEmptyProfessional(fieldValues?.equipeExecucao || [])
        const emptyValues = isEmpty(fieldValues)

        setBtnDisableEmptyProfessional(emptyProfessional)
        setShowAlerMessage(emptyProfessional)
        setBtnDisable(!emptyValues)
    }, [fieldValues])

    //

    useEffect(() => {
        console.log(data?.equipeExecucao)
    }, [data])

    return (
        <S.Content>
            <AccordionCustom
                hasBorder
                style={{ marginTop: '10px', background: 'rgba(43, 69, 212, 0.04)', border: '1px solid transparent' }}
                customHeader={
                    <S.HeaderCard>
                        <S.Grid collumns="3fr 1fr 1fr 1fr .1fr">
                            <p>
                                {data?.codigoProcedimento} - {data?.descricaoProcedimento}
                            </p>
                            <p>{data?.quantidadeSolicitada || '-'}</p>
                            <p>{NumberUtils?.maskMoney(data?.valorTotal)}</p>

                            <S.BadgeWrapper>
                                {data?.statusCobranca === StatusCobrancaEnum.AGUARDANDO_ANALISE && (
                                    <S.Badge type="waiting">
                                        <p>Aguardando análise</p>
                                    </S.Badge>
                                )}

                                {data?.statusCobranca === StatusCobrancaEnum.ANALISADA && (
                                    <S.Badge type="ready">
                                        <p>Analisada</p>
                                    </S.Badge>
                                )}

                                {data?.statusCobranca === StatusCobrancaEnum.FINALIZADA && (
                                    <S.Badge type="ready">
                                        <p>Finalizada</p>
                                    </S.Badge>
                                )}
                            </S.BadgeWrapper>
                        </S.Grid>
                    </S.HeaderCard>
                }
            >
                <S.Wrapper>
                    <S.WrapperWhite>
                        <h3>Revisão do item</h3>

                        {noValue ? (
                            <S.IconInfo>
                                <ReactSVG src="/faturamento/assets/icons/info.svg" wrapper="span" />{' '}
                                <p>
                                    Não foi possível recuperar o valor do item. Por favor, entre em contato pelo email:{' '}
                                    <b>judicializaçã*****************</b>
                                </p>
                            </S.IconInfo>
                        ) : (
                            <></>
                        )}

                        <S.Header>
                            {!isEditable && data?.statusCobranca === StatusCobrancaEnum.ANALISADA && (
                                <S.ContentGrid>
                                    <S.Item>
                                        <p>Via acesso</p>
                                        <span>{fieldValues?.viaAcesso || '-'}</span>
                                    </S.Item>
                                    <S.Item>
                                        <p>Data de execução</p>
                                        <span>{DateUtils?.parseDateDDMMYYYY(fieldValues?.dataExecucao) || '-'}</span>
                                    </S.Item>
                                    <S.Item>
                                        <p>Hora inicial</p>
                                        <span>{fieldValues?.horaIni || '-'}</span>
                                    </S.Item>
                                    <S.Item>
                                        <p>Hora final</p>
                                        <span>{fieldValues?.horaFim || '-'}</span>
                                    </S.Item>
                                </S.ContentGrid>
                            )}
                            {(isEditable || data?.statusCobranca === StatusCobrancaEnum.AGUARDANDO_ANALISE) && (
                                <S.SelectWrapper>
                                    <FormControl>
                                        <Select
                                            value={fieldValues?.viaAcesso}
                                            defaultValue="UNICA"
                                            onChange={(event) =>
                                                setFieldValues({
                                                    ...fieldValues,
                                                    viaAcesso: event?.target?.value
                                                })
                                            }
                                        >
                                            <MenuItem value="UNICA">1 - Única</MenuItem>
                                            <MenuItem value="MESMA_VIA">2 - Mesma via</MenuItem>
                                            <MenuItem value="DIFERENTE_VIAS">3 - Diferentes via</MenuItem>
                                        </Select>
                                    </FormControl>

                                    <InputDate
                                        minDate={moment().add(-10, 'year').toDate()}
                                        value={
                                            fieldValues?.dataExecucao !== undefined && fieldValues?.dataExecucao !== null
                                                ? new Date(fieldValues?.dataExecucao)
                                                : null
                                        }
                                        label="Data de execução"
                                        required={true}
                                        placeholder="00/00/0000"
                                        onChange={(date) => {
                                            setFieldValues({
                                                ...fieldValues,
                                                dataExecucao: date?.toString()
                                            })
                                        }}
                                        key={'input-date'}
                                    />

                                    <TextField
                                        label="Hora Inicio"
                                        required
                                        value={DateUtils.maskTime(fieldValues?.horaIni)}
                                        onChange={(e) => {
                                            if (e?.target.value.length <= 5) {
                                                setFieldValues({
                                                    ...fieldValues,
                                                    horaIni: DateUtils?.isValidTime(e?.target.value)
                                                })
                                            }
                                        }}
                                    />

                                    <TextField
                                        label="Hora Final"
                                        required
                                        value={DateUtils.maskTime(fieldValues?.horaFim)}
                                        onChange={(e) => {
                                            if (e?.target.value.length <= 5) {
                                                setFieldValues({
                                                    ...fieldValues,
                                                    horaFim: DateUtils?.isValidTime(e?.target.value)
                                                })
                                            }
                                        }}
                                    />
                                </S.SelectWrapper>
                            )}
                        </S.Header>
                    </S.WrapperWhite>

                    <S.WrapperWhite>
                        <h3>Profissional executante</h3>

                        {showAlerMessage && fieldValues?.equipeExecucao?.length > 1 && (
                            <S.IconInfo>
                                <ReactSVG src="/faturamento/assets/icons/info.svg" wrapper="span" />{' '}
                                <p>Informe o profissional executante no grau de participação adicionado.</p>
                            </S.IconInfo>
                        )}

                        {fieldValues?.equipeExecucao?.map((item, index) => (
                            <ProfessionalField
                                dadosPrestador={dadosPrestador}
                                key={index}
                                id={index}
                                data={item}
                                procedureData={data}
                                guideData={guideData}
                                // totalValue={totalValue}
                                fieldValues={fieldValues}
                                isEditable={isEditable}
                                saveTotalValue={saveTotalValue}
                                setValue={setValue}
                                setNoValue={setNoValue}
                                setFieldValues={setFieldValues}
                                setIsEditable={setIsEditable}
                                setTotalValue={setTotalValue}
                                setSaveTotalValue={setSaveTotalValue}
                            />
                        ))}

                        {(isEditable || data?.statusCobranca === StatusCobrancaEnum.AGUARDANDO_ANALISE) && (
                            <S.WrapperButtons>
                                <Button
                                    typeButton="ghost"
                                    onClick={() => {
                                        const profissionaisList = [...fieldValues.equipeExecucao, {} as IEquipeExecucao]
                                        setFieldValues({
                                            ...fieldValues,
                                            equipeExecucao: profissionaisList
                                        })
                                    }}
                                >
                                    Adicionar profissional
                                </Button>
                            </S.WrapperButtons>
                        )}
                    </S.WrapperWhite>

                    <S.WrapperContent>
                        <S.Grid collumns="1fr auto">
                            <p>Total a cobrar</p>
                            <p>{formatMonetary(String(totalValue))}</p>
                        </S.Grid>
                    </S.WrapperContent>

                    <S.Footer>
                        {/* <Button typeButton="text" onClick={() => setIsOpen(true)}>
                            Ver detalhes
                        </Button> */}
                        <div></div>

                        {data?.statusCobranca === StatusCobrancaEnum.AGUARDANDO_ANALISE && (
                            <Button
                                typeButton="ghost"
                                disabled={btnDisable || btnDisableEmptyProfessional || noValue}
                                onClick={() => {
                                    handleClickAnalisar()
                                }}
                            >
                                Analisar
                            </Button>
                        )}

                        {!isEditable && data?.statusCobranca === StatusCobrancaEnum.ANALISADA && (
                            <Button
                                typeButton="ghost"
                                onClick={() => {
                                    setIsEditable(!isEditable)
                                }}
                            >
                                Editar
                            </Button>
                        )}

                        {isEditable && data?.statusCobranca === StatusCobrancaEnum.ANALISADA && (
                            <Button
                                typeButton="ghost"
                                disabled={btnDisable || btnDisableEmptyProfessional}
                                onClick={() => {
                                    handleClickAnalisar()
                                }}
                            >
                                Salvar
                            </Button>
                        )}
                    </S.Footer>
                </S.Wrapper>
            </AccordionCustom>

            {/* Modal Detalhes de cobrança */}
            <ModalDetalhesItemGuia setIsOpen={setIsOpen} isOpen={isOpen} data={data} />
            <ModalInfoCobranca
                setPeriodoRegistrado={setPeriodoRegistrado}
                setIsOpen={setIsOpenVerifyModal}
                isOpen={isOpenVerifyModal}
                guideData={guideData}
                cobrarItem={handleClickToPatch}
            />
        </S.Content>
    )
}

export default GuiaHonorarioItem
