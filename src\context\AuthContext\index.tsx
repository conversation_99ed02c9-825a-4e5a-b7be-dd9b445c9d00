/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */

import React, { createContext, useEffect, useState } from 'react'
import useStorage from 'src/hooks/storage/useStorage'
import { getProfile } from 'src/services/apis/managementUser'
import { localStorageToken, localStorageUserData } from 'src/utils/constants'
import { PrestadorService } from 'src/services/faturamentoOperadoraApi/prestadorServices'
import { AxiosError } from 'axios'
import { useToast } from 'src/hooks/toast'
import { IPrestadorDTO } from 'types/prestador'
import jwt_decode from 'jwt-decode'

export const AuthContext = createContext({} as any)

type DecodedTypes = {
    sub: string
    permissions: string[]
    roles: string[]
    scopes: string[]
    exp: number
    type: string
}

type StateProviderProps = {
    children?: React.ReactNode
}

const AuthProvider = ({ children }: StateProviderProps) => {
    const { addToast } = useToast()

    const [data, setData, removeData] = useStorage(localStorageUserData)
    const [dataUser, setDataUser] = useState()
    const [userRoles, setUserRoles] = useState<string[]>()
    const [isAdmin, setIsAdmin] = useState(false)
    const [isOwner, setIsOwner] = useState(false)
    const [dataToken, setDataToken, removeDataToken] = useStorage(localStorageToken)
    const [prestadorVinculado, setPrestadorVinculado] = useState<IPrestadorDTO>()

    function logout() {
        removeDataToken()
        removeData()
        setPrestadorVinculado(undefined)
    }

    function getPrestadorVinculado(uuidUsuario: string) {
        if (prestadorVinculado === undefined) {
            PrestadorService.getPrestadorVinculado(uuidUsuario)
                .then(({ data }) => {
                    setPrestadorVinculado(data)
                })
                .catch((e: AxiosError) => {
                    if (e?.response?.status === 404) {
                        addToast({
                            title: 'Não há prestador vinculado ao seu acesso. Entre em contato com o suporte e solicite o ajuste em seu cadastro',
                            type: 'info',
                            duration: 8000
                        })

                        return
                    }

                    addToast({
                        title: 'Não foi possível buscar o prestador vinculado ao seu acesso.',
                        type: 'info',
                        duration: 5000
                    })
                })
        }
    }

    useEffect(() => {
        if (data?.name === undefined || data?.email === undefined || data?.imageUrl === undefined) {
            getProfile()
                .then((response) => {
                    setDataUser({
                        ...data,

                        ['name']: response.name,

                        ['email']: response.email,

                        ['imageUrl']: response.imageUrl
                    })
                })

                .catch((error) => {
                    //   console.log(error);
                })
        } else {
            setDataUser(data)
        }

        const decoded = dataToken && (jwt_decode(dataToken) as DecodedTypes)

        setUserRoles(decoded?.roles)

        if (decoded?.roles?.includes('OWNER')) {
            setIsOwner(true)
        }
        if (decoded?.roles?.includes('ADMIN')) {
            setIsAdmin(true)
        } else {
            getPrestadorVinculado(decoded?.sub)
        }
    }, [])

    useEffect(() => {
        setData(dataUser)
    }, [dataUser])

    return (
        <AuthContext.Provider
            value={{
                data,
                setData,
                removeData,
                dataToken,
                setDataToken,
                removeDataToken,
                logout,
                dataUser,
                prestadorVinculado,
                setDataUser,
                userRoles,
                isAdmin,
                isOwner
            }}
        >
            {children}
        </AuthContext.Provider>
    )
}

export default AuthProvider
