import Button from 'components/atoms/Button'
import DividerSectionCard from 'components/atoms/DividerSectionCard'
import Item from 'components/atoms/Item'
import TitleSection from 'components/atoms/TitleSection'
import SearchBar from 'components/molecules/SearchBar'
import ContentDataInfo from 'components/molecules/ShowDataConfirm'
import { useRouter } from 'next/router'
import { useCallback, useEffect, useState } from 'react'
import { ReactSVG } from 'react-svg'
import * as S from './styles'

import CardsFilter from 'components/molecules/GuideGlosaFilter'
import NoContent from 'components/molecules/NoContent'
import Pagination from 'components/molecules/Pagination'
import { useToast } from 'src/hooks/toast'
import { AnaliseContasService } from 'src/services/analiseContasApi/analiseContasServices'
import { PrestadorService } from 'src/services/faturamentoOperadoraApi/prestadorServices'
import { IValidaEncerramentoCompetencia } from 'types/analiseContas/competencia'
import { ILotePrestadorQuery } from 'types/analiseContas/lotePrestador'
import { IGetPropsLote, ILoteQuery, IPageLotes } from 'types/analiseContas/lotes'
import { initialResumoPrestador, IResumoPrestadorQuery } from 'types/analiseContas/resumoPrestador'
import { IPagination } from 'types/common/pagination'
import { IPrestadorQuery } from 'types/faturamentoOperadora/provider'
import { PaginationHelper } from 'utils/helpers/paginationHelper'
import { NumberUtils } from 'utils/numberUtils'
import { getMessageErrorFromApiResponse, capitalize, StringUtils } from 'utils/stringUtils'
import ModalRelatarPendencia from './organisms/ModalRelatarPendencia'
import { SituacaoPrestadorAnalise } from 'types/common/enums'
import { IGlossReason } from 'components/molecules/ModalAddGlossReasons/gloss-reason'
import ModalAddGlossReasons from 'components/molecules/ModalAddGlossReasons'
import TableRowIconButton from 'components/atoms/TableRowIconButton'
import Modal from 'components/atoms/Modal'
import { Button as ButtonMui, Checkbox, FormControl, FormControlLabel, InputLabel, MenuItem, Select, TextField } from '@mui/material'
import { ISortPage } from 'types/pagination'
import { TipoLoteGuiaEnum } from 'src/services/faturamentoPrestadorApi/geracao-recurso-glosa/enuns'
import dynamic from 'next/dynamic'

const AnimatedLoadingLottie = dynamic(() => import('components/atoms/AnimatedLoadingLottie'), { ssr: false })

export interface IResumoPrestadorProps {
    idPrestador: string
    competencia: string
}

export type lotChecked = {
    index: number
    checked: boolean
    idLote: string
}

type newFilterType = {
    numeroGuia: string
    tipoProcesso: 'TODOS' | 'REGULAR' | 'LIMINAR' | 'EXCEPCIONALIDADE'
    tipoLote: 'TODOS' | TipoLoteGuiaEnum
    loteSemGlosa: boolean
    loteComGlosa: boolean
}

const ResumoPrestadorTemplate = ({ idPrestador, competencia }: IResumoPrestadorProps) => {
    const router = useRouter()
    const { addToast } = useToast()
    const [showFilter, setShowFilter] = useState(false)
    const [motive, setMotive] = useState('')
    const [status, setStatus] = useState('Em auditoria')
    const [resumoPrestador, setResumoPrestador] = useState<IResumoPrestadorQuery>(initialResumoPrestador)
    const [isModalOpen, setIsModalOpen] = useState(false)
    // const [prestador, setPrestador] = useState<IPrestadorQuery>()
    const [pageLotes, setPageLotes] = useState<IPageLotes>()
    const [pagination, setPagination] = useState<IPagination>()
    const [numberPage, setNumberPage] = useState<number>(0)
    const [filtro, setFiltro] = useState<string>()
    const [newFilter, setNewFilter] = useState<newFilterType>({
        numeroGuia: null,
        tipoLote: 'TODOS',
        tipoProcesso: 'TODOS',
        loteComGlosa: false,
        loteSemGlosa: false
    })
    const [situacoes, setSituacoes] = useState<ILotePrestadorQuery[]>()
    const [filterFields, setFilterFields] = useState([])
    const [filterSelected, setFilterSelected] = useState('EM_ANALISE_ADMINISTRATIVA')
    const [checkedAll, setCheckedAll] = useState(false)
    const [lotsCheckeds, setLotsCheckeds] = useState<lotChecked[]>([])
    const [lotesChecados, setLotesChecados] = useState<ILoteQuery[]>()
    const [isOpenRelatarPendencia, setIsOpenRelatarPendencia] = useState<boolean>(false)
    const [isOpenGlosarLote, setIsOpenGlosarLote] = useState<boolean>(false)
    const [validToCloseInfo, setValidToCloseInfo] = useState<IValidaEncerramentoCompetencia>()
    const [loadingFinishProcess, setLoadingFinishProcess] = useState(false)
    const [loadingLots, setLoadingLots] = useState(false)
    const [isModalReabrirOpen, setIsModalReabrirOpen] = useState(false)
    const [reabrirLotesInProgress, setReabrirLotesInProgress] = useState(false)
    const [tipoLoteOptions, setTipoLoteOptions] = useState<{ label: string; value: 'TODOS' | TipoLoteGuiaEnum }[]>([])

    const getItemSituacao = (situacao: string) => {
        if (situacao === 'EM_ANALISE_ADMINISTRATIVA') {
            situacao = 'EM_ANALISE'
        }

        return situacoes?.some((i) => i.atributo === situacao)
            ? situacoes?.find((i) => i.atributo === situacao)
            : { porcentagem: 0, quantidadeDeLotes: 0 }
    }

    function getTipoLote() {
        AnaliseContasService.getTipoLote()
            .then(({ data }) => {
                const parseOptions = data?.map((item) => {
                    return {
                        label: item?.prettyName,
                        value: item?.value
                    }
                })

                setTipoLoteOptions(parseOptions)
            })
            .catch(() => {
                addToast({
                    title: 'Erro ao carregar tipos de lotes',
                    type: 'error',
                    duration: 4000
                })
                setPageLotes(null)
            })
    }

    const carregarLotes = useCallback(
        (page?: number, filter?: string, newFilter?: newFilterType) => {
            const getProps: IGetPropsLote & ISortPage = {
                competencia: competencia,
                uuidPrestador: idPrestador,
                situacao: filterSelected,
                numGuia: newFilter?.numeroGuia,
                tipoProcesso: newFilter?.tipoProcesso === 'TODOS' ? null : newFilter?.tipoProcesso,
                tipoLote: newFilter?.tipoLote === 'TODOS' ? null : newFilter?.tipoLote,
                lotesSemGlosa: newFilter?.loteSemGlosa,
                lotesComGlosa: newFilter?.loteComGlosa,
                size: 10,
                page: page || 0,
                numLote: filter || ''
            }

            setLoadingLots(true)

            AnaliseContasService.getLotes(getProps)
                .then(({ data }) => {
                    setPageLotes(data)
                    const objectPagination = PaginationHelper.parserPagination<ILoteQuery>(data, setNumberPage)
                    setPagination(objectPagination)

                    setLoadingLots(false)
                })
                .catch(() => {
                    addToast({
                        title: 'Erro ao carregar lotes',
                        type: 'error',
                        duration: 4000
                    })
                    setPageLotes(null)
                })
        },
        [filterSelected]
    )

    const loadPage = useCallback(() => {
        AnaliseContasService.getSituacoesLotesPrestador(competencia, idPrestador).then(({ data }) => {
            setSituacoes(data)
            carregarLotes()
        })
        AnaliseContasService.validaEncerramento(competencia, idPrestador).then((data) =>
            setValidToCloseInfo({ ...data, isValidaParaFechamento: data.isValidaParaFechamento })
        )
        AnaliseContasService.getResumoPrestador(competencia, idPrestador)
            .then(({ data }) => {
                setResumoPrestador(data)
            })
            .catch(() => {
                addToast({
                    title: 'Erro ao encontrar resumo de prestador',
                    type: 'error',
                    duration: 3000
                })
            })

        // PrestadorService.getByID(idPrestador)
        //     .then(({ data }) => setPrestador(data))
        //     .catch(() => {
        //         addToast({
        //             title: 'Erro ao encontrar prestador',
        //             type: 'error',
        //             duration: 3000
        //         })
        //     })
    }, [competencia, idPrestador, setResumoPrestador, carregarLotes, setSituacoes, setValidToCloseInfo])

    useEffect(() => {
        loadPage()
    }, [])

    const handlePesquisar = () => {
        carregarLotes(numberPage, filtro, newFilter)
    }

    useEffect(() => {
        carregarLotes(numberPage, filtro, newFilter)
    }, [numberPage, filterSelected])

    useEffect(() => {
        const { porcentagem: percEmAnalise, quantidadeDeLotes: qtdAnalise } = getItemSituacao('EM_ANALISE_ADMINISTRATIVA')
        const { porcentagem: percFechado, quantidadeDeLotes: qtdFechado } = getItemSituacao('FECHADO')

        setFilterFields([
            {
                name: 'Em análise',
                value: `${qtdAnalise || '0'} - (${percEmAnalise || '0'}%)`,
                situacao: 'EM_ANALISE_ADMINISTRATIVA'
            },
            { name: 'Fechados', value: `${qtdFechado || '0'} - (${percFechado || '0'}%)`, situacao: 'FECHADO' }
        ])
    }, [situacoes])

    useEffect(() => {
        if (pageLotes && pageLotes.content) {
            initLotsCheckeds(pageLotes.content)
        }

        setPageLotes(pageLotes)
    }, [pageLotes])

    const initLotsCheckeds = (data: ILoteQuery[]) => {
        const lotsCheckeds: lotChecked[] = []
        data?.forEach((item, index) => {
            lotsCheckeds.push({
                index,
                checked: false,
                idLote: item?.idLote
            })
        })
        setLotsCheckeds(lotsCheckeds)
    }

    const handleClick = (e, element) => {
        if (
            e.target.tagName !== 'INPUT' &&
            e.target.tagName !== 'IMG' &&
            e.target.tagName !== 'LABEL' &&
            e.target.tagName !== 'svg' &&
            e.target.tagName !== 'path'
        )
            router.push(`/resumo-lote/${element}`)
    }

    useEffect(() => {
        if (!pageLotes?.content) return

        setCheckedAll(lotsCheckeds.filter((i) => i.checked).length === pageLotes?.content?.length)
    }, [lotsCheckeds, pageLotes])

    const handleReabrirLotes = useCallback(() => {
        const props = {
            competencia: competencia,
            prestadorId: idPrestador
        }

        const idLotes = lotsCheckeds.filter((item) => item.checked).map((lot) => lot.idLote)

        setReabrirLotesInProgress(true)

        AnaliseContasService.patchReabrirLote(idLotes, props)
            .then(() => {
                addToast({ type: 'success', title: 'Lote(s) reaberto(s) com sucesso' })
                loadPage()
            })
            .catch((err) => {
                addToast({ type: 'error', title: 'Erro ao reabrir lote(s)', description: getMessageErrorFromApiResponse(err) })
            })
            .finally(() => {
                setIsModalReabrirOpen(false)
                setReabrirLotesInProgress(false)
            })
        //window.location.reload()
    }, [addToast, loadPage, pageLotes, competencia, idPrestador, lotsCheckeds])

    useEffect(() => {
        setLotesChecados(
            pageLotes?.content?.filter((lote, index) => {
                if (lotsCheckeds[index]?.checked && lotsCheckeds[index]) return lote
            })
        )
    }, [lotsCheckeds])
    const checkAll = useCallback(() => {
        setLotsCheckeds(lotsCheckeds.map((item) => ({ ...item, checked: !checkedAll })))
    }, [lotsCheckeds, checkedAll, setLotsCheckeds])

    const handleOnClosePesquisa = () => {
        setFiltro('')
        carregarLotes(numberPage)
    }

    const finishProcess = useCallback(() => {
        setLoadingFinishProcess(true)
        AnaliseContasService.putEncerrarPorPrestador(idPrestador, competencia)
            .then(() => {
                loadPage()
                addToast({
                    title: 'Encerramento concluído com sucesso',
                    type: 'success'
                })
            })
            .catch((err) =>
                addToast({
                    title: 'Erro ao encerrar processamento',
                    type: 'error',
                    description: getMessageErrorFromApiResponse(err),
                    duration: 3000
                })
            )
            .finally(() => setLoadingFinishProcess(false))
    }, [addToast, loadPage, setLoadingFinishProcess, idPrestador, competencia])

    const handleGlosarLotes = useCallback(
        (reasons: IGlossReason[]) => {
            const data = {
                lotes: lotesChecados?.map((lot) => lot.idLote.toString()),
                motivosGlosa: reasons.map((r) => ({ motivoGlosa: r.motivoGlosaUUID, justificativaGlosa: r.justificativaGlosa }))
            }
            return AnaliseContasService.patchGlosarLotes(data)
                .then(() => {
                    addToast({ type: 'success', title: 'Lotes glosados com sucesso' })
                    setIsOpenGlosarLote(false)
                    loadPage()
                })
                .catch(() => {
                    addToast({ type: 'error', title: 'Erro ao glosa lotes' })
                })
        },
        [addToast, loadPage, lotesChecados, setIsOpenGlosarLote]
    )

    const handleReabrir = useCallback(() => {
        AnaliseContasService.putReabrirCompetenciaPorPrestador(idPrestador, competencia)
            .then(() => loadPage())
            .catch((err) => {
                addToast({ type: 'error', title: 'Erro ao reabrir processamento', description: getMessageErrorFromApiResponse(err) })
            })
    }, [loadPage, addToast])

    useEffect(() => {
        setNewFilter({
            numeroGuia: '',
            tipoLote: 'TODOS',
            tipoProcesso: 'TODOS',
            loteComGlosa: false,
            loteSemGlosa: false
        })
    }, [filterSelected])

    useEffect(() => {
        if (showFilter) getTipoLote()
    }, [showFilter])

    return (
        <>
            <S.Header>
                <S.TitleCard>
                    <S.Title>{capitalize(resumoPrestador?.nomeFantasia)}</S.Title>
                    <S.SubTitle>{`CNPJ - ${StringUtils.maskCnpj(resumoPrestador?.cnpj)}`}</S.SubTitle>
                </S.TitleCard>
                <S.Button>
                    {resumoPrestador.situacaoPrestador === SituacaoPrestadorAnalise.ENCERRADO ? (
                        <Button
                            typeButton="text"
                            themeButton="ihealth"
                            onClick={handleReabrir}
                            style={{ width: 'fit-content' }}
                            iconLeft="/faturamento/assets/icons/refresh.svg"
                        >
                            Reabrir
                        </Button>
                    ) : (
                        <Button
                            disabled={loadingFinishProcess || !validToCloseInfo?.isValidaParaFechamento}
                            themeButton={'warning'}
                            onClick={finishProcess}
                            title={validToCloseInfo?.mensagem}
                        >
                            Encerrar
                        </Button>
                    )}
                </S.Button>
            </S.Header>
            <DividerSectionCard>
                <TitleSection>Valores</TitleSection>
                <S.ValuesContainer>
                    <S.ContentData>
                        <ContentDataInfo
                            className={'center-item'}
                            label={'Apresentado'}
                            value={!resumoPrestador.valorApresentado ? 'R$ 0,00' : NumberUtils.maskMoney(resumoPrestador?.valorApresentado)}
                            background={'grey'}
                            standart={'expanded'}
                        />
                        <ContentDataInfo
                            className={'center-item'}
                            label={'Apurado'}
                            value={!resumoPrestador.valorApurado ? 'R$ 0,00' : NumberUtils.maskMoney(resumoPrestador?.valorApurado)}
                            background={'grey'}
                            standart={'expanded'}
                        />
                        <ContentDataInfo
                            className={'center-item'}
                            label={'Glosado'}
                            value={!resumoPrestador.valorGlosado ? 'R$ 0,00' : NumberUtils.maskMoney(resumoPrestador?.valorGlosado)}
                            background={'grey'}
                            standart={'expanded'}
                        />
                    </S.ContentData>
                </S.ValuesContainer>
            </DividerSectionCard>
            <DividerSectionCard dividerContent={true}>
                <S.HeaderList>
                    <SearchBar
                        value={filtro}
                        handleOnSearch={handlePesquisar}
                        placeholder="Procure por lote"
                        handleOnClose={handleOnClosePesquisa}
                        handleOnChange={(e) => {
                            if (e?.target?.value === '') {
                                return handleOnClosePesquisa()
                            }

                            setFiltro(e?.target?.value)
                        }}
                    />
                    <S.FilterButtonWrapper onClick={() => setShowFilter(!showFilter)}>
                        <ReactSVG src="/faturamento/assets/icons/filter.svg" />
                        <p>Filtros</p>
                    </S.FilterButtonWrapper>
                </S.HeaderList>
                <div style={{ maxWidth: '300px' }}>
                    <CardsFilter cardsFilter={filterFields} filterSelected={filterSelected} setFilterSelected={setFilterSelected} />
                </div>
                {showFilter && (
                    <S.FilterSelectWrapper>
                        <div className="row1">
                            <TextField
                                label="Número da guia"
                                fullWidth
                                value={newFilter?.numeroGuia}
                                onChange={(e) => setNewFilter({ ...newFilter, numeroGuia: e?.target?.value })}
                            />
                            <FormControl>
                                <InputLabel>Tipo de processo</InputLabel>
                                <Select
                                    defaultValue="TODOS"
                                    value={newFilter?.tipoProcesso}
                                    onChange={(e) => {
                                        setNewFilter({
                                            ...newFilter,
                                            tipoProcesso: e.target.value as 'TODOS' | 'REGULAR' | 'LIMINAR' | 'EXCEPCIONALIDADE'
                                        })
                                    }}
                                >
                                    <MenuItem value="TODOS">Todos</MenuItem>
                                    <MenuItem value="REGULAR">Regular</MenuItem>
                                    <MenuItem value="LIMINAR">Liminar</MenuItem>
                                    <MenuItem value="EXCEPCIONALIDADE">Excepcionalidade</MenuItem>
                                </Select>
                            </FormControl>
                            <FormControl>
                                <InputLabel>Tipo de lote</InputLabel>
                                <Select
                                    defaultValue="TODOS"
                                    value={newFilter?.tipoLote}
                                    onChange={(e) => {
                                        setNewFilter({
                                            ...newFilter,
                                            tipoLote: e.target.value as 'TODOS' | TipoLoteGuiaEnum
                                        })
                                    }}
                                >
                                    <MenuItem value="TODOS">Todos</MenuItem>
                                    {tipoLoteOptions?.map((options, index) => (
                                        <MenuItem key={index} value={options?.value}>
                                            {options?.label}
                                        </MenuItem>
                                    ))}
                                    {/* <MenuItem value="TODOS">Todos</MenuItem>
                                    <MenuItem value="CONSULTA">Consulta</MenuItem>
                                    <MenuItem value="HONORARIO">Honorário</MenuItem>
                                    <MenuItem value="SPSADT">SP/SADT</MenuItem> */}
                                </Select>
                            </FormControl>
                        </div>
                        <div className="row2">
                            <div>
                                <FormControlLabel
                                    control={
                                        <Checkbox
                                            checked={newFilter?.loteSemGlosa}
                                            onChange={({ target }) => {
                                                setNewFilter({ ...newFilter, loteComGlosa: false, loteSemGlosa: target.checked })
                                            }}
                                        />
                                    }
                                    label={<S.LabelCheckbox>Lotes sem glosa</S.LabelCheckbox>}
                                />
                                <FormControlLabel
                                    control={
                                        <Checkbox
                                            checked={newFilter?.loteComGlosa}
                                            onChange={({ target }) => {
                                                setNewFilter({ ...newFilter, loteComGlosa: target.checked, loteSemGlosa: false })
                                            }}
                                        />
                                    }
                                    label={<S.LabelCheckbox>Lotes com glosa</S.LabelCheckbox>}
                                />
                            </div>

                            <div style={{ gap: '8px', display: 'flex' }}>
                                <ButtonMui
                                    color="neutral"
                                    variant="text"
                                    onClick={() =>
                                        setNewFilter({
                                            numeroGuia: '',
                                            tipoLote: 'TODOS',
                                            tipoProcesso: 'TODOS',
                                            loteComGlosa: false,
                                            loteSemGlosa: false
                                        })
                                    }
                                >
                                    Limpar
                                </ButtonMui>
                                <ButtonMui
                                    color="primary"
                                    variant="outlined"
                                    onClick={() => {
                                        handlePesquisar()
                                    }}
                                >
                                    Filtrar
                                </ButtonMui>
                            </div>
                        </div>
                    </S.FilterSelectWrapper>
                )}

                {loadingLots ? (
                    <AnimatedLoadingLottie style={{ width: '240px', margin: 'auto' }} />
                ) : pageLotes?.content?.length === 0 ? (
                    <div style={{ marginTop: '20px' }}>
                        <NoContent title="Por enquanto não há lotes" />
                    </div>
                ) : (
                    <>
                        <>
                            <S.ContentItemResult>
                                {filterSelected == 'PEDENTE' ? (
                                    <S.CheckBoxContainer></S.CheckBoxContainer>
                                ) : (
                                    <S.CheckBoxContainer>
                                        <input
                                            type="checkbox"
                                            checked={checkedAll}
                                            onClick={() => {
                                                checkAll()
                                            }}
                                        />
                                    </S.CheckBoxContainer>
                                )}
                                {lotsCheckeds?.some((i) => i.checked) && filterSelected === 'EM_ANALISE_ADMINISTRATIVA' ? (
                                    <>
                                        {/* <Item>
                                            <p className="button" onClick={() => setIsOpenRelatarPendencia(true)}>
                                                Relatar pendência
                                            </p>
                                        </Item> */}
                                        <Button
                                            style={{ whiteSpace: 'nowrap' }}
                                            typeButton="ghost"
                                            themeButton="gray"
                                            onClick={() => setIsOpenGlosarLote(true)}
                                        >
                                            Glosar lotes
                                        </Button>
                                    </>
                                ) : lotsCheckeds?.some((i) => i.checked) && filterSelected === 'FECHADO' ? (
                                    <S.LotsCheckedsAction>
                                        <span className="lotsCheckedsQtd">
                                            {`${lotsCheckeds.filter((i) => i.checked).length} ${
                                                lotsCheckeds.filter((i) => i.checked).length === 1 ? 'Selecionado' : 'Selecionados'
                                            }`}
                                        </span>
                                        <Button
                                            style={{ whiteSpace: 'nowrap' }}
                                            typeButton="ghost"
                                            themeButton="gray"
                                            onClick={() => setIsModalReabrirOpen(true)}
                                        >
                                            Reabrir lotes
                                        </Button>
                                    </S.LotsCheckedsAction>
                                ) : (
                                    <>
                                        <Item>
                                            <p>Lotes</p>
                                        </Item>
                                        <Item>
                                            <p>Análise técnica</p>
                                        </Item>
                                        <Item>
                                            <p>Análise adm.</p>
                                        </Item>
                                        <Item>
                                            <p>Valor apresentado</p>
                                        </Item>

                                        <Item>
                                            <p>Valor glosado</p>
                                        </Item>
                                        <Item>
                                            <p>Valor apurado</p>
                                        </Item>
                                        <Item>
                                            <p></p>
                                        </Item>
                                    </>
                                )}
                            </S.ContentItemResult>
                        </>
                        {pageLotes?.content?.map((obj, index) => (
                            <S.ContentItemResultGlosa key={index} checked={lotsCheckeds[index]?.checked}>
                                {filterSelected == 'PEDENTE' ? (
                                    <S.CheckBoxContainer></S.CheckBoxContainer>
                                ) : (
                                    <Item>
                                        <S.CheckBoxContainer>
                                            <input
                                                type="checkbox"
                                                checked={lotsCheckeds[index]?.checked}
                                                onChange={() => {
                                                    const newLotsCheckeds = [...lotsCheckeds]

                                                    if (!newLotsCheckeds) return

                                                    if (!newLotsCheckeds[index]) return

                                                    newLotsCheckeds[index].checked = !newLotsCheckeds[index]?.checked
                                                    setLotsCheckeds(newLotsCheckeds)
                                                }}
                                            />
                                        </S.CheckBoxContainer>
                                    </Item>
                                )}
                                <></>
                                <Item>
                                    <S.NumeroLoteWrapper>
                                        <p>{obj?.numeroLote}</p>
                                        <span>{obj?.tipoLote}</span>
                                    </S.NumeroLoteWrapper>
                                </Item>
                                <Item>
                                    <span>
                                        {obj.guiasEmAnaliseTecnica !== undefined && obj.guiasEmAnaliseTecnicaTotal !== undefined
                                            ? `${obj.guiasEmAnaliseTecnica} de ${obj.guiasEmAnaliseTecnicaTotal} guias`
                                            : '---'}
                                    </span>
                                </Item>
                                <Item>
                                    <span>
                                        {obj.guiasEmAnaliseAdm !== undefined && obj.guiasEmAnaliseAdmTotal !== undefined
                                            ? `${obj.guiasEmAnaliseAdm} de ${obj.guiasEmAnaliseAdmTotal} guias`
                                            : '---'}
                                    </span>
                                </Item>
                                <Item>
                                    <span>{NumberUtils.maskMoney(obj?.valorApresentado)}</span>
                                </Item>
                                <Item>
                                    <span>{NumberUtils.maskMoney(obj?.valorGlosado)}</span>
                                </Item>
                                <Item>
                                    <span style={{ color: 'black', fontWeight: 600 }}>{NumberUtils.maskMoney(obj?.valorApurado)}</span>
                                </Item>
                                <Item>
                                    <TableRowIconButton
                                        style={{ marginLeft: 'auto' }}
                                        icon="/faturamento/assets/icons/mai-ic-visible-true.svg"
                                        title="Ver detalhes"
                                        onClick={() =>
                                            router.push(`/processamento-contas/analise-contas/resumo-lote/${obj.idLote}?competencia=${competencia}`)
                                        }
                                    />
                                </Item>
                                {/* {status === 'Em aberto' ? (
                                    <Item style={{ flexDirection: 'row', gap: '25px', marginLeft: 'auto' }}>
                                        <ReactSVG src="/faturamento/assets/icons/warning.svg" />
                                        <ReactSVG src="/faturamento/assets/icons/shut.svg" />
                                    </Item>
                                ) : status === 'Fechado' ? (
                                    <Item>
                                        <ReactSVG style={{ marginLeft: 'auto' }} src="/faturamento/assets/icons/sync.svg" />
                                    </Item>
                                ) : null} */}
                            </S.ContentItemResultGlosa>
                        ))}
                        <S.PaginationContainer>
                            <Pagination
                                totalPage={pagination?.totalPaginas}
                                totalRegister={pagination?.totalRegistros}
                                actualPage={pagination?.paginaAtual}
                                setNumberPage={pagination?.setNumberPage}
                            />
                            <S.Page>
                                <span className="pageNumber">
                                    {pagination?.paginaAtual * pagination?.linhasPorPagina + 1} -{' '}
                                    {pageLotes?.content?.length + pagination?.paginaAtual * pagination?.linhasPorPagina}
                                    {' de '}
                                    {pagination?.totalRegistros}
                                </span>
                            </S.Page>
                        </S.PaginationContainer>
                    </>
                )}
            </DividerSectionCard>
            <ModalRelatarPendencia
                isOpen={isOpenRelatarPendencia}
                setIsOpen={setIsOpenRelatarPendencia}
                titleModal={'Relatar pendência'}
                selectedLots={lotesChecados}
            />
            {/* <ModalGlosarLote
                isOpen={isOpenGlosarLote}
                setIsOpen={setIsOpenGlosarLote}
                titleModal={'Glosar Lote'}
                selectedLots={lotesChecados}
                reloadLots={loadPage}
            /> */}
            <ModalAddGlossReasons
                isOpen={isOpenGlosarLote}
                onClose={() => {
                    setIsOpenGlosarLote(!isOpenGlosarLote)
                }}
                onConfirm={handleGlosarLotes}
            />

            <Modal
                isOpen={isModalReabrirOpen}
                style={{ width: '35vw', padding: '24px' }}
                onClose={() => {
                    setIsModalReabrirOpen(!isModalReabrirOpen)
                }}
            >
                <S.ModalContainer>
                    <S.ModalTitle>Reabrir lotes(s)</S.ModalTitle>

                    <p>Deseja reabrir o(s) lotes(s) selecionado(s)?</p>
                    <S.ContainerButtons style={{ marginTop: '28px' }}>
                        <Button
                            typeButton="text"
                            style={{
                                backgroundColor: 'transparent',
                                borderColor: 'gray',
                                width: '35%',
                                color: '#0000008f'
                            }}
                            onClick={() => {
                                setIsModalReabrirOpen(false)
                            }}
                        >
                            Cancelar
                        </Button>
                        <Button
                            className="btn-cancelar-guia"
                            themeButton="warning"
                            style={{
                                width: '30%'
                            }}
                            onClick={() => {
                                handleReabrirLotes()
                            }}
                            disabled={reabrirLotesInProgress}
                        >
                            Confirmar
                        </Button>
                    </S.ContainerButtons>
                </S.ModalContainer>
            </Modal>
        </>
    )
}

export default ResumoPrestadorTemplate
