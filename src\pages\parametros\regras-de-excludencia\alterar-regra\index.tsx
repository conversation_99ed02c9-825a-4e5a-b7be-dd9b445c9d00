import React from 'react'
import { useParameterContext } from 'src/context/ParametersContext/useParameter'
import RegraExcludenciaEditarTemplate from 'src/templates/FaturamentoOperadora/ParametrosFaturamentoTemplates/RegraExcludencia/RegraExcludenciaEditarTemplate'

const AlterarRegra = () => {
    const [parameterForm, setParameterForm] = useParameterContext()
    return <RegraExcludenciaEditarTemplate data={parameterForm} setData={setParameterForm} />
}

export default AlterarRegra
