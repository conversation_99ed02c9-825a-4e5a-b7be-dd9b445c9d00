import styled from 'styled-components'
import theme from 'styles/theme'

export const Container = styled.div`
    display: flex;
    flex-direction: column;
    gap: 2.4rem;
`

export const Content = styled.div`
    padding: 2.4rem;
    border-radius: 0.8rem;
    display: flex;
    flex-direction: column;
    gap: 2.4rem;
    background-color: #ffffff;
`

export const Header = styled.div`
    display: flex;
    flex-direction: column;
    gap: 0.8rem;

    .subtitle {
        font-size: 1.4rem;
        line-height: 2rem;
        font-weight: 400;
        color: #3e4e65;

        > b {
            font-weight: 700;
        }
    }
`

export const WrapperPagination = styled.div`
    display: flex;
    justify-content: space-between;
    align-items: flex-end;

    > p {
        font-size: 1.6rem;
        line-height: 2.4rem;
        color: rgba(0, 0, 0, 0.56);
    }
`

export const EmptyListMessage = styled.p`
    font-size: 1.6rem;
    line-height: 2.4rem;
    font-weight: 400;
    color: ${theme.colors.black[56]};
    text-align: center;
`

export const DataEnvio = styled.div`
    display: flex;
    align-items: center;
    gap: 0.8rem 1.6rem;
    flex-wrap: wrap;
`

export const TagDuracaoLoteAberto = styled.div`
    width: fit-content;
    display: flex;
    align-items: center;
    gap: 0.8rem;
    border-radius: 0.4rem;
    padding: 0.4rem 0.8rem;
    background-color: ${theme.colors.secondary[600]};

    .tag-icon {
        width: 1.8rem;
        height: 1.8rem;
        display: grid;
        place-items: center;

        div {
            line-height: 0;
        }

        svg {
            width: 100%;
            height: 100%;
            path {
                fill: #ffffff;
            }
        }
    }

    > p {
        font-size: 1.2rem;
        line-height: 1.6rem;
        font-weight: 400;
        color: #ffffff;
        white-space: nowrap;
    }
`
