import { IPage, ISortPage } from 'src/types/pagination'
// ===============================GET DEMONSTRATIVO=================================

export interface IGeDemonstrativoPageable extends IPage {
    content: IGeDemonstrativo[]
}
export interface IGeDemonstrativo {
    id: number
    nomePrestador: string
    cnpj: string
    nomeArquivoPed: string
    nomeArquivoEmp: string
    numeroPed: string
    numeroEmp: string
    // YYYY-MM-DD
    competencia: string
    tipoPrestador: 'CREDENCIADO' | 'REFERENCIADO_EVENTUAL'
    liberado: boolean
    bloqueadoAnaliseNF: boolean
    valorBruto: number
    tipoFaturamento?: string
}
export interface IGetArquivoProps extends ISortPage {
    // YYYY-MM-DD
    competencia: string
    nomeCNPJCodigo?: string
}

// ===============================GET BY ID NOTA FISCAL=================================

// export interface IGetNFPageable extends IPage {
//     content: IGetNF[]
// }

export interface IGetNF {
    dadosNotaFiscalDTO: IDadosNotaFiscalDTO
}
export interface IDadosNotaFiscalDTO {
    numeroNF: string
    tipoNF:
        | 'NOTA_FISCAL_ELETRONICA_PRODUTOS_OU_MERCADORIAS'
        | 'NOTA_FISCAL_CONHECIMENTO_TRANSPORTE_ELETRONICO'
        | 'NOTA_FISCAL_CUPOM_FISCAL'
        | 'NOTA_FISCAL_AO_CONSUMIDOR_ELETRONICO'
        | 'NOTA_FISCAL_MANIFESTO_DOCUMENTOS_FISCAIS_ELETRONICOS'
        | 'NOTA_FISCAL_AVULSA'
        | 'NOTA_FISCAL_REMESSA'
        | 'NOTA_FISCAL_COMPLEMENTAR'
        | 'NOTA_FISCAL_EXPORTACAO'
        | 'NOTA_FISCAL_REJEITADA'
        | 'NOTA_FISCAL_DENEGADA'
        | 'NOTA_FISCAL_ELETRONICA_SERVICOS'
    dataEmissaoNF: string
    dataEnvioNF: string
    situacaoNF: 'EM_ANALISE' | 'REPROVADA' | 'APROVADA' | 'CANCELADA'
    dataAprovacaoNF: string
    motivoReprovacao: string
    arquivos: IArquivo[]
}

export interface IArquivo {
    nome: string
    url: string
}
// ===============================GET BY ID HISTORICO REPROVAÇÕES=================================

export interface IGeReprovacoes {
    reprovacoes: IReprovacoes[]
}

export interface IReprovacoes {
    numeroNF: string
    dataReprovacao: Date
    usuario: string
    motivo: string
    urlNF: string
}
// ===============================GET BY ID HISTORICO BLOQUEIOS=================================

export interface IGetBloqueios {
    bloqueios: IBloqueios[]
}

export interface IBloqueios {
    data: Date
    motivo: string
    acao: 'BLOQUEIO' | 'DESBLOQUEIO'
    usuario: string
}
// ===============================GET BY ID DADOS GERAIS=================================

// export interface IGetDadosGeraisPageable extends IPage {
//     content: IGetDadosGerais[]
// }

export interface IGetDadosGerais {
    demonstrativoUUID: string
    dadosConta: IDadosConta
    dadosFinanceiros: IDadosFinanceiros
    dadosBancarios: IDadosBancarios
}

export interface IDadosConta {
    valorGlosa: number
    valorExtraTeto: number
    valorExtraTetoAcumulado: number
    valorTeto: number
    valorLiberadoCompetenciasAnteriores: number
    baseCalculoIRDemaisServicos: number
    baseCalculoIRReduzida: number
    valorProducao: number
    valorAprovadoPagamento: number
}

export interface IDetalhesValores {
    nome: string
    valor: string
}

export interface IDadosFinanceiros {
    codigoEPrestador: string
    cnpj: string
    codigoIBGEMunicipio: string
    nomeMunicipio: string
    competencia: string
    numeroPED: string
    numeroEMP: string
    numeroLIQ: string
    numeroNOB: string
    valorBrutoDetalhes?: IDetalhesValores[]
    valorRetencaoIRDetalhes?: IDetalhesValores[]
    valorRetencaoISSDetalhes?: IDetalhesValores[]
    valorLiquidoDetalhes?: IDetalhesValores[]
    valorBruto: number
    valorRetencaoIR: number
    valorRetencaoISS: number
    valorOutrosDescontos: number
    valorLiquido: number
    statusDemostrativo:
        | 'GERADO'
        | 'CANCELADO'
        | 'AGUARDANDO_ENVIO_NF'
        | 'AGUARDANDO_APROVACAO_NF'
        | 'NF_APROVADA'
        | 'NF_REPROVADA'
        | 'AGUARDANDO_CONFIRMACAO_PAGAMENTO'
        | 'PAGAMENTO_CONFIRMADO'
    // 2023-04-20
    dataConfirmacaoPagamento: string
    numeroNotaFiscalAprovada: string
    dataEmissaoNotaFiscalAprovada: string
    dataEnvioNotaFiscalAprovada: string
    dataAprovacaoNotaFiscalAprovada: string
    parcelaDescontos: IDescontos[]
    exercicios: string
}
export interface IDescontos {
    descricaoDesconto: string
    valorParcela: number
}
export interface IDadosBancarios {
    numeroBanco: string
    nomeBanco: string
    numeroAgencia: string
    numeroContaCorrente: string
}

export interface IDescontos {
    descricaoDesconto: string
    valorParcela: number
}
// ===============================GET BY UUID=================================

export interface IGetDadoByUUID {
    dadosBancoriosDTO: IDadosBancoriosDTO
    financeiroDTO: IFinanceiroDTO
}

export interface IDadosBancoriosDTO {
    numero_banco: string
    banco: string
    agencia: string
    conta: string
}

export interface IFinanceiroDTO {
    codigo: number
    nome: string
    municipio: string
    copetencia: Date
    num_Ped: string
    num_Emp: string
    num_Liq: string
    num_Nob: string
    valor_Bruto: number
    valor_IR: number
    valor_ISS: number
    valor_Descontos: number
    valor_Liquido: number
    cpfCnpj: string
}

// ===============================PATCH BY ID OPÇÕES=================================

export interface IPatchOpcoesForm {
    acao: 'BLOQUEAR_ANALISE_NF' | 'DESBLOQUEAR_ANALISE_NF'
    motivo: string
}

// ===============================PATCH BY ID OPÇÕES=================================

export interface IPatchLiberar {
    demonstrativosIds: number[]
}

// ===============================POST DEMONSTARATIVO=================================

export interface IPostDemonstrativoForm {
    prestadorCompetenciaIds: number[]
    competencia?: string
    tipoPrestador?: 'CREDENCIADO' | 'REFERENCIADO_EVENTUAL'
    codigosMunicipios?: string[]
    segregarMunicipios?: boolean
}

// ===============================PUT CANCELAR DEMONSTARATIVO================================

export interface IPutCancelar {
    demostrativosIds?: number[]
    motivo: string
}
