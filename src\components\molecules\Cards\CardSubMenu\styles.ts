/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-unused-vars */
import styled, {css} from 'styled-components';

type CardSubMenuProps = {
	isDisabled?: boolean;
};

export const CardSubMenu = styled.div<CardSubMenuProps>`
	${({theme, isDisabled}) => css`
		border: 1px solid #ccc;
		border-radius: 10px;
		padding: 20px;
		width: 100%;
		height: 120px;
		background-color: ${theme.colors.white['100']};
		opacity: ${isDisabled ? '0.5' : '1'};
		cursor: ${isDisabled ? 'no-drop' : 'pointer'};
		transition: all 0.5s;

		p {
			font-size: 1.2rem;
		}

		:hover {
			background-color: ${isDisabled ? '#fafafa' : theme.colors.primary['500']};

			h1,
			p {
				color: ${isDisabled ? '' : theme.colors.white['bg']};
			}

			svg path {
				fill: ${isDisabled ? theme.colors.primary['500'] : theme.colors.white['bg']};
			}
		}
	`}
`;

export const Content = styled.div`
	${({theme}) => css`
		display: flex;
		margin-bottom: 10px;

		h1 {
			color: ${theme.colors.black['56']};
		}

		span {
			margin-right: 10px;
		}
	`}
`;
