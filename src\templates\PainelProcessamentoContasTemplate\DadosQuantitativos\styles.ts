import styled from 'styled-components'
import theme from 'styles/theme'

const CardThemeConfigs = {
    ['green']: {
        bgColor: 'rgba(0, 97, 0, 0.08)',
        labelTextColor: theme.colors.black[88],
        valueTextColor: '#3e4e65',
        iconBgColor: '#006100'
    },
    ['pink']: {
        bgColor: 'rgba(178, 18, 6, 0.08)',
        labelTextColor: theme.colors.black[88],
        valueTextColor: '#3e4e65',
        iconBgColor: '#940A00'
    },
    ['grey']: {
        bgColor: theme.colors.black['08'],
        labelTextColor: theme.colors.black[88],
        valueTextColor: '#3e4e65',
        iconBgColor: '#3e4e65'
    },
    ['yellow']: {
        bgColor: theme.colors.secondary[400],
        labelTextColor: theme.colors.black[88],
        valueTextColor: '#3e4e65',
        iconBgColor: theme.colors.secondary[600]
    }
}

export const Container = styled.div``

type CardDefaultProps = {
    cardTheme: 'green' | 'pink' | 'grey' | 'yellow'
}
export const CardDefault = styled.div<CardDefaultProps>`
    width: 100%;
    height: 9.6rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.6rem;
    padding: 1.6rem;
    border-radius: 0.8rem;
    background-color: ${({ cardTheme }) => CardThemeConfigs[cardTheme].bgColor};

    .card-data {
        display: flex;
        flex-direction: column;
        gap: 0.4rem;
    }

    .label {
        font-size: 1.6rem;
        line-height: 2.4rem;
        font-weight: 600;
        color: ${({ cardTheme }) => CardThemeConfigs[cardTheme].labelTextColor};
        text-align: center;
        white-space: nowrap;
    }

    .value {
        font-size: 1.6rem;
        line-height: 2.4rem;
        font-weight: 600;
        color: ${({ cardTheme }) => CardThemeConfigs[cardTheme].valueTextColor};
        text-align: center;
    }
`

export const CardWithIcon = styled(CardDefault)`
    justify-content: flex-start;

    .card-data {
        row-gap: 0;
    }

    .label {
        text-align: left;
    }

    .value {
        font-size: 2.8rem;
        line-height: 3.6rem;
        font-weight: 700;
        text-align: left;

        .value-description {
            font-size: 1.2rem;
            line-height: 2rem;
            font-weight: 600;
        }
    }
`

export const HorizontalCard = styled(CardDefault)`
    height: fit-content;
    gap: 2.4rem;
    padding: 0.8rem;

    .card-data {
        flex-direction: row;
        column-gap: 1.6rem;
    }
`

type CardIconProps = {
    cardTheme: 'green' | 'pink' | 'grey' | 'yellow'
}
export const CardIcon = styled.div<CardIconProps>`
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    padding: 0.8rem;
    display: grid;
    place-items: center;
    background-color: ${({ cardTheme }) => CardThemeConfigs[cardTheme].iconBgColor};

    > div {
        line-height: 0;
        svg {
            path {
                fill: #ffffff;
                fill-opacity: 1;
            }
        }
    }
`

export const CardInfoIcon = styled.div`
    cursor: pointer;

    > div {
        line-height: 0;
        svg path {
            fill: #5459f6;
        }
    }
`

export const CardButton = styled.div`
    width: fit-content;
    padding: 0.8rem 1.2rem;
    border-radius: 999px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.8rem;
    background-color: #3e4e65;
    color: #ffffff;
    font-size: 1.2rem;
    line-height: 1.6rem;
    font-weight: 400;
    cursor: pointer;
    user-select: none;
`

export const FlexRow = styled.div`
    display: flex;
    gap: 1.6rem;

    @media screen and (max-width: 980px) {
        flex-wrap: wrap;
        ${CardDefault} {
            width: 100% !important;
        }
    }
`

export const FlexColumn = styled.div`
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 1.6rem;
`
