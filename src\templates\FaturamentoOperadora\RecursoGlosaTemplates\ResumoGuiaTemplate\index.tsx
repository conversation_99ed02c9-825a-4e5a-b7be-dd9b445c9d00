import React, { useEffect, useState } from 'react'
import { useToast } from 'src/hooks/toast'
import { useRouter } from 'next/router'
import Button from 'components/atoms/Button'
import DividerSectionCard from 'components/atoms/DividerSectionCard'
import { IResumoGuiaRecursoDTO } from 'types/analiseContas/guia-recurso'
import { GuiaRecursoService } from 'src/services/analiseContasApi/guiaRecurso'
import ContentDataInfo from 'components/molecules/ShowDataConfirm'
import { NumberUtils } from 'utils/numberUtils'
import { TipoGuiaEnum } from 'src/services/composicaoPagamentoApi/enum'
import * as S from './styles'
import GeneralInformation from '../Components/GeneralInformation'
import GuideInformationTable from '../Components/GuideInformationTable'
import { RecursoGlosaOperadoraGuia } from 'src/services/recurso-glosa-operadora/guia'
import { QuantidadeTipoDto } from 'src/services/recurso-glosa-operadora/guia/type'
import { capitalize } from 'utils/capitalize'
import Modal from 'components/atoms/Modal'
import ModalIndeferirGuia from '../Components/ModalIndeferirGuia'
import { DateUtils } from 'utils/dateUtils'
import { RecursoGlosaService } from 'src/services/analiseContasApi/recursoGlosaServices'

const ResumoGuiaTemplate = ({ uuid }) => {
    const { addToast } = useToast()
    const router = useRouter()
    const [resumoLoteGuia, setResumoLoteGuia] = useState<IResumoGuiaRecursoDTO>()
    const [step, setStep] = useState<number>(0)
    const [loadingFecharAbrir, setLoadingFecharAbrir] = useState<boolean>()
    const [selectedIndex, setSelectedIndex] = useState<number>(0)
    const [tabName, setTabName] = useState<string>(null)
    const [openModalReabrirGuia, setOpenModalReabrirGuia] = useState<boolean>(false)
    const [refresh, setRefresh] = useState(false)

    const [dataContent, setDataContent] = useState<QuantidadeTipoDto[]>([
        {
            tipo: 'INFORMACOES_GERAIS',
            quantidade: null
        }
    ])

    const [isOpen, setIsOpen] = useState({
        openModalFinalizar: false,
        openModalIndeferir: false,
        openModalDeferir: false
    })

    useEffect(() => {
        if (!uuid) return
        getResumoGuia()
    }, [router.isReady, refresh])

    const getResumoGuia = () => {
        GuiaRecursoService.getResumoGuiaRecurso(uuid)
            .then(({ data }) => {
                setResumoLoteGuia(data)
            })
            .catch(({ response }) => {
                addToast({
                    title: response?.data?.message,
                    type: 'error',
                    duration: 3000
                })
            })
    }

    useEffect(() => {
        RecursoGlosaOperadoraGuia.getQuantidadeItens(uuid)
            .then(({ data }) => {
                const newDataContent = data?.map((item) => ({
                    tipo: item?.tipo,
                    quantidade: item?.quantidade
                }))

                setDataContent((prevData) => [...prevData, ...newDataContent])
            })
            .catch(({ response }) => {
                addToast({
                    title: response?.data?.message,
                    type: 'error',
                    duration: 3000
                })
            })
    }, [])

    const handleClickToFinish = () => {
        RecursoGlosaOperadoraGuia.patchFinalizarGuia(uuid)
            .then(({ data }) => {
                addToast({
                    title: 'Guia finalizada com sucesso!',
                    type: 'success',
                    duration: 3000
                })

                setRefresh(!refresh)
                getResumoGuia()
            })
            .catch(({ response }) => {
                addToast({
                    title: response?.data?.message,
                    type: 'error',
                    duration: 3000
                })
            })
            .finally(() => {
                setIsOpen({
                    ...isOpen,
                    openModalFinalizar: false
                })
            })
    }

    // DEFERIR
    const handleClickToDeferir = () => {
        RecursoGlosaOperadoraGuia.patchDeferirGuia(uuid)
            .then(() => {
                addToast({
                    title: 'Guia deferida com sucesso!',
                    type: 'success',
                    duration: 3000
                })

                setRefresh(!refresh)
                getResumoGuia()
            })
            .catch(({ response }) => {
                addToast({
                    title: response?.data?.message,
                    type: 'error',
                    duration: 3000
                })
            })
            .finally(() => {
                setIsOpen({
                    ...isOpen,
                    openModalDeferir: false
                })
            })
    }

    enum EnumTipo {
        INFORMACOES_GERAIS = 'Informações Gerais',
        HONORARIOS = 'Honorários',
        PROCEDIMENTOS = 'Procedimentos',
        TAXA_DIARIA_GAS = 'Taxas, Diárias e Gases',
        MATERIAL_OPME = 'Materiais e OPME',
        MEDICAMENTOS = 'Medicamentos'
    }

    const handleReabrirGuia = () => {
        setLoadingFecharAbrir(true)
        RecursoGlosaService.patchReabrirGuiaRecurso(uuid)
            .then(() => {
                addToast({ title: 'Guia reaberta com sucesso', type: 'success', duration: 3000 })
                setOpenModalReabrirGuia(false)
                setRefresh(!refresh)
                // getQuantitativosPrestador()
            })
            .catch(({ response }) => {
                addToast({
                    title: response?.data?.message,
                    type: 'error',
                    duration: 3000
                })
            })
            .finally(() => {
                setLoadingFecharAbrir(false), setOpenModalReabrirGuia(false)
            })
    }

    return (
        <>
            <DividerSectionCard>
                <S.Header style={{ marginBottom: '24px' }}>
                    <S.PrestadorInfoWrapper>
                        <S.TitleWrapper>
                            <h3>Guia recurso de glosa {resumoLoteGuia?.numeroGuia}</h3>
                        </S.TitleWrapper>
                    </S.PrestadorInfoWrapper>

                    {resumoLoteGuia?.reaberturaHabilitada ? (
                        <Button
                            iconLeft="/faturamento/assets/icons/ic-open-cadeado.svg"
                            disabled={false}
                            style={{ width: 'fit-content', backgroundColor: 'rgba(43, 69, 212, 0.04)', color: '#2B45D4' }}
                            onClick={() => setOpenModalReabrirGuia(true)}
                        >
                            Reabrir
                        </Button>
                    ) : (
                        <S.BtnHeaderWrapper>
                            <Button
                                iconLeft="/faturamento/assets/icons/ic-like.svg"
                                style={{ width: 'fit-content', backgroundColor: '#EEF3F2', color: '#38B449' }}
                                onClick={() =>
                                    setIsOpen({
                                        ...isOpen,
                                        openModalDeferir: true
                                    })
                                }
                            >
                                Deferir
                            </Button>
                            <Button
                                iconLeft="/faturamento/assets/icons/ic-dislike.svg"
                                style={{ width: 'fit-content', backgroundColor: 'rgba(244, 67, 54, 0.04)', color: '#F44336' }}
                                onClick={() =>
                                    setIsOpen({
                                        ...isOpen,
                                        openModalIndeferir: true
                                    })
                                }
                            >
                                Indeferir
                            </Button>
                            <Button
                                iconLeft="/faturamento/assets/icons/check-ic.svg"
                                disabled={!resumoLoteGuia?.finalizacaoHabilitada}
                                themeButton="warning"
                                style={{ width: 'fit-content' }}
                                onClick={() =>
                                    setIsOpen({
                                        ...isOpen,
                                        openModalFinalizar: true
                                    })
                                }
                            >
                                Finalizar
                            </Button>
                        </S.BtnHeaderWrapper>
                    )}
                </S.Header>
                <S.Header>
                    <S.ContentData>
                        <ContentDataInfo
                            label="Mês de competência"
                            value={
                                DateUtils.getMonthFullName(resumoLoteGuia?.competencia) +
                                    ' ' +
                                    new Date(resumoLoteGuia?.competencia).toLocaleDateString('pt-BR', {
                                        year: 'numeric'
                                    }) || '--'
                            }
                            background={'grey'}
                            standart={'expanded'}
                        />
                        <ContentDataInfo
                            label="Tipo de guia"
                            value={TipoGuiaEnum[resumoLoteGuia?.tipo] || '--'}
                            background={'grey'}
                            standart={'expanded'}
                        />
                        {/* <ContentDataInfo
                            label="Guia de origem"
                            value={resumoLoteGuia?.numeroGuiaOrigem || '--'}
                            background={'grey'}
                            standart={'expanded'}
                        /> */}

                        <S.BoxGrey background={'grey'} standart={'expanded'}>
                            <label>Guia de origem</label>
                            {/* <p onClick={() => router.push(`/processamento-contas/analise-contas/resumo-guia/${resumoLoteGuia?.guiaOrigemId}`)}>
                                {resumoLoteGuia?.numeroGuiaOrigem || '--'}
                            </p> */}

                            <a
                                href={`/faturamento/processamento-contas/analise-contas/resumo-guia/${resumoLoteGuia?.guiaOrigemId}`}
                                target="_blank"
                                rel="noreferrer"
                            >
                                {resumoLoteGuia?.numeroGuiaOrigem || '--'}
                            </a>
                        </S.BoxGrey>
                    </S.ContentData>
                </S.Header>

                <S.Header>
                    <S.ContentData>
                        <ContentDataInfo
                            label="Apresentado"
                            value={NumberUtils.maskMoney(resumoLoteGuia?.valorApresentadoGuiaOrigem)}
                            background={'grey'}
                            standart={'expanded'}
                        />
                        <ContentDataInfo
                            label="Apurado"
                            value={NumberUtils.maskMoney(resumoLoteGuia?.valorApuradoGuiaOrigem)}
                            background={'grey'}
                            standart={'expanded'}
                        />
                        <ContentDataInfo
                            label="Glosado"
                            value={NumberUtils.maskMoney(resumoLoteGuia?.valorGlosadoGuiaOrigem)}
                            background={'grey'}
                            standart={'expanded'}
                        />
                        <ContentDataInfo
                            label="Recursado"
                            value={NumberUtils.maskMoney(resumoLoteGuia?.valorRecursado)}
                            background={'grey'}
                            standart={'expanded'}
                        />
                        <ContentDataInfo
                            label="Deferido"
                            value={NumberUtils.maskMoney(resumoLoteGuia?.valorDeferido)}
                            background={'grey'}
                            standart={'expanded'}
                        />
                    </S.ContentData>
                </S.Header>
            </DividerSectionCard>

            <DividerSectionCard dividerContent={true}>
                <S.StepsMenu hasScroll={dataContent?.length >= 8}>
                    {dataContent?.map((item, index) => (
                        <>
                            {item?.quantidade === 0 ? (
                                <></>
                            ) : (
                                <li
                                    key={index}
                                    onClick={() => {
                                        setStep(index === 0 ? 0 : 1)
                                        setSelectedIndex(index)
                                        setTabName(item?.tipo)
                                    }}
                                    className={selectedIndex === index ? 'active' : ''}
                                >
                                    {EnumTipo[item?.tipo]}

                                    {item?.quantidade && (
                                        <S.StepQtd>
                                            <p>{item?.quantidade}</p>
                                        </S.StepQtd>
                                    )}
                                </li>
                            )}
                        </>
                    ))}
                </S.StepsMenu>

                <hr />

                {step === 0 && <GeneralInformation />}
                {step === 1 && <GuideInformationTable tabName={tabName} uuid={uuid} setRefresh={setRefresh} refresh={refresh} />}
            </DividerSectionCard>

            {/* MODAIS */}

            {/* MODAL INDEFERIR GUIA */}
            <ModalIndeferirGuia
                uuid={uuid}
                openModal={isOpen?.openModalIndeferir}
                setCloseModal={() =>
                    setIsOpen({
                        ...isOpen,
                        openModalIndeferir: false
                    })
                }
                handleIndeferir={() => {
                    getResumoGuia()
                    setRefresh(!refresh)
                    setIsOpen({
                        ...isOpen,
                        openModalIndeferir: false
                    })
                }}
                guia={resumoLoteGuia}
            />

            <Modal
                isOpen={isOpen?.openModalFinalizar}
                onClose={() =>
                    setIsOpen({
                        ...isOpen,
                        openModalFinalizar: false
                    })
                }
                style={{ padding: 0 }}
            >
                <S.WrapperModal>
                    <h1>Finalizar analise da Guia {resumoLoteGuia?.numeroGuia}?</h1>
                    <S.WrapperButtons>
                        <Button
                            themeButton="gray"
                            typeButton="text"
                            onClick={() =>
                                setIsOpen({
                                    ...isOpen,
                                    openModalFinalizar: false
                                })
                            }
                        >
                            Cancelar
                        </Button>
                        <Button themeButton="secondary" onClick={handleClickToFinish} disabled={false}>
                            Finalizar
                        </Button>
                    </S.WrapperButtons>
                </S.WrapperModal>
            </Modal>

            {/* DEFERIR */}

            <Modal
                isOpen={isOpen?.openModalDeferir}
                onClose={() =>
                    setIsOpen({
                        ...isOpen,
                        openModalDeferir: false
                    })
                }
                style={{ padding: 0 }}
            >
                <S.WrapperModal>
                    <h1>Deferir a guia ?</h1>
                    <p>
                        Essa ação irá deferir o valor recursado de todos os itens pelo prestador. Quaisquer status ou dados informados anteriormente
                        pela análise serão sobrescrita.
                    </p>

                    <S.WrapperButtons>
                        <Button
                            themeButton="gray"
                            typeButton="text"
                            onClick={() =>
                                setIsOpen({
                                    ...isOpen,
                                    openModalDeferir: false
                                })
                            }
                        >
                            Cancelar
                        </Button>
                        <Button themeButton="success" onClick={handleClickToDeferir} disabled={false}>
                            Deferir
                        </Button>
                    </S.WrapperButtons>
                </S.WrapperModal>
            </Modal>

            {/* MODAL REABRIR GUIA */}
            <Modal isOpen={openModalReabrirGuia} onClose={() => setOpenModalReabrirGuia(false)} style={{ padding: 0 }}>
                <S.WrapperModal>
                    <h1>Reabrir a guia {resumoLoteGuia?.numeroGuia} ?</h1>
                    <p>Ao reabrir a guia, será possível alterar novamente.</p>

                    <S.WrapperButtons>
                        <Button themeButton="gray" typeButton="text" onClick={() => setOpenModalReabrirGuia(false)}>
                            Cancelar
                        </Button>
                        <Button themeButton="secondary" onClick={handleReabrirGuia} disabled={loadingFecharAbrir}>
                            {loadingFecharAbrir ? 'Reabrindo..' : 'Reabrir'}
                        </Button>
                    </S.WrapperButtons>
                </S.WrapperModal>
            </Modal>
        </>
    )
}

export default ResumoGuiaTemplate
