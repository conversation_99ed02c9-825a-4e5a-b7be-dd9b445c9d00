import { useEffect, useState } from 'react'
import { ReactSVG } from 'react-svg'
import Button from 'components/atoms/Button'
import NavTabs, { Tab } from 'components/molecules/NavTabs'
import ItensGuia from './ItensGuia'
import ResumoGuias from './ResumoGuia'
import Modal from 'components/atoms/Modal'
import InputDate from 'components/molecules/InputDate'
import SimpleSelect from 'components/molecules/SimpleSelect'
import moment from 'moment'
import { useToast } from 'src/hooks/toast'
import { GuiaLoteManualService } from 'src/services/faturamentoPrestadorApi/lote-manual-guia'
import { ProcessoBeneficiarioEnum, TipoLoteManualGuiaEnum } from 'src/services/faturamentoPrestadorApi/lote-manual-guia/enuns'
import { IGuiaLoteManualDTO, IPeriodoCobrancaPayload } from 'src/services/faturamentoPrestadorApi/lote-manual-guia/types'
import { resetObjectData } from 'utils/functions'
import { getMessageErrorFromApiResponse } from 'utils/stringUtils'
import * as S from './styles'
import { StatusCobrancaEnum } from 'src/services/faturamentoPrestadorApi/lote-manual/enuns'
import { PrestadorService } from 'src/services/faturamentoOperadoraApi/prestadorServices'
import { IPrestadorDTO } from 'types/prestador'
import { Me } from 'src/services/gestaoUsuarios/me'
import { ParseTipoGuiaEnum } from 'utils/enum/parse-enum'

type Modal = {
    etapaCumprida: boolean
    etapaNaoCumprida: boolean
    detalhesCobranca: boolean
}

type ResumoGuiaLoteProps = {
    idGuia: string
}

export interface IFieldValues {
    dataFinalFaturamento: Date | string
    dataInicioFaturamento: Date | string
    tipoFaturamento: IOptions
}

export interface IOptions {
    label: string
    value: 'PARCIAL' | 'FINAL' | 'COMPLEMENTAR' | 'TOTAL'
}

function ResumoGuiaLoteTemplate({ idGuia }: ResumoGuiaLoteProps) {
    const [tabs, setTabs] = useState<Tab[]>()
    const [isOpen, setIsOpen] = useState<Modal>()
    const [guideData, setGuideData] = useState<IGuiaLoteManualDTO>()
    const [fieldValues, setFieldValues] = useState<IFieldValues>()
    const [refreshModal, setRefreshModal] = useState<boolean>(false)
    const [btnDisable, setBtnDisable] = useState<boolean>(true)
    const { addToast } = useToast()
    const [statusCobranca, setStatusCobranca] = useState<StatusCobrancaEnum>()
    const [dadosPrestador, setDadosPrestador] = useState<IPrestadorDTO>()

    const getGuiaLoteManual = () => {
        GuiaLoteManualService?.getByID(idGuia)
            .then(({ data }) => {
                setGuideData(data)
                setStatusCobranca(data?.statusCobranca)
            })
            .catch((error) => {
                console.error(error)
            })
    }

    function handleClickToCobrarGuia() {
        GuiaLoteManualService?.patchCobrarGuia(idGuia)
            .then(() => {
                addToast({
                    type: 'success',
                    title: 'Cobrado com sucesso!',
                    duration: 5000
                })
            })
            .catch((err) => {
                addToast({
                    type: 'error',
                    title: 'Erro ao tentar cobrar o item',
                    duration: 5000,
                    description: getMessageErrorFromApiResponse(err)
                })
            })
            .finally(() => {
                setFieldValues(resetObjectData(fieldValues))
                setIsOpen({ ...isOpen, etapaCumprida: false })
                setRefreshModal(!refreshModal)
            })
    }

    const getContexts = async () => {
        const { data }: any = await Me.get()
        PrestadorService.getPrestadorVinculado(data?.id)
            .then(({ data }) => {
                setDadosPrestador(data)
            })
            .catch((err) => {
                console.log(err)
            })
    }

    function handleClickToPeriodoCobranca() {
        const payload: IPeriodoCobrancaPayload = {
            dataFinalFaturamento: moment(fieldValues?.dataFinalFaturamento).format('YYYY-MM-DD') + 'T' + '00:00',
            dataInicioFaturamento: moment(fieldValues?.dataInicioFaturamento).format('YYYY-MM-DD') + 'T' + '00:00',
            tipoFaturamento: fieldValues?.tipoFaturamento?.value
        }

        GuiaLoteManualService?.patchPeriodoCobranca(payload, idGuia)
            .then(() => {
                // addToast({
                //     type: 'success',
                //     title: 'Cobrado com sucesso!',
                //     duration: 4000
                // })

                handleClickToCobrarGuia()
            })
            .catch((err) => {
                addToast({
                    type: 'error',
                    title: 'Erro ao tentar cobrar o item',
                    duration: 5000,
                    description: getMessageErrorFromApiResponse(err)
                })
            })
            .finally(() => {
                setFieldValues(resetObjectData(fieldValues))
                setIsOpen({ ...isOpen, etapaCumprida: false })
                setRefreshModal(!refreshModal)
            })
    }

    // VERIFICA SE OS CAMPOS DE HORA ESTÃO PREENCHIDOS

    function isEmpty(fieldValues: IFieldValues) {
        if (guideData?.tipo === TipoLoteManualGuiaEnum.RESUMO_INTERNACAO || guideData?.tipo === TipoLoteManualGuiaEnum.HONORARIOS) {
            if (guideData?.tipo === TipoLoteManualGuiaEnum.RESUMO_INTERNACAO) {
                return fieldValues?.dataFinalFaturamento && fieldValues?.dataInicioFaturamento && fieldValues?.tipoFaturamento
            } else {
                return fieldValues?.dataFinalFaturamento && fieldValues?.dataInicioFaturamento
            }
        } else {
            return true
        }
    }

    // VERIFICA TIPO DE GUIA E CHAMA O RESPECTIVO PATCH

    function handleClickToFinishBtn() {
        if (guideData?.tipo === TipoLoteManualGuiaEnum.RESUMO_INTERNACAO || guideData?.tipo === TipoLoteManualGuiaEnum.HONORARIOS) {
            handleClickToPeriodoCobranca()
        } else {
            handleClickToCobrarGuia()
        }
    }

    useEffect(() => {
        const empty = isEmpty(fieldValues)
        setBtnDisable(!empty)
    }, [fieldValues, guideData])

    useEffect(() => {
        getGuiaLoteManual()
        getContexts()
    }, [])

    useEffect(() => {
        if (idGuia && dadosPrestador) {
            setTabs([
                {
                    label: 'Resumo',
                    component: <ResumoGuias idGuia={idGuia} guideData={guideData} dadosPrestador={dadosPrestador} />
                },
                {
                    label: 'Itens',
                    component: (
                        <ItensGuia idGuia={idGuia} guideData={guideData} setStatusCobranca={setStatusCobranca} dadosPrestador={dadosPrestador} />
                    )
                }
            ])
        }
        console.log(guideData)
    }, [idGuia, guideData, refreshModal, dadosPrestador])

    return (
        <S.Container>
            <S.Header>
                <S.FlexContent>
                    <S.Title>
                        Guia de {ParseTipoGuiaEnum[guideData?.tipo]} - {guideData?.numeroGuiaPrestador || '-'}
                    </S.Title>

                    <S.BadgeTop>
                        {statusCobranca === StatusCobrancaEnum.AGUARDANDO_ANALISE && (
                            <S.Badge type="waiting">
                                <p>Aguardando análise</p>
                            </S.Badge>
                        )}

                        {statusCobranca === StatusCobrancaEnum.ANALISADA && (
                            <S.Badge type="ready">
                                <p>Analisada</p>
                            </S.Badge>
                        )}
                    </S.BadgeTop>
                </S.FlexContent>

                <p className="paragraph">Visualize, edite e atualize as informações necessárias para realizar a cobrança desta guia.</p>
                {/* <Button themeButton="secondary" onClick={() => setIsOpen({ ...isOpen, etapaCumprida: true })}>
                    Finalizar cobrança
                </Button> */}
            </S.Header>

            <S.BadgesWrapper>
                {/* {guideData?.tipo && (
                    <S.Badge type="primary">
                        <ReactSVG src="/faturamento/assets/icons/mai-ic-form.mono.svg" />
                        <p>{guideData?.tipo}</p>
                    </S.Badge>
                )} */}
                {guideData?.processoBeneficiario === ProcessoBeneficiarioEnum.LIMINAR && (
                    <S.Badge type="secondary">
                        <ReactSVG src="/faturamento/assets/icons/mai-ic-lawscale.mono.svg" />
                        <p>Liminar judicial</p>
                    </S.Badge>
                )}
                {guideData?.processoBeneficiario === ProcessoBeneficiarioEnum.EXCEPCIONALIDADE && (
                    <S.Badge type="error">
                        <ReactSVG src="/faturamento/assets/icons/excepcionalidade.svg" />
                        <p>Excepcionalidade</p>
                    </S.Badge>
                )}
            </S.BadgesWrapper>

            <NavTabs tabs={tabs} />

            {/* Modal Etapa Nao Cumprida */}
            {/* <Modal
                onClose={() => setIsOpen({ ...isOpen, etapaNaoCumprida: false })}
                isOpen={isOpen?.etapaNaoCumprida}
                style={{ padding: '24px', width: '783px' }}
            >
                <S.WrapperModal>
                    <h1>Antes de finalizar, preencha todas informações</h1>
                    <div>
                        <p>Você ainda precisa preencher algumas informações para finalizar a cobrança da guia:</p>
                        <ul>
                            <li>Selecione e especifique os itens a serem enviados para cobrança</li>
                            <li>Adicione a conta da guia e outros documentos relevantes para o faturamento.</li>
                        </ul>
                    </div>

                    <S.WrapperButtonsModal>
                        <Button themeButton="secondary" onClick={() => setIsOpen({ ...isOpen, etapaNaoCumprida: false })}>
                            Entendi
                        </Button>
                    </S.WrapperButtonsModal>
                </S.WrapperModal>
            </Modal> */}

            {/* Modal Etapa Cumprida */}
            {/* <Modal
                onClose={() => setIsOpen({ ...isOpen, etapaCumprida: false })}
                isOpen={isOpen?.etapaCumprida}
                style={{ padding: '24px', width: '783px' }}
            >
                <S.WrapperModal key={refreshModal as any}>
                    <h1>{`Finalizar a cobrança da guia ${guideData?.numeroGuiaOperadora}`}?</h1>

                    {(guideData?.tipo === TipoLoteManualGuiaEnum.RESUMO_INTERNACAO || guideData?.tipo === TipoLoteManualGuiaEnum.HONORARIOS) && (
                        <p>
                            Antes de finalizar, insira o período de cobrança da guia. Lembre-se que para preencher o período de cobrança de guias com
                            uma única data de atendimento, basta repetir a data nos dois campos disponíveis.
                        </p>
                    )}

                    {guideData?.tipo === TipoLoteManualGuiaEnum.RESUMO_INTERNACAO && (
                        <SimpleSelect
                            options={[
                                { label: 'PARCIAL', value: 'PARCIAL' },
                                { label: 'FINAL', value: 'FINAL' },
                                { label: 'COMPLEMENTAR', value: 'COMPLEMENTAR' },
                                { label: 'TOTAL', value: 'TOTAL' }
                            ]}
                            onChange={(event) => {
                                setFieldValues({
                                    ...fieldValues,
                                    tipoFaturamento: event
                                })
                            }}
                            isClearable
                            defaultValue={''}
                            required
                            label="Tipo de faturamento"
                        />
                    )}

                    {(guideData?.tipo === TipoLoteManualGuiaEnum.RESUMO_INTERNACAO || guideData?.tipo === TipoLoteManualGuiaEnum.HONORARIOS) && (
                        <S.WrapperInputs>
                            <InputDate
                                minDate={moment().add(-10, 'year').toDate()}
                                value={
                                    fieldValues?.dataInicioFaturamento !== undefined && fieldValues?.dataInicioFaturamento !== null
                                        ? new Date(fieldValues?.dataInicioFaturamento)
                                        : null
                                }
                                label="Período de cobrança - Inicial"
                                required={true}
                                placeholder="00/00/00"
                                onChange={(date: Date) => {
                                    setFieldValues({
                                        ...fieldValues,
                                        dataInicioFaturamento: date?.toString()
                                    })
                                }}
                                key={'input-date-1'}
                            />

                            <InputDate
                                value={
                                    fieldValues?.dataFinalFaturamento !== undefined && fieldValues?.dataFinalFaturamento !== null
                                        ? new Date(fieldValues?.dataFinalFaturamento)
                                        : null
                                }
                                required={true}
                                label="Período de cobranca - Final"
                                minDate={moment().add(-1, 'month').toDate()}
                                placeholder="00/00/00"
                                onChange={(date: Date) => {
                                    // handleFieldsChange(
                                    //     setFieldValues,
                                    //     'dataFinalFaturamento',
                                    //     date !== null && date !== undefined ? DateUtils?.converterData(date) : null
                                    // )

                                    setFieldValues({
                                        ...fieldValues,
                                        dataFinalFaturamento: date?.toString()
                                    })
                                }}
                                key={'input-date-2'}
                            />
                        </S.WrapperInputs>
                    )}

                    <S.WrapperButtonsModal>
                        <Button typeButton="text" themeButton="gray" onClick={() => setIsOpen({ ...isOpen, etapaCumprida: false })}>
                            Cancelar
                        </Button>
                        <Button themeButton="secondary" onClick={() => handleClickToFinishBtn()} disabled={btnDisable}>
                            Finalizar cobrança
                        </Button>
                    </S.WrapperButtonsModal>
                </S.WrapperModal>
            </Modal> */}
        </S.Container>
    )
}

export default ResumoGuiaLoteTemplate
