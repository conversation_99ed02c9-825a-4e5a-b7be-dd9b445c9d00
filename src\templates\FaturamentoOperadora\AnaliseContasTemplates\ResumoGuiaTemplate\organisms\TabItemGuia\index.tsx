import React, { useMemo } from 'react'
import NoContent from 'components/molecules/NoContent'
import CardItemGuia from '../CardItemGuia'
import { IItemGuiaAgrupadoDTO, IItemGuiaDTO } from 'types/analiseContas/guiasLote'
import { IGuiaInfoGeraisQuery } from 'types/analiseContas/guia'

const TabItemGuia = ({
    infoGeraisGuia,
    itensGuia,
    permission,
    setItensGuia
}: {
    infoGeraisGuia: IGuiaInfoGeraisQuery
    itensGuia: IItemGuiaAgrupadoDTO[]
    setItensGuia: React.Dispatch<React.SetStateAction<IItemGuiaDTO>>
    permission?: boolean
}) => {
    const itens = useMemo(() => itensGuia || [], [itensGuia])

    return (
        <div className="container-guide" style={{ paddingTop: 12 }}>
            {/* <S.HeaderGuide> */}
            {/* <SearchBar
                    value={filtro}
                    handleOnSearch={console.log}
                    handleOnClose={console.log}
                    handleOnChange={(e) => setFiltro(e?.target?.value)}
                    placeholder="Procurar itens por código ou descrição"
                /> */}
            {/* {pageData?.content?.length === 0 ? (
                    <>
                        <S.Page>
                            <span className="pageNumber">
                                {pagination?.paginaAtual * pagination?.linhasPorPagina + 1} -{' '}
                                {pageData?.content?.length + pagination?.paginaAtual * pagination?.linhasPorPagina}
                                {' de '}
                                {pagination?.totalRegistros}
                            </span>
                            <ReactSVG src="/faturamento/assets/icons/dots.svg" />
                        </S.Page>
                    </>
                ) : null} */}
            {/* </S.HeaderGuide> */}

            {/* 2104 - CardItemGuia */}

            {itens?.length === 0 ? (
                <div style={{ marginTop: '16px' }}>
                    <NoContent title="Nenhum item foi encontrado." />
                </div>
            ) : (
                itens?.map((item, index: number) => (
                    <CardItemGuia
                        infoGeraisGuia={infoGeraisGuia}
                        key={index}
                        setItensGuia={setItensGuia}
                        item={item}
                        index={index}
                        permission={permission}
                    />
                ))
            )}

            {/* {itens.length > 0 && (
                <Pagination
                    totalPage={pagination?.totalPaginas}
                    totalRegister={pagination?.totalRegistros}
                    actualPage={pagination?.paginaAtual}
                    setNumberPage={pagination?.setNumberPage}
                />
            )} */}
        </div>
    )
}

export default TabItemGuia
