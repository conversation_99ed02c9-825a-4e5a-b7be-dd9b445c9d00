import CardSubMenu from 'components/molecules/CardSubMenu'
import * as S from './styles'
import BeneficiarySearch from 'components/molecules/BeneficiarySearch'
import SelectBeneficiary from 'components/organisms/SelectBeneficiary'
import { useEffect, useState } from 'react'
import { beneficiario } from 'utils/LinksApps'
import { Beneficiary } from 'types/faturamentoOperadora/beneficiary'
import { Relatorio } from 'src/services/relatorio'
import { downloadFileV2 } from 'utils/download'
import { useToast } from 'src/hooks/toast'
import { getMessageErrorFromApiResponse } from 'utils/stringUtils'
import TitleSection from 'components/atoms/TitleSection'

const RelatorioFaturamentoTemplates = () => {
    const [cpf, setCpf] = useState(null)
    const { addToast } = useToast()
    const [isDisabled, setIsDisabled] = useState(false)

    function handleClickToDownloadRelatorio(beneficiaryData: Beneficiary) {
        setIsDisabled(true)

        if (beneficiaryData) {
            Relatorio.getRelatorio(beneficiaryData?.id)
                .then((response) => {
                    downloadFileV2(response?.data, 'relatorio.xlsx')
                    setIsDisabled(false)
                })
                .catch((err) => {
                    setIsDisabled(false)
                    addToast({
                        title: 'Ocorreu um erro ao tentar fazer o download',
                        description: '',
                        type: 'error',
                        duration: 3000
                    })
                })
        }
    }

    return (
        <S.Container>
            <BeneficiarySearch
                label="Procure pelo CPF ou número do cartão do beneficiário"
                value={cpf}
                setValue={setCpf}
                onClick={(recipient: Beneficiary) => {
                    !isDisabled && handleClickToDownloadRelatorio(recipient)
                }}
            />
        </S.Container>
    )
}

export default RelatorioFaturamentoTemplates
