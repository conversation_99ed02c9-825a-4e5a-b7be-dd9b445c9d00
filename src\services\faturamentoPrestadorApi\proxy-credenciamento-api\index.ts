import { AxiosResponse } from 'axios'
import { apiFaturamentoPrestador } from 'src/services/apis/apiFaturamentoPrestador'
import { ObjectUtils } from 'utils/objectUtils'
import { IEnderecoCredenciamentoResponse, IGetPrestadorProps, IPrecificarItemDTO, IPrecificarItemProps, IPrestadorPageable } from './types'

const baseUrl = '/proxy/credenciamento'

const dataMock = {
    content: [
        {
            codigoConselho: '1',
            cpf: 'string',
            email: 'string',
            nome: 'Marcos 1',
            numeroDoConselho: 'string',
            siglaEstadoConselho: 'string',
            termoConselho: 'string',
            uuid: '3fa85f64-5717-4562-b3fc-2c963f66afa6'
        },
        {
            codigoConselho: '2',
            cpf: 'string',
            email: 'string',
            nome: 'Carlos 2',
            numeroDoConselho: 'string',
            siglaEstadoConselho: 'string',
            termoConselho: 'string',
            uuid: '3fa85f64-5717-4562-b3fc-2c963f66afa6'
        },
        {
            codigoConselho: '3',
            cpf: 'string',
            email: 'string',
            nome: 'Rogerio 3',
            numeroDoConselho: 'string',
            siglaEstadoConselho: 'string',
            termoConselho: 'string',
            uuid: '3fa85f64-5717-4562-b3fc-2c963f66afa6'
        }
    ]
}

const dataValue = {
    honorario: 22,
    porteAnestesico: 33,
    valor: 44,
    valorFilme: 55,
    valorPorteAnestesico: 66,
    valorUco: 77
}

export class PrestadorProxyService {
    static async getPrestador(props?: IGetPrestadorProps): Promise<AxiosResponse<IPrestadorPageable>> {
        const params = ObjectUtils.propsToParams(props)
        const url = props ? `${baseUrl}/profissional/resumo?${params}` : baseUrl
        return apiFaturamentoPrestador.get(url)
    }

    static async getPrestadorEndereco(prestadorId: string) {
        const url = `${baseUrl}/prestador/${prestadorId}/endereco`
        return apiFaturamentoPrestador.get<IEnderecoCredenciamentoResponse>(url)
    }

    static async getPrecificarItem(props?: IPrecificarItemProps): Promise<AxiosResponse<IPrecificarItemDTO>> {
        const params = ObjectUtils.propsToParams(props)
        const url = props ? `${baseUrl}/precificar/item?${params}` : baseUrl
        return apiFaturamentoPrestador.get(url)

        // const response: AxiosResponse = {
        //     data: dataValue,
        //     status: 200,
        //     statusText: 'ok',
        //     headers: null,
        //     config: null
        // }

        // return Promise.resolve(response)
    }
}
