import SectionContainer from 'components/atoms/SectionContainer'
import { useState, useEffect } from 'react'
import { ReactSVG } from 'react-svg'
import { IPagination } from 'types/common/pagination'
import { AprovacaoMock } from './mock'
import * as S from './styles'
import Button from 'components/atoms/Button'
import Modal from 'components/atoms/Modal'
import { INotaFiscalDTO } from 'types/faturamentoOperadora/notaFiscal'
import { DemonstrativoServices } from 'src/services/faturamentoPrestadorApi/demostrativoServices'
import TiposNotaFiscais, { SitucaoNotaFiscais } from 'src/enum/tipo-nota-fiscal.enum'
import moment from 'moment'
import AttachmentViewModal from '../../ModaisDemonstrativos/AttachmentViewModal'
import { useToast } from 'src/hooks/toast'
import { getMessageErrorFromApiResponse } from 'utils/stringUtils'
import { ContasAPagar } from 'src/services/faturamentoPrestadorApi/contas-apagar-controller'

type NotaFicalProps = {
    demonstrativoUUID: string
}

const NotaFiscal = ({ demonstrativoUUID }: NotaFicalProps) => {
    // const [selectedValue, setSelectedValue] = useState<string>()
    // const [pagination, setPagination] = useState<IPagination>()
    // const [aprovacaoNotaFiscal, setaProvacaoNotaFiscal] = useState<any[]>(AprovacaoMock)
    const [modalCancelarNota, setModalCancelarNota] = useState(false)
    const [fileModal, setFileModal] = useState(false)
    const [refresh, setRefresh] = useState(false)
    const { addToast } = useToast()
    const [fileUrl, setFileUrl] = useState<string>()

    const [notaFiscal, setNotaFiscal] = useState<INotaFiscalDTO>()

    const handleCancelar = () => {
        ContasAPagar.cancelaNotaFiscal({ notaFiscalId: notaFiscal.uuid })
            .then(() => {
                setNotaFiscal({ ...notaFiscal, situacao: 'CANCELADA ' })
                setModalCancelarNota((state) => !state)
                setRefresh(!refresh)
                addToast({
                    type: 'success',
                    title: 'Nota fiscal cancelada com sucesso!'
                })
            })
            .catch((err) => {
                addToast({
                    type: 'error',
                    title: 'Erro ao tentar enviar o documento',
                    description: getMessageErrorFromApiResponse(err)
                })
            })
    }

    function createUrlDisplay(anexoUuid?: string) {
        // if (anexoUuid) {
        //     Anexos.display(anexoUuid)
        //         .then((response) => {
        //             const url = window.URL.createObjectURL(
        //                 new Blob([response.data], {
        //                     type: response?.headers['content-type']
        //                 })
        //             )

        //             setFileUrl(url)
        //         })
        //         .catch((err) => {
        //             addToast({
        //                 title: err?.response?.data?.message ? err?.response?.data?.message : 'Ocorreu um erro: Desconhecido',
        //                 type: 'error',
        //                 duration: 5000
        //             })
        //         })
        // } else {
        ContasAPagar.getDisplayById(notaFiscal?.uuid)
            .then((response) => {
                const url = window.URL.createObjectURL(
                    new Blob([response.data], {
                        type: response?.headers['content-type']
                    })
                )
                console.log(response?.headers)

                setFileUrl(url)
            })
            .catch((err) => {
                addToast({
                    title: err?.response?.data?.message ? err?.response?.data?.message : 'Ocorreu um erro: Desconhecido',
                    type: 'error',
                    duration: 5000
                })
            })
        // }
    }

    useEffect(() => {
        ContasAPagar.getNotaFiscalUltima({ demonstrativoUUID: demonstrativoUUID }).then(({ data }) => {
            setNotaFiscal(data)
        })
    }, [demonstrativoUUID, refresh])

    return (
        <S.NotaFiscalContainer>
            <SectionContainer title="Contas">
                <S.Row0>
                    <S.CardInfo>
                        <h3>Número da nota fiscal</h3>
                        <p className="align_left">{notaFiscal?.numeroNF ?? '-'}</p>
                    </S.CardInfo>
                    <S.CardInfo>
                        <h3>Tipo da nota fiscal</h3>
                        <p className="align_left">{TiposNotaFiscais[notaFiscal?.tipoNF] ?? '-'}</p>
                    </S.CardInfo>
                    <S.CardInfo>
                        <h3>Data de emissão da nota fiscal</h3>
                        <p className="align_left">{notaFiscal?.dataEmissaoNF ? moment(notaFiscal?.dataEmissaoNF).format('DD/MM/YYYY') : '-'}</p>
                    </S.CardInfo>
                    <S.CardInfo>
                        <h3>Data de envio da nota fiscal</h3>
                        <p className="align_left">{notaFiscal?.dataEnvioNF ? moment(notaFiscal?.dataEnvioNF).format('DD/MM/YYYY') : '-'}</p>
                    </S.CardInfo>
                    <S.CardInfo>
                        <h3>Data de aprovação de nota fiscal</h3>
                        <p className="align_left">{notaFiscal?.dataAnalise ? moment(notaFiscal?.dataAnalise).format('DD/MM/YYYY') : '-'}</p>
                    </S.CardInfo>
                </S.Row0>
                <S.Row0>
                    <S.CardInfo style={{ width: '25%' }}>
                        <h3>Situação da Nota Fiscal</h3>
                        <p className="align_left">{SitucaoNotaFiscais[notaFiscal?.situacao] ?? '-'}</p>
                    </S.CardInfo>
                    <S.CardInfo>
                        <h3>Motivo da reprovação (Se Reprovada)</h3>
                        <p className="align_left">{notaFiscal?.situacao === 'REPROVADA' ? notaFiscal?.motivoReprovacao : '-'}</p>
                    </S.CardInfo>
                    <S.CardInfo>
                        <h3>Tipo de faturamento</h3>
                        <p className="align_left">{notaFiscal?.tipoFaturamento ?? '-'}</p>
                    </S.CardInfo>
                </S.Row0>
                {notaFiscal && (
                    <S.ActionRow>
                        <Button
                            iconLeft={'/faturamento/assets/icons/listMany.svg'}
                            typeButton="text"
                            style={{ width: 'fit-content' }}
                            onClick={() => {
                                createUrlDisplay()
                                setFileModal(true)
                            }}
                        >
                            Nota fiscal
                        </Button>
                        {/* <Button
                        iconLeft={'/faturamento/assets/icons/listMany.svg'}
                        typeButton="text"
                        style={{ width: 'fit-content' }}
                        onClick={() => {
                            console.log('')
                        }}
                    >
                        Simples Nacional
                    </Button> */}
                        {notaFiscal?.situacao !== 'CANCELADA' && (
                            <Button
                                iconLeft={'/faturamento/assets/icons/closeX.svg'}
                                typeButton="text"
                                themeButton="danger"
                                style={{ width: 'fit-content' }}
                                onClick={() => {
                                    setModalCancelarNota((state) => !state)
                                }}
                            >
                                Cancelar nota
                            </Button>
                        )}
                    </S.ActionRow>
                )}
            </SectionContainer>
            {/* <div>
                <SectionContainer title="Bloqueios de aprovação da nota fiscal">
                    <S.ActionWrapper>
                        <S.RadioWrapper>
                            <FormControlLabel
                                control={
                                    <Radio
                                        checked={selectedValue === 'Bloqueado'}
                                        onChange={(e) => setSelectedValue(e.target.value)}
                                        value="Bloqueado"
                                        name="radio-buttons"
                                    />
                                }
                                label="Bloqueado"
                            />
                            <FormControlLabel
                                control={
                                    <Radio
                                        checked={selectedValue === 'Liberado'}
                                        onChange={(e) => setSelectedValue(e.target.value)}
                                        value="Liberado"
                                        name="radio-buttons"
                                    />
                                }
                                label="Liberado"
                            />
                        </S.RadioWrapper>
                        <S.ButtonWrapper>
                            <TextField fullWidth label="Motivo Bloqueio" />
                            <Button color="warning" startIcon={<ReactSVG src="/contas/assets/imgs/ok-black.svg" />}>
                                Salvar alterações
                            </Button>
                        </S.ButtonWrapper>
                    </S.ActionWrapper>
                </SectionContainer>
                <TablePagination
                    titles={titles}
                    pagination={pagination}
                    values={aprovacaoNotaFiscal?.map((item) => {
                        return {
                            ...item
                        }
                    })}
                    customGridStyles={'1fr 1fr 3fr 1fr'}
                />
            </div> */}
            <Modal isOpen={modalCancelarNota} onClose={() => setModalCancelarNota((state) => !state)}>
                <S.TitleModal>Cancelar a nota fiscal?</S.TitleModal>
                <S.TextContent>Ao cancelar a nota fiscal, será necessário que envie outra nota fiscal para prosseguir com o pagamento.</S.TextContent>
                <S.ModalActions>
                    <Button
                        themeButton="gray"
                        onClick={() => {
                            setModalCancelarNota((state) => !state)
                        }}
                        typeButton="text"
                        style={{ width: 'fit-content' }}
                    >
                        Não, cancelar
                    </Button>
                    <Button
                        themeButton="danger"
                        typeButton="flat"
                        style={{ width: 'fit-content' }}
                        onClick={() => {
                            handleCancelar()
                        }}
                    >
                        Cancelar nota
                    </Button>
                </S.ModalActions>
            </Modal>

            <AttachmentViewModal
                isModalOpen={fileModal}
                onCloseModel={() => setFileModal(false)}
                documentUrl={fileUrl}
                ambienteDisplay={'NOTA_FISCAL'}
            />
        </S.NotaFiscalContainer>
    )
}

export default NotaFiscal
