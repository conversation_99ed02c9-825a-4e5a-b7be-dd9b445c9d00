import React, { useEffect, useState, useCallback } from 'react'
import { ReactSVG } from 'react-svg'
import { useRouter } from 'next/router'
import Item from 'components/atoms/Item'
import Button from 'components/atoms/Button'
import SearchBar from 'components/molecules/SearchBar'
import TitleSection from 'components/atoms/TitleSection'
import ReturnHeader from 'components/molecules/ReturnHeader'
import ButtonFilter from 'components/molecules/ButtonFilter'
import ContentDataInfo from 'components/molecules/ShowDataConfirm'
import DividerSectionCard from 'components/atoms/DividerSectionCard'
import * as S from './styles'
import { IPagination } from 'types/common/pagination'
import { NumberUtils } from 'utils/numberUtils'
import { useToast } from 'src/hooks/toast'
import { lotesListagemHCOEFechadoMock, lotesTotalizacaoMock, prestadorResumoMock } from 'src/services/recurso-glosa-operadora/prestador/mock'
import TablePaginationRecursoOperadora from '../Components/TablePaginationRecurso'
import { PaginationHelper } from 'utils/helpers/paginationHelper'
import { RecursoGlosaOperadoraPrestador } from 'src/services/recurso-glosa-operadora/prestador'
import { prestadoresMock } from '../mock'
import NoContent from 'components/molecules/NoContent'
import { lotesListagemFechadoMock } from 'src/services/recurso-glosa-operadora/lote/mock'
import {
    IAnaliseRecursoLoteProps,
    IAnaliseRecursoLoteResponse,
    IQuantitativosPrestadorResponse,
    IResumoPrestador,
    SituacaoLoteEnum,
    SituacaoLoteType,
    TipoLoteEnum
} from 'types/analiseContas/recursoGlosa'
import { RecursoGlosaService } from 'src/services/analiseContasApi/recursoGlosaServices'
import { useDebounce } from 'utils/useDebounce'
import { Box, Modal } from '@mui/material'

const ResumoPrestadorTemplate = ({ uuid }) => {
    const { addToast } = useToast()
    const router = useRouter()
    const [status, setStatus] = useState<SituacaoLoteType>('EM_ANALISE')
    const [resumo, setResumo] = useState<IResumoPrestador>(null)
    const [quantitativos, setQuantitativos] = useState<IQuantitativosPrestadorResponse>(null)
    const [lotesTotalizacao, setLotesTotalizacao] = useState(lotesTotalizacaoMock)
    const [lotes, setLotes] = useState<IAnaliseRecursoLoteResponse[]>([])
    const [numberPage, setNumberPage] = useState<number>(0)
    const [filter, setFilter] = useState<string>('')
    const [searchBarValue, setSarchBarValue] = useState<string>('')
    const [pagination, setPagination] = useState<IPagination>()
    const [enableButton, setEnableButton] = useState<boolean>(true)
    const [refresh, setRefresh] = useState<boolean>(true)
    const [showModalFinalizarPrestador, setShowModalFinalizarPrestador] = useState<boolean>(false)

    const totalQuantitativos = quantitativos?.qtdCancelados + quantitativos?.qtdEmAnalise + quantitativos?.qtdFechados
    const todosEstaoFechados = quantitativos?.qtdFechados / totalQuantitativos == 1

    function handleFinalizarPrestador() {
        setEnableButton(false)
        RecursoGlosaService.finalizarPrestadorAnaliseRecurso(uuid)
            .then((error) => {
                addToast({
                    title: 'Guias finalizadas com sucesso',
                    type: 'success'
                })

                setRefresh(!refresh)
            })

            .catch(({ response }) => {
                addToast({
                    title: response?.data?.message,
                    type: 'error',
                    duration: 3000
                })
            })
            .finally(() => {
                setEnableButton(true)
                setShowModalFinalizarPrestador(false)
            })
    }

    // const debouncedValue = useDebounce<string>(filter, 500)

    const titles: {
        label: string
        value:
            | keyof {
                  id: number
                  numeroLote: string
                  numeroLoteOrigem: string
                  tipoLoteDescricao: string
                  qtdGuiasEmAnaliseTecnica: string
                  qtdGuiasEmAnaliseAdministrativa: string
                  valorRecursado: number
                  valorDeferido: number
                  valorIndeferido: number
                  ativo: boolean
              }
            | 'acao'
    }[] = [
        { label: 'Lotes', value: 'numeroLote' },
        { label: 'Lote de origem', value: 'numeroLoteOrigem' },
        { label: 'Tipo', value: 'tipoLoteDescricao' },
        { label: 'Guias em análise tec.', value: 'qtdGuiasEmAnaliseTecnica' },
        { label: 'Guias em análise adm.', value: 'qtdGuiasEmAnaliseAdministrativa' },
        { label: 'Recursado', value: 'valorRecursado' },
        { label: 'Indeferido', value: 'valorIndeferido' },
        { label: 'Deferido', value: 'valorDeferido' },
        { label: '', value: 'acao' }
    ]

    const titulosCancelados: {
        label: string
        value:
            | keyof {
                  id: number
                  numeroLote: string
                  tipoLoteDescricao: string
                  justificativaCancelamento: string
                  nomeUsuarioCancelamento: string
                  valorRecursado: number
                  ativo: boolean
              }
            | 'acao'
    }[] = [
        { label: 'Lotes', value: 'numeroLote' },
        { label: 'Tipo', value: 'tipoLoteDescricao' },
        { label: 'Justificativa', value: 'justificativaCancelamento' },
        { label: 'Usuário', value: 'nomeUsuarioCancelamento' },
        { label: 'Recursado', value: 'valorRecursado' },
        { label: '', value: 'acao' }
    ]

    const getResumoPrestador = (prestadorAnaliseRecursoId: string) => {
        RecursoGlosaService.getResumoDadosPrestador(prestadorAnaliseRecursoId).then(({ data }) => setResumo(data))
    }

    const getQuantitativosPrestador = (prestadorAnaliseRecursoId: string) => {
        RecursoGlosaService.getQuantitativosLotesPrestador(prestadorAnaliseRecursoId).then(({ data }) => setQuantitativos(data))
    }

    useEffect(() => {
        RecursoGlosaService.getLotes({
            prestadorAnaliseRecursoId: uuid,
            situacao: status,
            identificadorLote: searchBarValue ? searchBarValue : null,
            size: 10,
            page: numberPage
        })
            .then(({ data }) => {
                setLotes(data.content)
                setPagination(PaginationHelper.parserPagination(data, setNumberPage))
            })
            .catch(({ response }) => {
                addToast({
                    title: response?.data?.message,
                    type: 'error'
                })
            })
    }, [numberPage, status, searchBarValue])

    useEffect(() => {
        getResumoPrestador(uuid)
        getQuantitativosPrestador(uuid)
    }, [refresh])

    const handlePesquisar = () => {
        setSarchBarValue(filter)
        setNumberPage(0)
    }

    // const handleOnClosePesquisa = () => {
    //     setFilter('')
    //     carregarGuias(numberPage, '')
    // }

    return (
        <>
            <S.Header style={{ marginBottom: '24px' }}>
                <S.PrestadorInfoWrapper>
                    <h3>{resumo?.nome}</h3>
                    <p>CNPJ - {resumo?.cnpj}</p>
                </S.PrestadorInfoWrapper>

                <Button
                    disabled={!enableButton || !resumo?.finalizacaoHabilitada}
                    themeButton="warning"
                    style={{ width: 'fit-content' }}
                    onClick={() => setShowModalFinalizarPrestador(true)}
                >
                    Finalizar
                </Button>
            </S.Header>
            <DividerSectionCard>
                <TitleSection>Valores</TitleSection>
                <S.Header>
                    <S.ContentData>
                        <S.TuplaDataInfo
                            label="Recursado"
                            value={NumberUtils.maskMoney(resumo?.valorRecursado)}
                            background={'grey'}
                            standart={'expanded'}
                        />
                        <S.TuplaDataInfo
                            label="Deferido"
                            value={NumberUtils.maskMoney(resumo?.valorDeferido)}
                            background={'grey'}
                            standart={'expanded'}
                        />
                        <S.TuplaDataInfo
                            label="Indeferido"
                            value={NumberUtils.maskMoney(resumo?.valorIndeferido)}
                            background={'grey'}
                            standart={'expanded'}
                        />
                    </S.ContentData>
                </S.Header>
            </DividerSectionCard>
            <DividerSectionCard dividerContent={true}>
                <S.HeaderList>
                    <SearchBar
                        value={filter}
                        placeholder="Procurar por lote"
                        handleOnSearch={() => handlePesquisar()}
                        type="text"
                        handleOnClose={() => {
                            setFilter(null), setSarchBarValue(null)
                        }}
                        handleOnChange={(e) => {
                            if (e?.target?.value === '' || e?.target?.value === null) {
                                setFilter('')
                            }
                            setFilter(e.target.value)
                            setNumberPage(0)
                        }}
                    />

                    {/* <S.Page>
                        <span>1 - 5 de 10</span>
                        <ReactSVG src="/faturamento/assets/icons/dots.svg" />
                    </S.Page> */}
                </S.HeaderList>
                <S.Header style={{ marginTop: '24px' }}>
                    <ButtonFilter
                        filter={true}
                        setNumberPage={setNumberPage}
                        setStatus={setStatus}
                        data={[
                            {
                                name: 'Em análise',
                                value: `${quantitativos?.qtdEmAnalise} - (${
                                    quantitativos?.qtdEmAnalise ? Math.round((quantitativos?.qtdEmAnalise / totalQuantitativos) * 100) : 0
                                }%)`,
                                situacao: 'EM_ANALISE'
                            },
                            {
                                name: 'Fechados',
                                value: `${quantitativos?.qtdFechados} - (${
                                    quantitativos?.qtdFechados ? Math.round((quantitativos?.qtdFechados / totalQuantitativos) * 100) : 0
                                }%)`,
                                situacao: 'FECHADO'
                            },
                            {
                                name: 'Cancelados',
                                value: `${quantitativos?.qtdCancelados} - (${
                                    quantitativos?.qtdCancelados ? Math.round((quantitativos?.qtdCancelados / totalQuantitativos) * 100) : 0
                                }%)`,
                                situacao: 'CANCELADO'
                            }
                        ]}
                    />
                </S.Header>
                {lotes && lotes?.length > 0 ? (
                    <TablePaginationRecursoOperadora
                        titles={status !== 'CANCELADO' ? titles : titulosCancelados}
                        values={
                            status !== 'CANCELADO'
                                ? lotes?.map((item, index) => {
                                      return {
                                          ...item,
                                          qtdGuiasEmAnaliseAdministrativa: (
                                              <S.AlignCenter>{`${item?.qtdGuiasEmAnaliseAdministrativa} de ${item?.qtdGuias}`}</S.AlignCenter>
                                          ),
                                          qtdGuiasEmAnaliseTecnica: (
                                              <S.AlignCenter>{`${item?.qtdGuiasEmAnaliseTecnica} de ${item?.qtdGuias}`}</S.AlignCenter>
                                          ),
                                          valorRecursado: <S.AlignCenter>{NumberUtils.maskMoney(item?.valorRecursado)}</S.AlignCenter>,
                                          valorDeferido: <S.AlignCenter>{NumberUtils.maskMoney(item?.valorDeferido)}</S.AlignCenter>,
                                          valorIndeferido: <S.AlignCenter>{NumberUtils.maskMoney(item?.valorIndeferido)}</S.AlignCenter>,
                                          acao: (
                                              <S.WrapperAction
                                                  ativo={true}
                                                  //   style={item?.situacao === 'EM_ANALISE' ? { cursor: 'pointer' } : { cursor: 'not-allowed' }}
                                                  onClick={() => {
                                                      //FIXME:
                                                      //   if (item?.situacao === 'EM_ANALISE') {

                                                      //   }
                                                      router.push(`/processamento-contas/recurso-glosa/prestador/lotes/${item?.loteRecursoId}`)
                                                  }}
                                              >
                                                  <ReactSVG wrapper="div" style={{ fill: 'red' }} src="/faturamento/assets/icons/icon-eye.svg" />
                                              </S.WrapperAction>
                                          )
                                      }
                                  })
                                : lotes?.map((item, index) => {
                                      return {
                                          ...item,
                                          valorRecursado: <S.AlignCenter>{NumberUtils.maskMoney(item?.valorRecursado)}</S.AlignCenter>,
                                          valorDeferido: <S.AlignCenter>{NumberUtils.maskMoney(item?.valorDeferido)}</S.AlignCenter>,
                                          valorIndeferido: <S.AlignCenter>{NumberUtils.maskMoney(item?.valorIndeferido)}</S.AlignCenter>,
                                          acao: (
                                              <S.WrapperAction
                                                  ativo={true}
                                                  onClick={() => {
                                                      //FIXME:
                                                      //   if (item?.situacao === 'EM_ANALISE') {

                                                      //   }

                                                      router.push(`/processamento-contas/recurso-glosa/prestador/lotes/${item?.loteRecursoId}`)
                                                  }}
                                              >
                                                  <ReactSVG wrapper="div" style={{ fill: 'red' }} src="/faturamento/assets/icons/icon-eye.svg" />
                                              </S.WrapperAction>
                                          )
                                      }
                                  })
                        }
                        pagination={pagination}
                        customGridStyles={status !== 'CANCELADO' ? '1fr 1fr 1.2fr 1.5fr 1.5fr 1fr 1fr 1fr 0.1fr' : '0.5fr 0.5fr 2fr 1fr 0.8fr 0.2fr'}
                        noSelectAll={true}
                        selectIdField={'id'}
                    />
                ) : (
                    <NoContent title="Nenhum lote encontrado" />
                )}
            </DividerSectionCard>
            <Modal open={showModalFinalizarPrestador} onClose={() => setShowModalFinalizarPrestador(false)}>
                <Box display="flex" gap="16px" flexDirection="column">
                    <S.PrestadorInfoWrapper>
                        <h3>Finalizar recurso de glosa do prestador Hospital São Pedro?</h3>
                    </S.PrestadorInfoWrapper>
                    <Box display="flex" justifyContent="end">
                        <Button
                            onClick={() => setShowModalFinalizarPrestador(false)}
                            style={{ width: 'fit-content' }}
                            themeButton="gray"
                            typeButton="text"
                        >
                            Cancelar
                        </Button>
                        <Button
                            disabled={!enableButton}
                            themeButton="warning"
                            style={{ width: 'fit-content' }}
                            onClick={() => handleFinalizarPrestador()}
                        >
                            Finalizar
                        </Button>
                    </Box>
                </Box>
            </Modal>
        </>
    )
}

export default ResumoPrestadorTemplate
