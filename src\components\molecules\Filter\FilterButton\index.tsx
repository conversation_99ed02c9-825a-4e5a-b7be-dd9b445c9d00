import Button, { ButtonProps } from 'components/atoms/Button'
import React from 'react'

const FilterButton = ({
    iconLeft = '/faturamento/assets/icons/filter.svg',
    themeButton = 'primary',
    typeButton = 'text',
    color = 'rgba(0, 80, 229, 1)',
    ...rest
}: ButtonProps) => {
    return <Button iconLeft={iconLeft} themeButton={themeButton} typeButton={typeButton} color={color} {...rest} />
}

export default FilterButton
