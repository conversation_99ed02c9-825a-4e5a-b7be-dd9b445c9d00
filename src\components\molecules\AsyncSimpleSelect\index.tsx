/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-explicit-any */

import React, { useState } from 'react'
import theme from 'styles/theme'
import { components, StylesConfig, GroupBase } from 'react-select'
import AsyncSelect from 'react-select/async'
import themeStyles from '../../../styles/theme'
import * as S from './styles'

type AsyncSimpleSelectProps = {
    defaultValue?: any
    onChange: any
    loadOptions?: (inputValue: string, callback: any) => any
    value?: any
    label?: string
    required?: boolean
    isClearable?: boolean
    disabled?: boolean
    cacheOption?: boolean
    hasAddOption?: boolean
    defaultOptions?: boolean
}

export type Options = {
    label: string
    value: string
    descricao?: string
}

const AsyncSimpleSelect = ({
    defaultValue,
    value,
    onChange,
    loadOptions,
    label,
    isClearable,
    required,
    disabled,
    hasAddOption,
    cacheOption = true,
    defaultOptions = true
}: AsyncSimpleSelectProps) => {
    const [isFocused, setIsFocused] = useState(false)

    const NoOptionsMessage = (props: any) => {
        return (
            <components.NoOptionsMessage {...props}>
                <span className="custom-css-class">Sem opções</span>
            </components.NoOptionsMessage>
        )
    }

    const stylesConfig: any = {
        control: (provider, state) => ({
            display: 'flex',
            flexDirection: 'row',
            borderRadius: '0.4rem',
            fontSize: '1.6rem',
            height: '5.6rem',
            border: state.isFocused ? '2px solid' : '1px solid',
            borderColor: state.isFocused ? `${theme.colors.primary['500']}` : `${themeStyles.colors.black[16]}`,
            ...(disabled && {
                backgroundColor: theme.colors.black['08']
            })
        }),
        option: (provider, state) => ({
            ...provider,
            padding: '0.8rem 1.6rem',
            fontSize: '1.4rem',
            lineHeight: '2rem',
            fontWeight: '400',
            color: state.isSelected ? '#fff' : theme.colors.black[88],
            ...(state.isFocused && {
                backgroundColor: theme.colors.maida.primary.opacity
            }),
            ':active': {
                backgroundColor: theme.colors.maida.primary.opacity
            },
            ...(state.isSelected && {
                backgroundColor: theme.colors.primary[500]
            }),
            ...(hasAddOption && {
                ':nth-child(1)': {
                    color: themeStyles.colors.black[88],
                    fontWeight: 600,
                    cursor: 'pointer'
                }
            })
        }),
        valueContainer: (provider, state) => ({
            ...provider,
            paddingLeft: '1.4rem'
        }),
        indicatorSeparator: () => ({
            display: 'none'
        }),
        indicatorsContainer: (provider, state) => ({
            ...provider,
            paddingRight: '0.8rem'
        }),
        singleValue: (provider, state) => ({
            ...provider,
            fontSize: '1.6rem',
            lineHeight: '2.4rem',
            fontWeight: '400',
            color: theme.colors.black[88],
            ...(disabled && {
                color: theme.colors.black[56]
            })
        }),
        menu: (provider, state) => ({
            ...provider,
            zIndex: '5'
        })
    }

    return (
        <S.Wrapper isFocused={isFocused || (defaultValue !== '' && defaultValue !== undefined && defaultValue !== null)} focus={isFocused}>
            <label>
                {label}
                {required && <span> *</span>}
            </label>
            <AsyncSelect
                styles={stylesConfig}
                instanceId="postType"
                components={{ NoOptionsMessage }}
                placeholder={''}
                defaultValue={defaultValue}
                value={value}
                onChange={onChange}
                loadOptions={loadOptions}
                isClearable={isClearable}
                onFocus={() => {
                    setIsFocused(true)
                }}
                isDisabled={disabled}
                onBlur={() => {
                    setIsFocused(false)
                }}
                isSearchable
                cacheOptions={cacheOption}
                defaultOptions={defaultOptions}
            />
        </S.Wrapper>
    )
}

export default AsyncSimpleSelect

