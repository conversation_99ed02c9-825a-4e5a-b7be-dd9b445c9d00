import { default as InputOut } from 'components/molecules/novos/Input'
import React, { InputHTMLAttributes } from 'react'
import { useFiltroContext } from '../../Root'

interface IInput {
    field: string
}
export default function Input({ field, ...props }: IInput & InputHTMLAttributes<HTMLInputElement>) {
    const { setFieldValue, filter } = useFiltroContext()

    const handleOnChange: React.ChangeEventHandler<HTMLInputElement> = (e) => {
        setFieldValue(e.currentTarget.value, field)
    }

    return <InputOut type={'text'} value={filter?.[field]} onChange={handleOnChange} {...props} />
}
