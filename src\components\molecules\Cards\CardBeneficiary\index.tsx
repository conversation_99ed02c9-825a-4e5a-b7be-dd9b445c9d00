/* eslint-disable prettier/prettier */
import React from 'react'
import { ReactSVG } from 'react-svg'
import * as S from './styles'

type CardBeneficiaryProps = {
    holder?: string
    plan?: string
    cpf?: string
    name?: string
    type?: string
    onClick?: () => void
}

const CardBeneficiary = ({ holder, plan, cpf, type, onClick }: CardBeneficiaryProps) => {
    return (
        <S.Content onClick={onClick}>
            <S.Data>
                <ReactSVG wrapper="div" src="/faturamento/assets/icons/human.svg"></ReactSVG>
                <div>
                    <h4>{holder}</h4>
                    {cpf || type || plan && <span>
                        {type} - {plan} - {cpf}
                    </span>}
                   
                </div>
            </S.Data>
        </S.Content>
    )
}

export default CardBeneficiary

