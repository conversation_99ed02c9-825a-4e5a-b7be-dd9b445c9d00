import styled from 'styled-components'

export const Content = styled.main`
    margin: 0 auto;
    width: 95%;
    height: 60vh;
    max-width: 1400px;
    border-radius: 8px;
    background-color: #ffffff;
`

export const Header = styled.div`
    width: 95%;
    padding: 16px;
    margin: 0 auto;
    max-width: 1400px;
    border-radius: 8px;
    background-color: #ffffff;
    display: flex;
    flex-direction: column;
`

export const ResultHeader = styled.div`
    display: flex;
    justify-content: space-between;
`

export const ContentItem = styled.div`
    display: grid;
    margin-bottom: 10px;
    padding: 24px 16px 0 16px;
    grid-template-columns: 1fr 1fr 2.5fr 0.4fr 0.4fr;
`

export const ContentItemResult = styled.div`
    display: grid;
    cursor: pointer;
    padding: 10px 16px;
    background: #f6f6f9;
    border-radius: 8px;
    align-items: center;
    grid-template-columns: 1fr 1fr 2.5fr 0.4fr 0.4fr;
`
