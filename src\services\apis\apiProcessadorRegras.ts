import axios from 'axios'
import { jwtAuthorizationHeaderInjector } from './jwtAuthorizationHeaderInjector'

export const apiProcessadorRegras = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_PROCESSADOR_REGRAS,
    headers: {
        'Content-Type': 'application/json'
    }
})

apiProcessadorRegras.interceptors.request.use(
    (config) => {
        return jwtAuthorizationHeaderInjector(config)
    },
    (error) => Promise.reject(error)
)
