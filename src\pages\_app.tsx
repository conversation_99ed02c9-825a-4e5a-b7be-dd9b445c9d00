import { NextPage } from 'next'
import { AppProps } from 'next/app'
import { ReactElement, ReactNode } from 'react'
import { CalendarFormProvider } from 'src/context/CalendarContext/useBillingCalendar'
import { NewPeriodFormProvider } from 'src/context/CalendarContext/useNewPeriod'
import { NewProcedureFormProvider } from 'src/context/ParametersContext/useNewProcedure'
import { ParameterFormProvider } from 'src/context/ParametersContext/useParameter'
import { ThemeProvider } from '@mui/material/styles'
import { ThemeProvider as StyledThemeProvider } from 'styled-components'
import GlobalStyles from '../styles/globals'
import { ToastProvider } from 'src/context/ToastContext'
import themeMui from 'styles/themeMui'
import theme from '../styles/theme'
import { ToastContainer } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.css'
import Head from 'next/head'
import AuthProvider from 'src/context/AuthContext'
import { switchAssetsClient } from 'utils/parseAssets'
import AplicationProvider from 'src/context/AplicationContext'
import { QueryClient, QueryClientProvider } from 'react-query'

import 'react-loading-skeleton/dist/skeleton.css'

type NextPageWithLayout = NextPage & {
    getLayout?: (page: ReactElement) => ReactNode
}

type AppPropsWithLayout = AppProps & {
    Component: NextPageWithLayout
}

const queryClient = new QueryClient()

function App({ Component, pageProps }: AppPropsWithLayout) {
    const getLayout = Component.getLayout ?? ((page) => page)

    return (
        <>
            <Head>
                <title>Faturamento</title>
                <meta name="description" content="A simple project starter to work with Typescript, React, NextJS and Styled Components" />
                <link rel="manifest" href="/manifest.json" />
                <link rel="preconnect" href="https://fonts.gstatic.com" />
                <link rel="icon" href={`/faturamento/assets/clients/${switchAssetsClient()}/favicon.svg`} />
            </Head>
            <QueryClientProvider client={queryClient}>
                <StyledThemeProvider theme={theme}>
                    <ThemeProvider theme={themeMui}>
                        <CalendarFormProvider>
                            <NewPeriodFormProvider>
                                <ParameterFormProvider>
                                    <NewProcedureFormProvider>
                                        <ToastContainer autoClose={5000} />

                                        <ToastProvider>
                                            <AuthProvider>
                                                <AplicationProvider>
                                                    {getLayout(<Component {...pageProps} />)} <GlobalStyles />
                                                </AplicationProvider>
                                            </AuthProvider>
                                        </ToastProvider>
                                    </NewProcedureFormProvider>
                                </ParameterFormProvider>
                            </NewPeriodFormProvider>
                        </CalendarFormProvider>
                    </ThemeProvider>
                </StyledThemeProvider>
            </QueryClientProvider>
        </>
    )
}

export default App
