import { Box, CircularProgress } from '@mui/material'
import Button from 'components/atoms/Button'
import Checkbox from 'components/atoms/CheckBox'
import NoContent from 'components/molecules/NoContent'
import Pagination from 'components/molecules/Pagination'
import moment from 'moment'
import React, { useEffect, useState } from 'react'
import { useAuth } from 'src/hooks/auth'
import { useToast } from 'src/hooks/toast'
import { CobrancaServices } from 'src/services/faturamentoPrestadorApi/cobrancaServices'
import { ModuloEnum } from 'src/services/faturamentoPrestadorApi/geracao-recurso-glosa/enuns'
import { ILoteCobrancaDTO, IPageLote } from 'types/cobrancaPrestador/loteCobranca'
import { IPrestadorAnaliseQuery } from 'types/cobrancaPrestador/prestadorAnalise'
import { IPagination } from 'types/common/pagination'
import { retiraSequencial } from 'utils/functions'
import { PaginationHelper } from 'utils/helpers/paginationHelper'
import { capitalize } from 'utils/stringUtils'
import { ICheckeboxFilter } from '../TabLoteMedico'
import { filterCardEnum } from '../TabLoteMedico/enuns'
import * as S from './styles'

export type lotChecked = {
    index: number
    checked: boolean
    numberLote: string
}
type ListaLotesProcessandoProps = {
    competenciaSelecionada: IPrestadorAnaliseQuery
    carregarSituacoes: any
    refreshList: boolean
    filtroNumeroLote: string
    lotes: IPageLote
    setLotes?: any
    loadingLotes: boolean
    setLoadingLotes: React.Dispatch<React.SetStateAction<boolean>>
    checkboxFilter: ICheckeboxFilter
    setCheckboxFilter: React.Dispatch<React.SetStateAction<ICheckeboxFilter>>
}

export type itemsProps = {
    component: React.ReactNode
}

const ListaLotesProcessando = ({
    competenciaSelecionada,
    carregarSituacoes,
    refreshList,
    filtroNumeroLote,
    lotes,
    setLotes,
    loadingLotes,
    setLoadingLotes,
    checkboxFilter,
    setCheckboxFilter
}: ListaLotesProcessandoProps) => {
    const { addToast } = useToast()
    const { prestadorVinculado } = useAuth()

    const [checkedAll, setCheckedAll] = useState(false)
    const [lotsCheckeds, setLotsCheckeds] = useState<lotChecked[]>([])
    const [pagination, setPagination] = useState<IPagination>()
    const [numberPage, setNumberPage] = useState(0)

    useEffect(() => {
        initLotsCheckeds(lotes?.content)
    }, [lotes])

    const initLotsCheckeds = (data: ILoteCobrancaDTO[]) => {
        const lotsCheckeds: lotChecked[] = []
        data?.forEach((item, index) => {
            lotsCheckeds.push({
                index,
                checked: false,
                numberLote: item?.loteCobrancaId?.toString()
            })
        })
        setLotsCheckeds(lotsCheckeds)
    }

    function checkAll() {
        setLotsCheckeds(lotsCheckeds.map((item) => ({ ...item, checked: !checkedAll })))
    }
    const returnModulo = () => {
        if (checkboxFilter.lotesCobranca && checkboxFilter.lotesRecursoGlosa) {
            return null
        }
        if (!checkboxFilter.lotesCobranca && !checkboxFilter.lotesRecursoGlosa) {
            return 'EMPTY'
        }
        if (checkboxFilter.lotesCobranca && !checkboxFilter.lotesRecursoGlosa) {
            return ModuloEnum.MEDICO
        }
        if (checkboxFilter.lotesRecursoGlosa && !checkboxFilter.lotesCobranca) {
            return ModuloEnum.RECURSO
        }
    }

    const carregarLotes = (modulo: ModuloEnum | 'EMPTY' = null) => {
        setLoadingLotes(true)
        if (modulo === 'EMPTY') {
            setLotes(null)
            setLoadingLotes(false)
        } else {
            CobrancaServices.getLotes(competenciaSelecionada?.competencia, prestadorVinculado?.uuid, filterCardEnum.PROCESSANDO, {
                modulo,
                filtroNumeroLote: filtroNumeroLote !== '' ? retiraSequencial(filtroNumeroLote) : null,
                page: numberPage,
                size: 10
            })
                .then(({ data }) => {
                    setLotes(data)

                    const objectPagination = PaginationHelper.parserPagination<ILoteCobrancaDTO>(data, setNumberPage)
                    setPagination(objectPagination)
                })
                .finally(() => setLoadingLotes(false))
        }
    }

    useEffect(() => {
        carregarLotes(returnModulo())
    }, [numberPage, refreshList])

    useEffect(() => {
        if (!lotes) return

        setCheckedAll(lotsCheckeds.filter((i) => i.checked).length === lotes?.content.length)
    }, [lotsCheckeds, lotes])

    // useEffect(() => {
    //     if (!refresh) return

    //     carregarLotes(returnModulo())
    //     carregarSituacoes()
    //     setRefresh(false)
    // }, [refresh])

    const handleClickCancelarLotes = () => {
        const cancelarLotes = async () => {
            let errosOcorridos = false
            await Promise.all(
                lotsCheckeds
                    ?.filter((i) => i.checked)
                    .map(async (lote) => {
                        try {
                            await CobrancaServices.patchCancelamento(lote.numberLote)
                        } catch {
                            errosOcorridos = true
                        }
                    })
            )

            if (!errosOcorridos) {
                addToast({ title: 'Lotes cancelados com sucesso', type: 'success' })
                carregarLotes(returnModulo())
                carregarSituacoes()
            } else {
                addToast({ title: 'Ocorreu erro ao cancelar o lote. Tente novamente.', type: 'error' })
            }
        }

        cancelarLotes()

        setTimeout(() => {
            if (carregarSituacoes) carregarSituacoes()
        }, 1000)
    }

    return (
        <>
            <S.CheckboxWrapper>
                <Checkbox
                    checked={checkboxFilter.lotesCobranca}
                    onCheck={() => setCheckboxFilter({ ...checkboxFilter, lotesCobranca: !checkboxFilter.lotesCobranca })}
                    label="Lotes de cobrança"
                />

                <Checkbox
                    checked={checkboxFilter.lotesRecursoGlosa}
                    onCheck={() => setCheckboxFilter({ ...checkboxFilter, lotesRecursoGlosa: !checkboxFilter.lotesRecursoGlosa })}
                    label="Lotes de recurso de glosa"
                />
            </S.CheckboxWrapper>
            {loadingLotes ? (
                <Box sx={{ display: 'flex', minHeight: 218, alignItems: 'center', justifyContent: 'center' }}>
                    <CircularProgress sx={{ color: '#2B45D4' }} />
                </Box>
            ) : (
                <S.Wrapper>
                    {!lotes || lotes?.content.length === 0 ? (
                        <NoContent title="No momento não existe nenhum lote em processamento" path="/parametros/regras-de-excludencia/nova-regra" />
                    ) : (
                        <>
                            <div className="contentInput">
                                <div className="contentNumber">
                                    {/* <span className="pageNumber">1-10 de 248</span> */}
                                    <span className="pageNumber">
                                        {pagination?.paginaAtual * pagination?.linhasPorPagina + 1} -{' '}
                                        {lotes?.content?.length + pagination?.paginaAtual * pagination?.linhasPorPagina}
                                        {' de '}
                                        {pagination?.totalRegistros}
                                    </span>
                                    {/* <ReactSVG src="/faturamento/assets/icons/dot.svg" /> */}
                                </div>
                            </div>

                            {lotsCheckeds?.some((i) => i.checked) ? (
                                <S.ContentCancelLotes onClick={handleClickCancelarLotes}>
                                    <S.ButtonWrapper>
                                        <Button typeButton="ghost" themeButton="octopus">
                                            Cancelar lotes selecionados
                                        </Button>
                                    </S.ButtonWrapper>
                                </S.ContentCancelLotes>
                            ) : (
                                <S.ContentCancelLotes style={{ cursor: 'default' }}>
                                    <S.ButtonWrapper>
                                        <Button typeButton="ghost" themeButton="octopus" disabled>
                                            Cancelar lotes selecionados
                                        </Button>
                                    </S.ButtonWrapper>
                                </S.ContentCancelLotes>
                            )}

                            <S.Table>
                                <tr>
                                    <th className="checkLotLabel">
                                        <input
                                            value={'01'}
                                            type="checkbox"
                                            id={'01'}
                                            onChange={() => {
                                                checkAll()
                                            }}
                                            checked={checkedAll}
                                        />
                                        Lote
                                    </th>
                                    <th>Data de envio</th>
                                    <th>Usuário</th>
                                    <th>Envio</th>
                                </tr>

                                {lotes?.content?.map((lote, index: number) => (
                                    <S.TabContent
                                        key={index}
                                        checked={lotsCheckeds[index]?.checked}
                                        onClick={() => {
                                            //   route.push('/faturamento/guia');
                                        }}
                                    >
                                        <td className="checkLot">
                                            <input
                                                value={lote?.numeroLote}
                                                type="checkbox"
                                                id={lote?.numeroLote?.toString()}
                                                onChange={() => {
                                                    const newLotsCheckeds = [...lotsCheckeds]

                                                    if (!newLotsCheckeds) return

                                                    if (!newLotsCheckeds[index]) return

                                                    newLotsCheckeds[index].checked = !newLotsCheckeds[index]?.checked
                                                    setLotsCheckeds(newLotsCheckeds)
                                                }}
                                                checked={lotsCheckeds[index]?.checked}
                                            />
                                            {lote?.numeroLote}
                                        </td>
                                        <td>{moment(lote?.dataEnvio).format('DD/MM/YYYY [-] HH:mm')}</td>
                                        <td>{capitalize(lote?.nomeUsuario)}</td>
                                        <td>{lote?.tipoEnvio === 'ELETRONICO' ? 'Eletrônico' : capitalize(lote?.tipoEnvio)}</td>
                                        {/* <td>
                                <ReactSVG src="/faturamento/assets/icons/ic-trash.svg" />
                            </td> */}
                                    </S.TabContent>
                                ))}
                            </S.Table>

                            <div style={{ marginTop: '40px' }}>
                                <Pagination
                                    totalPage={pagination?.totalPaginas}
                                    totalRegister={pagination?.totalRegistros}
                                    actualPage={pagination?.paginaAtual}
                                    setNumberPage={(page) => setNumberPage(page)}
                                />
                            </div>
                        </>
                    )}
                </S.Wrapper>
            )}
        </>
    )
}

export default ListaLotesProcessando
