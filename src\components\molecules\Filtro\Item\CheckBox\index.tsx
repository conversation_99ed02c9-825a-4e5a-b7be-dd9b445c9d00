import { default as Check } from 'components/molecules/novos/CheckBox'
import React, { InputHTMLAttributes } from 'react'
import { useFiltroContext } from '../../Root'
import { Wrapper } from './styles'

interface ICheckbox {
    field: string
}

const CheckBox = ({ field, placeholder, ...props }: ICheckbox & InputHTMLAttributes<HTMLInputElement>) => {
    const { setFieldValue, filter } = useFiltroContext()

    const handleOnChange = (e: boolean) => {
        setFieldValue(e, field)
    }

    return (
        <Wrapper>
            <Check style={{ marginLeft: '50px' }} checked={filter?.[field]} onCheck={handleOnChange} label={placeholder} {...props} />
        </Wrapper>
    )
}

export default CheckBox
