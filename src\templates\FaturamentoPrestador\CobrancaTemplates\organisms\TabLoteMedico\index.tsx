import Button from 'components/atoms/Button'
import CardsFilter from 'components/molecules/GuideGlosaFilter'
import ListaLotesCancelados from '../ListaLotesCancelados'
import ListaLotesFechados from '../ListaLotesFechados'
import ListaLotesRecusados from '../ListaLotesRecusados'
// import ListaLotesDevolvidos from '../ListaLotesDevolvidos'
import ListaLotesProcessando from '../ListaLotesProcessando'
import ListaLotesEmAnalise from '../ListaLotesEmAnalise'
import ListaLotesEmAuditoria from '../ListaLotesEmAuditoria'

import React, { useCallback, useEffect, useState } from 'react'
import { useToast } from 'src/hooks/toast'
import { CobrancaServices, IGetCobrancaProps, situacaoLoteEnum } from 'src/services/faturamentoPrestadorApi/cobrancaServices'
import { IPageLote } from 'types/cobrancaPrestador/loteCobranca'
import { IPrestadorAnaliseQuery } from 'types/cobrancaPrestador/prestadorAnalise'
import { IResumoSituacaoLoteCobrancaQuery } from 'types/cobrancaPrestador/resumoSituacaoLoteCobranca'
import * as S from './styles'
import { NumberUtils } from 'utils/numberUtils'
import ModalDocumentosNovoLote from '../ModalDocumentosNovoLote'
import Input from 'components/molecules/SearchBarGuiaGlosa'
import NoContent from 'components/molecules/NoContent'
import { useAuth } from 'src/hooks/auth'
import { filterCardEnum, tiposLotesEnum } from './enuns'
import ListaLotesAnalisados, { ICheckeboxFilterAnalisado } from '../ListaLotesAnalisados'
import { GeracaoRecursoGlosa } from 'src/services/faturamentoPrestadorApi/geracao-recurso-glosa'
import { ModuloEnum } from 'src/services/faturamentoPrestadorApi/geracao-recurso-glosa/enuns'
import { retiraSequencial } from 'utils/functions'
import ListaEmConstrucao from '../ListaEmConstrucao'
import ModalNovoLoteV2 from '../ModalNovoLoteV2'
// import ListaEmConstrucao from '../ListaEmConstrucao'

export interface ICheckeboxFilter {
    lotesCobranca: boolean
    lotesRecursoGlosa: boolean
}

const TabLoteMedico = ({
    competenciaSelecionada,
    competenciaDisponivel,
    setForceUpdateCompetencias
}: {
    competenciaSelecionada: IPrestadorAnaliseQuery
    competenciaDisponivel: any
    setForceUpdateCompetencias: any
}) => {
    const { addToast } = useToast()
    const { prestadorVinculado } = useAuth()

    const [isOpenModal, setIsOpenModal] = useState(false)
    const [isOpenModalDocumentos, setIsOpenModalDocumentos] = useState(false)
    const [idLoteParaConciliacao, setIdLoteParaConciliacao] = useState<string>()
    const [filterFields, setFilterFields] = useState([])
    const [idLoteCobranca, setIdLoteCobranca] = useState()
    const [documentosObrigatorios, setDocumentosObrigatorios] = useState([])
    const [numeroNota, setNumeroNota] = useState()
    const [valorNota, setValorNota] = useState()
    const [dataEmissao, setDataEmissao] = useState<string>()
    const [fileNFUploadModal, setFileNFUploadModal] = useState()
    const [isReset, setIsReset] = useState(false)
    const [filterSelected, setFilterSelected] = useState<filterCardEnum>(filterCardEnum.PROCESSANDO)
    const [lotes, setLotes] = useState<IPageLote | any>()
    const [checkboxFilterAnalisado, setCheckboxFilterAnalisado] = useState<ICheckeboxFilterAnalisado>({
        lotesSemGlosa: false,
        lotesGlosaNaoRecursado: false,
        lotesGlosaRecursado: false,
        lotesRecurso: false
    })
    const [checkboxFilterAnalise, setCheckboxFilterAnalise] = useState<ICheckeboxFilter>({
        lotesCobranca: false,
        lotesRecursoGlosa: false
    })
    const [checkboxFilterProcessando, setCheckboxFilterProcessando] = useState<ICheckeboxFilter>({
        lotesCobranca: false,
        lotesRecursoGlosa: false
    })
    const [checkboxFilterRecusado, setCheckboxFilterRecusado] = useState<ICheckeboxFilter>({
        lotesCobranca: false,
        lotesRecursoGlosa: false
    })
    const [exigeNF, setExigeNF] = useState<boolean>()
    const [calendarioEnvioLotesAberto, setCalendarioEnvioLotesAberto] = useState<boolean>(true)
    // const [refresh, setRefresh] = useState(false)
    const [refreshList, setRefreshList] = useState(false)
    const [situacoes, setSituacoes] = useState<IResumoSituacaoLoteCobrancaQuery[]>()
    const [filtro, setFiltro] = useState<string>('')
    const [loadingLotes, setLoadingLotes] = useState(true)

    // const [lotesDevolucao, setLotesDevolucao] = useState<ILoteDevolucaoQuery[]>([])
    // const [lotesRecusa, setLotesRecusa] = useState<ILoteRecusaQuery[]>([])

    const handleResetState = () => {
        setIsOpenModal(false)
        setIsOpenModalDocumentos(false)
        // setIsOpenModalConciliacao(false)
        setIdLoteParaConciliacao(null)
        setDocumentosObrigatorios([])
        setNumeroNota(null)
        setValorNota(null)
        setFileNFUploadModal(null)
        setExigeNF(null)
        setIsReset(true)

        carregarSituacoes()
    }

    const carregarSituacoes = useCallback(() => {
        CobrancaServices.getPorcentagemSituacoes(competenciaSelecionada.competencia, prestadorVinculado.uuid).then(({ data }) => {
            setSituacoes(data)
        })
    }, [competenciaSelecionada])

    useEffect(() => {
        if (!competenciaSelecionada) return

        carregarSituacoes()
    }, [competenciaSelecionada])

    const getItemSituacao = (situacao: string) => {
        return situacoes?.some((i) => i.situacao === situacao)
            ? situacoes?.find((i) => i.situacao === situacao)
            : { porcentagem: 0, quantidadeDeLotes: 0 }
    }

    useEffect(() => {
        const { porcentagem: percProcessando, quantidadeDeLotes: qtdProcessando } = getItemSituacao('PROCESSANDO')
        const { porcentagem: percEmAuditoria, quantidadeDeLotes: qtdEmAuditoria } = getItemSituacao('EM_AUDITORIA')
        const { porcentagem: percEmAnalise, quantidadeDeLotes: qtdEmAnalise } = getItemSituacao('EM_ANALISE')
        const { porcentagem: percFechado, quantidadeDeLotes: qtdFechado } = getItemSituacao('FECHADO')
        const { porcentagem: percCancelado, quantidadeDeLotes: qtdCancelado } = getItemSituacao('CANCELADO')
        const { porcentagem: percRecusado, quantidadeDeLotes: qtdRecusado } = getItemSituacao('RECUSADO')
        // const { porcentagem: percDevolvido, quantidadeDeLotes: qtdDevolvido } = getItemSituacao('DEVOLVIDO')

        const planservFilters = [
            { name: 'Em construção', value: ``, situacao: 'EM_CONSTRUCAO' },
            { name: 'Processando', value: `${qtdProcessando || '0'} - (${percProcessando || '0'}%)`, situacao: 'PROCESSANDO' },
            { name: 'Em análise', value: `${qtdEmAnalise || '0'} - (${percEmAnalise || '0'}%)`, situacao: 'EM_ANALISE' },
            // { name: 'Fechado', value: `${qtdFechado || '0'} - (${percFechado || '0'}%)`, situacao: 'FECHADO' },
            { name: 'Analisado', value: `${qtdFechado || '0'} - (${percFechado || '0'}%)`, situacao: 'ANALISADO' },
            { name: 'Cancelado', value: `${qtdCancelado || '0'} - (${percCancelado || '0'}%)`, situacao: 'CANCELADO' },
            { name: 'Recusado', value: `${qtdRecusado || '0'} - (${percRecusado || '0'}%)`, situacao: 'RECUSADO' }
        ]

        setFilterFields(planservFilters)
    }, [situacoes])

    useEffect(() => {
        if (!competenciaSelecionada) return
        setRefreshList(!refreshList)
    }, [filterSelected, competenciaSelecionada])

    useEffect(() => {
        setRefreshList(!refreshList)
    }, [checkboxFilterProcessando])

    useEffect(() => {
        // setCalendarioEnvioLotesAberto(true)

        CobrancaServices.getAbertoParaEnvioLote({
            modulo: 'MEDICO',
            competencia: competenciaSelecionada?.competencia,
            prestadorId: prestadorVinculado?.uuid
        })
            .then(({ data }) => {
                setCalendarioEnvioLotesAberto(!data?.calendarioEnvioLotesAberto)
            })
            .catch(() => {
                // addToast({ title: 'Ocorreu erro ao enviar a nota fiscal. Tente novamente.', type: 'error' })
            })
    }, [competenciaSelecionada])

    const handleClickGerarNota = (idLote, numeroNota, valorNota, dataEmissao, file, documentosObrigatorios) => {
        if (!idLote && !numeroNota && !valorNota && !file) {
            return carregarSituacoes()
        }

        CobrancaServices.postNotaFiscalParaLotes({
            idLotes: [idLote],
            numeroNota: numeroNota,
            valor: NumberUtils.unMaskMoney(valorNota)?.toString(),
            dataEmissao,
            arquivos: [file]
        })
            .then(() => {
                carregarSituacoes()

                if (documentosObrigatorios.length === 0) {
                    setRefreshList(!refreshList)
                    addToast({ title: 'Lote e nota fiscal enviados com sucesso.', type: 'success', duration: 5000 })
                    handleResetState()
                }
            })
            .catch(() => {
                addToast({ title: 'Ocorreu erro ao enviar a nota fiscal. Tente novamente.', type: 'error' })
            })
    }

    const handleSubmit = () => {
        if (exigeNF) {
            handleClickGerarNota(idLoteCobranca, numeroNota, valorNota, dataEmissao, fileNFUploadModal, documentosObrigatorios)
        }
        setIsReset(true)
    }

    const handleClickPesquisar = () => {
        setRefreshList(!refreshList)
    }

    const handleClickClearBtn = () => {
        setFiltro('')
        setRefreshList(!refreshList)
    }

    return (
        <S.Container>
            <S.HeaderSearch>
                <S.Search>
                    <Input
                        value={filtro}
                        placeholder="Procure por lote"
                        handleClickClearField={() => handleClickClearBtn()}
                        handleClickIconRight={handleClickPesquisar}
                        handleOnChange={(e) => setFiltro(e.target.value)}
                    />
                </S.Search>
                <S.AddButton>
                    <div className="contentButton">
                        <Button
                            disabled={calendarioEnvioLotesAberto}
                            themeButton="secondary"
                            style={{ padding: '12px 20px' }}
                            iconLeft={'/faturamento/assets/icons/plus.svg'}
                            onClick={() => {
                                setIsOpenModal(true)
                            }}
                        >
                            Novo lote
                        </Button>
                    </div>
                </S.AddButton>
            </S.HeaderSearch>
            <div className="contentHeader">
                <CardsFilter
                    cardsFilter={filterFields}
                    filterSelected={filterSelected}
                    setFilterSelected={(value: filterCardEnum) => {
                        setFilterSelected(value)
                        setFiltro('')
                    }}
                />
            </div>

            {/* table */}
            {filterSelected === 'EM_CONSTRUCAO' && (
                <ListaEmConstrucao
                    lotes={lotes}
                    filtroNumeroLote={filtro}
                    carregarSituacoes={carregarSituacoes}
                    competenciaSelecionada={competenciaSelecionada}
                    refreshList={refreshList}
                    setLotes={setLotes}
                    loadingLotes={loadingLotes}
                    setLoadingLotes={setLoadingLotes}
                />
            )}

            {filterSelected === 'PROCESSANDO' && (
                <ListaLotesProcessando
                    lotes={lotes}
                    filtroNumeroLote={filtro}
                    carregarSituacoes={carregarSituacoes}
                    competenciaSelecionada={competenciaSelecionada}
                    refreshList={refreshList}
                    setLotes={setLotes}
                    loadingLotes={loadingLotes}
                    setLoadingLotes={setLoadingLotes}
                    checkboxFilter={checkboxFilterProcessando}
                    setCheckboxFilter={setCheckboxFilterProcessando}
                />
            )}

            {/* {filterSelected === 'EM_AUDITORIA' && (
                <ListaLotesEmAuditoria
                    forceUpdate={isOpenModal}
                    competenciaSelecionada={competenciaSelecionada}
                    searchLote={filtro}
                    setLotes={setLotes}
                    lotes={lotes}
                />
            )} */}

            {filterSelected === 'EM_ANALISE' && (
                <ListaLotesEmAnalise
                    filtro={filtro}
                    forceUpdate={isOpenModal}
                    competenciaSelecionada={competenciaSelecionada}
                    searchLote={filtro}
                    setLotes={setLotes}
                    refreshList={refreshList}
                    lotes={lotes}
                    checkboxFilter={checkboxFilterAnalise}
                    setCheckboxFilter={setCheckboxFilterAnalise}
                    loadingLotes={loadingLotes}
                    setLoadingLotes={setLoadingLotes}
                />
            )}

            {filterSelected === 'ANALISADO' && (
                <ListaLotesAnalisados
                    filtro={filtro}
                    forceUpdate={isOpenModal}
                    competenciaSelecionada={competenciaSelecionada}
                    searchLote={filtro}
                    tipoLote={tiposLotesEnum.LOTES_GLOSA_NAO_RECURSADO}
                    setLotes={setLotes}
                    refreshList={refreshList}
                    lotes={lotes}
                    checkboxFilter={checkboxFilterAnalisado}
                    setCheckboxFilter={setCheckboxFilterAnalisado}
                    loadingLotes={loadingLotes}
                    setLoadingLotes={setLoadingLotes}
                />
            )}
            {/* DECRAPTED */}
            {/* {filterSelected === 'FECHADO' && (
                <ListaLotesFechados
                    forceUpdate={isOpenModal}
                    competenciaSelecionada={competenciaSelecionada}
                    searchLote={filtro}
                    setLotes={setLotes}
                    refreshList={refreshList}
                    lotes={lotes}
                />
            )} */}
            {filterSelected === 'CANCELADO' && (
                <ListaLotesCancelados
                    forceUpdate={isOpenModal}
                    competenciaSelecionada={competenciaSelecionada}
                    searchLote={filtro}
                    setLotes={setLotes}
                    refreshList={refreshList}
                    lotes={lotes}
                    loadingLotes={loadingLotes}
                    setLoadingLotes={setLoadingLotes}
                />
            )}
            {filterSelected === 'RECUSADO' && (
                <ListaLotesRecusados
                    forceUpdate={isOpenModal}
                    competenciaSelecionada={competenciaSelecionada}
                    searchLote={filtro}
                    refreshList={refreshList}
                    setLotes={setLotes}
                    lotes={lotes}
                    loadingLotes={loadingLotes}
                    setLoadingLotes={setLoadingLotes}
                    checkboxFilter={checkboxFilterRecusado}
                    setCheckboxFilter={setCheckboxFilterRecusado}
                />
            )}

            {/* {filterSelected === 'NOCONTENT' && (
                <NoContent
                    title="Não encontramos o lote pesquisado. Confira o número digitado e tente novamente."
                    path="/parametros/regras-de-excludencia/nova-regra"
                />
            )} */}

            <ModalNovoLoteV2
                isOpen={isOpenModal}
                setIsOpen={setIsOpenModal}
                competencia={!competenciaSelecionada?.competencia ? competenciaDisponivel?.competencia : competenciaSelecionada?.competencia}
                // reset={isReset}
                // tipoLote="cobranca"
                // labelButton="Próximo"
                // setReset={setIsReset}

                // onClickEnviarNotaFiscal={(idLote, numeroNota, valorNota, file) => {
                //     handleClickGerarNota(idLote, numeroNota, valorNota, dataEmissao, file, documentosObrigatorios)
                // }}
                setRefreshList={setRefreshList}
                // fileUpload={fileUpload}
                // setFileUpload={setFileUpload}
            />

            <ModalDocumentosNovoLote
                exigeNF={exigeNF}
                handleSubmit={handleSubmit}
                isOpen={isOpenModalDocumentos}
                idLoteCobranca={idLoteCobranca}
                handleResetState={handleResetState}
                setIsOpen={setIsOpenModalDocumentos}
                documentosObrigatorios={documentosObrigatorios}
                refresheList={() => setRefreshList(!refreshList)}
            />
        </S.Container>
    )
}

export default TabLoteMedico
