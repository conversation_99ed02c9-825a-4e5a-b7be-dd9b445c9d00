/* eslint-disable @typescript-eslint/no-unused-vars */
import styled from 'styled-components'

export const Container = styled.div`
    max-width: 1400px;
    padding: 24px;
    margin: 0 auto;
    width: 95%;
    background-color: #fff;
    border-radius: 8px;
`

export const Title = styled.h2`
    font-weight: 600;
    font-size: 14px;
    line-height: 24px;
    color: #2b45d4;
`

export const FormInput = styled.form`
    gap: 8px;
`

export const ProceduresButtons = styled.div`
    display: flex;
    padding-top: 35px;

    button:first-of-type {
        margin-right: 16px;
    }
`

export const ButtonContainer = styled.div`
    display: flex;
    padding: 25px 0 35px 0;
    justify-content: flex-end;
`

export const ContainerInputs = styled.div`
    .row {
        display: flex;
        margin: 40px 0px;
        gap: 16px;

        .rule-name {
            flex: 1;
        }
        .effective-date {
            flex: 1;
        }
        .medical-bills {
            flex: 1;
        }
        .first-verification {
            flex: 1;
        }
        .second-verification {
            flex: 1;
        }
        .provider {
            flex-basis: 33%;
        }

        @media screen and (max-width: 1024px) {
            flex-direction: column;
            gap: 16px;
            margin: 0 0px 16px 0;

            input,
            label {
                font-size: 1.4rem;
            }
        }
    }
`
