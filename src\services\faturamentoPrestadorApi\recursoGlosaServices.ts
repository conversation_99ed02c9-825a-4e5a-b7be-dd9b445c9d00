import { AxiosRequestConfig, AxiosResponse } from 'axios'
import { IDocumentoQuery } from 'types/cobrancaPrestador/documento'
import { IGuiaGlosaQuery, IPageGuiaGlosa } from 'types/recursoGlosaPrestador/guiaGlosa'
import { IItemGuiaGlosaQuery, IRecursoGlosaCommand } from 'types/recursoGlosaPrestador/itemGuiaGlosa'
import { IItemGuiaGlosaMotivoQuery } from 'types/recursoGlosaPrestador/itemGuiaGlosaMotivo'
import { IResumoSituacaoGuiaGlosaQuery } from 'types/recursoGlosaPrestador/resumoSituacaoGuiaGlosa'
import { ObjectUtils } from 'utils/objectUtils'
import { apiFaturamentoPrestador } from '../apis/apiFaturamentoPrestador'
import { ISolicitacaoEnvioEletronicoDTO } from 'types/cobrancaPrestador/loteCobranca'

export interface IGetGuiaProps {
    page?: number
    size?: number
    loteOuNumeroGuia?: string
}

const baseUrl = '/recurso-glosa'

export class RecursoGlosaServices {
    // static async getDetalhesCompetencia(exercicio: number): Promise<AxiosResponse<IPrestadorAnaliseQuery[]>> {
    //     const idPrestador = ID_PRESTADOR
    //     return axiosApi.get(`${baseUrl}/exercicio/${exercicio}/detalhe-competencia?prestadorId=${idPrestador}&tipoFaturamento=NORMAL`)
    // }

    // static async getResumoMedico(competencia: string): Promise<AxiosResponse<IResumoQuery>> {
    //     const idPrestador = ID_PRESTADOR
    //     return axiosApi.get(`${baseUrl}/resumo?modulo=MEDICO&competencia=${competencia}&prestadorId=${idPrestador}`)
    // }

    // static async getResumoOdonto(competencia: string): Promise<AxiosResponse<IResumoQuery>> {
    //     const idPrestador = ID_PRESTADOR
    //     return axiosApi.get(`${baseUrl}/resumo?modulo=ODONTO&competencia=${competencia}&prestadorId=${idPrestador}`)
    // }

    // static getDemonstrativoProducaoMedico(competencia: string, tipoArquivo: string): string {
    //     return `${process.env.NEXT_PUBLIC_API_FATURAMENTO}${baseUrl}/demonstrativo/producao/download?tipoCobranca=MEDICO&competencia=${competencia}&tipoArquivo=${tipoArquivo}`
    // }

    // static getDemonstrativoPagamentoMedico(competencia: string, tipoArquivo: string): string {
    //     return `${process.env.NEXT_PUBLIC_API_FATURAMENTO}${baseUrl}/demonstrativo/pagamento/download?tipoCobranca=MEDICO&competencia=${competencia}&tipoArquivo=${tipoArquivo}`
    // }

    static async getPorcentagemSituacoes(competencia: string, idPrestador: string): Promise<AxiosResponse<IResumoSituacaoGuiaGlosaQuery[]>> {
        return apiFaturamentoPrestador.get(`${baseUrl}/guias/porcentagem-situacoes?competencia=${competencia}&prestadorId=${idPrestador}`)
    }

    static async getGuias(competencia: string, situacao: string, idPrestador: string, props?: IGetGuiaProps): Promise<AxiosResponse<IPageGuiaGlosa>> {
        const params = ObjectUtils.propsToParams(props)
        const url = props
            ? `${baseUrl}/guias?competencia=${competencia}&situacao=${situacao}&idPrestador=${idPrestador}&${params}`
            : `${baseUrl}/guias?competencia=${competencia}&situacao=${situacao}&idPrestador=${idPrestador}`
        return apiFaturamentoPrestador.get(url)
    }

    static async getGuiaGlosa(id: string): Promise<AxiosResponse<IGuiaGlosaQuery>> {
        // const params = objectToParams(props)
        // const url = props
        // ? `${baseUrl}/guias?competencia=${competencia}&situacao=${situacao}&idPrestador=${idPrestador}&${params}`
        // : `${baseUrl}/guias?competencia=${competencia}&situacao=${situacao}&idPrestador=${idPrestador}`
        return apiFaturamentoPrestador.get(`${baseUrl}/guias/${id}`)
    }

    static async getItensGuiaGlosa(id: string, tipoItem: string): Promise<AxiosResponse<IItemGuiaGlosaQuery[]>> {
        return apiFaturamentoPrestador.get(`${baseUrl}/guias/${id}/itens?tipoItem=${tipoItem}`)
    }

    static async getMotivosItemGuiaGlosa(id: string, idItem: string): Promise<AxiosResponse<IItemGuiaGlosaMotivoQuery[]>> {
        return apiFaturamentoPrestador.get(`${baseUrl}/guias/${id}/itens/${idItem}/motivos`)
    }

    static async putPrimeiroRecursoGlosa(id: string, idItem: string, dados: IRecursoGlosaCommand): Promise<AxiosResponse<IItemGuiaGlosaQuery>> {
        return apiFaturamentoPrestador.put(`${baseUrl}/guias/${id}/itens/${idItem}/primeiro-recurso`, dados)
    }

    static async postEnviarRecurso(id: string): Promise<AxiosResponse<IGuiaGlosaQuery>> {
        return apiFaturamentoPrestador.post(`${baseUrl}/guias/${id}/envio-recurso`)
    }

    // DECREPTED
    static async postLoteEnvioEletronico({
        arquivo,
        options
    }: {
        arquivo: any
        options?: AxiosRequestConfig
    }): Promise<AxiosResponse<ISolicitacaoEnvioEletronicoDTO>> {
        const formData = new FormData()

        formData.append('arquivo', arquivo)

        return apiFaturamentoPrestador.post(`${baseUrl}/lote/envio-eletronico`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            },
            ...options
        })
    }

    static async postDocumentoRecursoGlosa(id: string, idItem: string, arquivo: any): Promise<AxiosResponse<IDocumentoQuery>> {
        const formData = new FormData()

        formData.append('arquivo', arquivo)

        return apiFaturamentoPrestador.post(`${baseUrl}/guias/${id}/itens/${idItem}/documento`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        })
    }

    static async getDocumentosItemGuiaGlosa(id: string, idItem: string): Promise<AxiosResponse<IDocumentoQuery[]>> {
        return apiFaturamentoPrestador.get(`${baseUrl}/guias/${id}/itens/${idItem}/documentos`)
    }

    static getDocumentoRecursoGlosaDownload(idDocumento: string): string {
        return `${process.env.NEXT_PUBLIC_API_FATURAMENTO}${baseUrl}/documentos/${idDocumento}/download`
    }

    // static async getLotesVisaoFechamento(competencia: string, props?: IGetCobrancaProps): Promise<AxiosResponse<IPageLoteFechamento>> {
    //     const idPrestador = ID_PRESTADOR
    //     const params = objectToParams(props)
    //     const url = props
    //         ? `${baseUrl}/lotes/visao-fechamento?modulo=MEDICO&competencia=${competencia}&idPrestador=${idPrestador}&${params}`
    //         : `${baseUrl}/lotes/visao-fechamento?modulo=MEDICO&competencia=${competencia}&idPrestador=${idPrestador}`
    //     return axiosApi.get(url)
    // }

    // static async getLotesVisaoCancelamento(competencia: string, props?: IGetCobrancaProps): Promise<AxiosResponse<IPageLoteCancelamento>> {
    //     const idPrestador = ID_PRESTADOR
    //     const params = objectToParams(props)
    //     const url = props
    //         ? `${baseUrl}/lotes/visao-cancelamento?modulo=MEDICO&competencia=${competencia}&idPrestador=${idPrestador}&${params}`
    //         : `${baseUrl}/lotes/visao-cancelamento?modulo=MEDICO&competencia=${competencia}&idPrestador=${idPrestador}`
    //     return axiosApi.get(url)
    // }

    // static async getLotesVisaoRecusa(competencia: string, props?: IGetCobrancaProps): Promise<AxiosResponse<IPageLoteRecusa>> {
    //     const idPrestador = ID_PRESTADOR
    //     const params = objectToParams(props)
    //     const url = props
    //         ? `${baseUrl}/lotes/visao-recusa?modulo=MEDICO&competencia=${competencia}&idPrestador=${idPrestador}&${params}`
    //         : `${baseUrl}/lotes/visao-recusa?modulo=MEDICO&competencia=${competencia}&idPrestador=${idPrestador}`
    //     return axiosApi.get(url)
    // }

    // static async getLotesVisaoDevolucao(competencia: string, props?: IGetCobrancaProps): Promise<AxiosResponse<IPageLoteDevolucao>> {
    //     const idPrestador = ID_PRESTADOR
    //     const params = objectToParams(props)
    //     const url = props
    //         ? `${baseUrl}/lotes/visao-devolucao?modulo=MEDICO&competencia=${competencia}&idPrestador=${idPrestador}&${params}`
    //         : `${baseUrl}/lotes/visao-devolucao?modulo=MEDICO&competencia=${competencia}&idPrestador=${idPrestador}`
    //     return axiosApi.get(url)
    // }

    // static async postLoteEnvioEletronico({
    //     competencia,
    //     arquivo,
    //     options
    // }: {
    //     competencia: string
    //     arquivo: any
    //     options?: AxiosRequestConfig
    // }): Promise<AxiosResponse<ILotesQuery>> {
    //     const formData = new FormData()

    //     formData.append('arquivoLote', arquivo)

    //     return axiosApi.post(`${baseUrl}/lote/envio-eletronico?modulo=MEDICO&competencia=${competencia}`, formData, {
    //         headers: {
    //             'Content-Type': 'multipart/form-data'
    //         },
    //         ...options
    //     })
    // }

    // static async postLoteNotaFiscal({
    //     idLote,
    //     numeroNota,
    //     valor,
    //     arquivo,
    //     options
    // }: {
    //     idLote: string
    //     numeroNota: string
    //     valor: string
    //     arquivo: any
    //     options?: AxiosRequestConfig
    // }): Promise<AxiosResponse<IDocumentoQuery>> {
    //     const formData = new FormData()

    //     formData.append('arquivo', arquivo)

    //     return axiosApi.post(`${baseUrl}/lote/${idLote}/documento?tipoDocumento=NOTA_FISCAL&numeroNota=${numeroNota}&valor=${valor}`, formData, {
    //         headers: {
    //             'Content-Type': 'multipart/form-data'
    //         },
    //         ...options
    //     })
    // }

    // static async patchCancelamento(idLote): Promise<AxiosResponse<ILotesQuery>> {
    //     return axiosApi.patch(`${baseUrl}/lote/${idLote}/cancelamento`)
    // }

    // static async getLoteDocumento(idLote: string): Promise<AxiosResponse<IDocumentoQuery>> {
    //     return axiosApi.get(`${baseUrl}/lotes/${idLote}/documento`)
    // }
}
