import { AxiosRequestConfig, AxiosResponse } from 'axios'
import { IExercicioQuery } from 'types/analiseContas/exercicio'
import { IDocumentoQuery } from 'types/cobrancaPrestador/documento'
import { INotaFiscalQuery } from 'types/cobrancaPrestador/documentoNotaFiscal'
import { IEnvioManualCommand } from 'types/cobrancaPrestador/envioManual'
import {
    IGetTipoEnvioNfPrestador,
    ILotNfForm,
    ILoteCobrancaDTO,
    IPageLote,
    ISolicitacaoEnvioEletronicoDTO
} from 'types/cobrancaPrestador/loteCobranca'
import { IGetDetalhesCompetenciaProps, IPrestadorAnaliseQuery, IGetCompetenciasDisponiveis } from 'types/cobrancaPrestador/prestadorAnalise'
import { IResumoCobrancaQuery } from 'types/cobrancaPrestador/resumoCobranca'
import { IResumoSituacaoLoteCobrancaQuery } from 'types/cobrancaPrestador/resumoSituacaoLoteCobranca'
import { ILoteCancelamentoQuery, IPageLoteCancelamento } from 'types/cobrancaPrestador/visaoCancelamento'
import { IPageLoteDevolucao } from 'types/cobrancaPrestador/visaoDevolucao'
import { IPageLoteFechamento } from 'types/cobrancaPrestador/visaoFechamento'
import { IPageLoteRecusa } from 'types/cobrancaPrestador/visaoRecusa'
import { ObjectUtils } from 'utils/objectUtils'
import { unMaskDate } from 'utils/dateUtils'
import { apiFaturamentoPrestador } from '../apis/apiFaturamentoPrestador'
import { IVinculoNotaFiscalProps } from 'types/cobrancaPrestador/vinculoNotaFiscal'
import { TipoArquivoModeloCSV, TipoDocumento } from 'types/common/enums'
import { IValoresDeLotesApresentadosDTO } from 'types/cobrancaPrestador/valoresDeLotesApresentado'
import { ICompetenciaCalendarioDTO, IGetPropsCalendarioEnvioProps } from 'types/cobrancaPrestador/competenciaEnvio'
import { IGetAbertoEnvioLote, IGetAbertoEnvioLoteDto } from 'types/cobrancaPrestador/envioLotes'
import { ModuloEnum } from './geracao-recurso-glosa/enuns'

export interface IGetCobrancaProps {
    page?: number
    size?: number
    filtroNumeroLote?: string
    modulo?: ModuloEnum
}

export enum situacaoLoteEnum {
    AGUARDANDO_DOCUMENTACAO = 'AGUARDANDO_DOCUMENTACAO',
    PROCESSANDO = 'PROCESSANDO',
    EM_AUDITORIA = 'EM_AUDITORIA',
    EM_ANALISE = 'EM_ANALISE',
    FECHADO = 'FECHADO',
    CANCELADO = 'CANCELADO',
    RECUSADO = 'RECUSADO',
    DEVOLVIDO = 'DEVOLVIDO'
}

const baseUrl = '/cobranca'

export class CobrancaServices {
    static async getDetalhesCompetencia(props: IGetDetalhesCompetenciaProps): Promise<AxiosResponse<IPrestadorAnaliseQuery[]>> {
        // const params = ObjectUtils.propsToParams(props)
        // const url = props
        //     ? `${baseUrl}/lotes?modulo=MEDICO&competencia=${competencia}&situacaoLote=${situacao}&idPrestador=${idPrestador}&${params}`
        //     : `${baseUrl}/lotes?modulo=MEDICO&competencia=${competencia}&situacaoLote=${situacao}&idPrestador=${idPrestador}`

        return apiFaturamentoPrestador.get(
            `${baseUrl}/exercicio/${props.exercicio}/detalhe-competencia?prestadorId=${props.idPrestador}&tipoFaturamento=${props.tipoFaturamento}`
            // `${baseUrl}/exercicio/${props.exercicio}/detalhe-competencia?prestadorId=${props.idPrestador}&tipoFaturamento=NORMAL`
        )
    }

    static async getCompetenciasDisponiveis(props: IGetCompetenciasDisponiveis): Promise<AxiosResponse<IPrestadorAnaliseQuery[]>> {
        return apiFaturamentoPrestador.get(
            `${baseUrl}/competencia/disponiveis?prestadorId=${props.idPrestador}&competencia=${props.competencia}&modulo=${props.modulo}`
        )
    }

    static async getAbertoParaEnvioLote(props: IGetAbertoEnvioLote): Promise<AxiosResponse<IGetAbertoEnvioLoteDto>> {
        return apiFaturamentoPrestador.get(
            `${baseUrl}/aberto-para-envio-lote?prestadorId=${props.prestadorId}&competencia=${props.competencia}&modulo=${props.modulo}`
        )
    }

    static async getExercicios(idPrestador: string): Promise<AxiosResponse<IExercicioQuery[]>> {
        const url = `${baseUrl}/competencia/exercicios?prestadorUuid=${idPrestador}`
        return apiFaturamentoPrestador.get(url)
    }

    static async getResumoMedico(competencia: string, idPrestador: string): Promise<AxiosResponse<IResumoCobrancaQuery>> {
        return apiFaturamentoPrestador.get(`${baseUrl}/resumo?modulo=MEDICO&competencia=${competencia}&prestadorId=${idPrestador}`)
    }

    static async getResumoOdonto(competencia: string, idPrestador: string): Promise<AxiosResponse<IResumoCobrancaQuery>> {
        return apiFaturamentoPrestador.get(`${baseUrl}/resumo?modulo=ODONTO&competencia=${competencia}&prestadorId=${idPrestador}`)
    }

    static getDemonstrativoProducaoPrestador(competencia: string, tipoArquivo: string, idPrestador: string): string {
        return `${process.env.NEXT_PUBLIC_API_FATURAMENTO}${baseUrl}/demonstrativo/producao/download?competencia=${competencia}&prestadorId=${idPrestador}&tipoArquivo=${tipoArquivo}`
    }

    static getDemonstrativoProducaoLote(idLote: string, tipoArquivo: string): string {
        return `${process.env.NEXT_PUBLIC_API_FATURAMENTO}${baseUrl}/demonstrativo/producao/download?idLote=${idLote}&tipoArquivo=${tipoArquivo}`
    }

    static getDemonstrativoPagamentoMedico(competencia: string, tipoArquivo: string): string {
        return `${process.env.NEXT_PUBLIC_API_FATURAMENTO}${baseUrl}/demonstrativo/pagamento/download?tipoCobranca=MEDICO&competencia=${competencia}&tipoArquivo=${tipoArquivo}`
    }

    static async getPorcentagemSituacoes(competencia: string, idPrestador: string): Promise<AxiosResponse<IResumoSituacaoLoteCobrancaQuery[]>> {
        return apiFaturamentoPrestador.get(
            `${baseUrl}/lotes/porcentagem-situacoes?tipoCobranca=MEDICO&competencia=${competencia}&prestadorId=${idPrestador}`
        )
    }

    static async getLotes(competencia: string, idPrestador: string, situacao?: string, props?: IGetCobrancaProps): Promise<AxiosResponse<IPageLote>> {
        const params = ObjectUtils.removeEmptyParams(props)
        const url = `${baseUrl}/lotes?competencia=${competencia}&situacaoLote=${situacao}&idPrestador=${idPrestador}&${params}`
        return apiFaturamentoPrestador.get(url)
    }

    static async getLotesVisaoFechamento(
        competencia: string,
        idPrestador: string,
        props?: IGetCobrancaProps
    ): Promise<AxiosResponse<IPageLoteFechamento>> {
        const params = ObjectUtils.propsToParams(props)
        const url = props
            ? `${baseUrl}/lotes/visao-fechamento?modulo=MEDICO&competencia=${competencia}&idPrestador=${idPrestador}&${params}`
            : `${baseUrl}/lotes/visao-fechamento?modulo=MEDICO&competencia=${competencia}&idPrestador=${idPrestador}`
        return apiFaturamentoPrestador.get(url)
    }

    static async getLotesVisaoCancelamento(
        competencia: string,
        idPrestador: string,
        props?: IGetCobrancaProps
    ): Promise<AxiosResponse<IPageLoteCancelamento>> {
        const params = ObjectUtils.propsToParams(props)
        const url = props
            ? `${baseUrl}/lotes/visao-cancelamento?modulo=MEDICO&competencia=${competencia}&idPrestador=${idPrestador}&${params}`
            : `${baseUrl}/lotes/visao-cancelamento?modulo=MEDICO&competencia=${competencia}&idPrestador=${idPrestador}`
        return apiFaturamentoPrestador.get(url)
    }

    static async getLotesVisaoRecusa(competencia: string, idPrestador: string, props?: IGetCobrancaProps): Promise<AxiosResponse<IPageLoteRecusa>> {
        const params = ObjectUtils.removeEmptyParams(props)
        const url = `${baseUrl}/lotes/visao-recusa?competencia=${competencia}&idPrestador=${idPrestador}&${params}`
        return apiFaturamentoPrestador.get(url)
    }

    static async getLotesVisaoDevolucao(
        competencia: string,
        idPrestador: string,
        props?: IGetCobrancaProps
    ): Promise<AxiosResponse<IPageLoteDevolucao>> {
        const params = ObjectUtils.propsToParams(props)
        const url = props
            ? `${baseUrl}/lotes/visao-devolucao?modulo=MEDICO&competencia=${competencia}&idPrestador=${idPrestador}&${params}`
            : `${baseUrl}/lotes/visao-devolucao?modulo=MEDICO&competencia=${competencia}&idPrestador=${idPrestador}`
        return apiFaturamentoPrestador.get(url)
    }

    static async postLoteEnvioEletronico({
        competencia,
        arquivo,
        options
    }: {
        competencia: string
        arquivo: any
        options?: AxiosRequestConfig
    }): Promise<AxiosResponse<ISolicitacaoEnvioEletronicoDTO>> {
        const formData = new FormData()

        formData.append('arquivoLote', arquivo)

        return apiFaturamentoPrestador.post(`${baseUrl}/lote/envio-eletronico?modulo=MEDICO&competencia=${competencia}`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            },
            ...options
        })
    }

    static async postEnvioEletronicoLoteComNf({
        competencia,
        loteXMLs,
        notaFiscal,
        docComp,
        options
    }: {
        competencia: string
        loteXMLs: File[]
        notaFiscal?: ILotNfForm
        docComp: File[]
        options?: AxiosRequestConfig
    }): Promise<AxiosResponse<ISolicitacaoEnvioEletronicoDTO[]>> {
        const formData = new FormData()

        loteXMLs?.forEach((file) => formData.append('arquivosLotes', file))
        docComp?.forEach((file) => formData.append('arqsDocsComplementares', file))
        notaFiscal?.nfFiles?.forEach((file) => formData.append('arquivosNotaFiscal', file))

        return apiFaturamentoPrestador.post(`${baseUrl}/lote/envio-lote-eletronico`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            },
            params: {
                modulo: 'MEDICO',
                competencia,
                ...(notaFiscal?.conciliarNotaFiscal
                    ? { conciliarNotaFiscal: true, numeroNotaFiscalConciliacao: notaFiscal?.numeroNota }
                    : {
                          numeroNota: notaFiscal?.numeroNota,
                          valor: notaFiscal?.valor,
                          servicoPrestado: notaFiscal?.servicoPrestado,
                          dataEmissao: notaFiscal?.dataEmissao
                      })
            },
            ...options
        })
    }

    static async postNotaFiscalParaLotes({
        idLotes,
        numeroNota,
        valor,
        dataEmissao,
        arquivos,
        options
    }: {
        idLotes: string[]
        numeroNota: string
        valor: string
        dataEmissao: string
        arquivos: File[]
        options?: AxiosRequestConfig
    }): Promise<AxiosResponse<IDocumentoQuery>> {
        const formData = new FormData()

        arquivos?.forEach((file) => formData.append('arquivos', file))

        const loteParametros = idLotes.map((idLote) => {
            return `lote=${idLote}`
        })

        return apiFaturamentoPrestador.post(
            `${baseUrl}/lote/documento?${loteParametros.join(
                '&'
            )}&tipoDocumento=NOTA_FISCAL&numeroNota=${numeroNota}&valor=${valor}&dataEmissao=${unMaskDate(dataEmissao)}`,
            formData,
            {
                headers: {
                    'Content-Type': 'multipart/form-data'
                },
                ...options
            }
        )
    }

    static async postDocumentos({
        idLote,
        arquivos,
        tipoDocumento
    }: {
        idLote: string[]
        arquivos: File[]
        tipoDocumento: TipoDocumento
    }): Promise<AxiosResponse<IDocumentoQuery>> {
        const formData = new FormData()

        arquivos?.forEach((file) => formData.append('arquivos', file))

        return apiFaturamentoPrestador.post(`${baseUrl}/lote/documento?lote=${idLote}&tipoDocumento=${tipoDocumento}`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        })
    }

    static async postDocumentosAuditoria({
        idLote,
        arquivo,
        options
    }: {
        idLote: string
        arquivo: any
        options?: AxiosRequestConfig
    }): Promise<AxiosResponse<IDocumentoQuery>> {
        const formData = new FormData()

        formData.append('arquivo', arquivo)

        return apiFaturamentoPrestador.post(`${baseUrl}/lote/documento?lote=${idLote}&tipoDocumento=DOCUMENTACAO_DIVERSA`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            },
            ...options
        })
    }

    static async postLoteEnvioManual(data: IEnvioManualCommand): Promise<AxiosResponse<ILoteCobrancaDTO>> {
        return apiFaturamentoPrestador.post(`${baseUrl}/lote/geracao-manual`, data)
    }

    static async postVincularLoteNotaFiscal(props: IVinculoNotaFiscalProps): Promise<AxiosResponse<IDocumentoQuery>> {
        const params = ObjectUtils.propsToParams(props)
        return apiFaturamentoPrestador.post(`${baseUrl}/lote/nota-fiscal?${params}`)
    }

    static async patchCancelamento(idLote): Promise<AxiosResponse<ILoteCancelamentoQuery>> {
        return apiFaturamentoPrestador.patch(`${baseUrl}/lote/${idLote}/cancelamento`)
    }

    ////
    // static async postLoteEnvioEletronico(data: IEnvioEletronicoCommand): Promise<AxiosResponse<IEnvioEletronicoQuery>> {
    //     return axiosApi.post(`${baseUrl}/lotes/envio-eletronico`, data)
    // }

    // static async getLoteVisaoRecusa(competencia: string, props?: IGetCobrancaProps): Promise<AxiosResponse<IVisaoRecusaQuery>> {
    //     const params = objectToParams(props)
    //     const url = props ? `${baseUrl}/lotes/visao-recusa?${params}` : baseUrl
    //     return axiosApi.get(url)
    // }

    // static async getLoteVisaoDevolucao(competencia: string, props?: IGetCobrancaProps): Promise<AxiosResponse<IVisaoDevolucaoQuery>> {
    //     const params = objectToParams(props)
    //     const url = props ? `${baseUrl}/lotes/visao-devolucao?${params}` : baseUrl
    //     return axiosApi.get(url)
    // }

    // static async getLoteVisaoCancelamento(competencia: string, props?: IGetCobrancaProps): Promise<AxiosResponse<IVisaoCancelamentoQuery>> {
    //     const params = objectToParams(props)
    //     const url = props ? `${baseUrl}/lotes/visao-cancelamento?${params}` : baseUrl
    //     return axiosApi.get(url)
    // }

    static async getLoteDocumento(idLote: string): Promise<AxiosResponse<IDocumentoQuery>> {
        return apiFaturamentoPrestador.get(`${baseUrl}/lotes/${idLote}/documento`)
    }

    static async getNotasFiscalPorNumero(numero: string): Promise<AxiosResponse<INotaFiscalQuery>> {
        return apiFaturamentoPrestador.get(`${baseUrl}/notas-fiscais/${numero}`)
    }

    static async getNotasFiscais(competencia: string, idPrestador: string): Promise<AxiosResponse<INotaFiscalQuery[]>> {
        return apiFaturamentoPrestador.get(`${baseUrl}/notas-fiscais?competencia=${competencia}&idPrestador=${idPrestador}`)
    }

    static getProtocoloRecebimentoXml(idLote: string): Promise<AxiosResponse<Blob>> {
        return apiFaturamentoPrestador.get(`${baseUrl}/lotes/${idLote}/protocolo-recebimento/xml/download`)
    }

    static getProtocoloRecebimentoPdf(idLote: string): Promise<AxiosResponse<Blob>> {
        return apiFaturamentoPrestador.get(`${baseUrl}/lotes/${idLote}/protocolo-recebimento/pdf/download`, { responseType: 'blob' })
    }

    static getNotaFiscalDownloadPorLote(idLote: string): Promise<AxiosResponse<Blob>> {
        return apiFaturamentoPrestador.get(`${baseUrl}/lotes/${idLote}/nota-fiscal/download`, { responseType: 'blob' })
    }

    static getNotaFiscalDownload(idNotaFiscal: string): Promise<AxiosResponse<Blob>> {
        return apiFaturamentoPrestador.get(`${baseUrl}/notas-fiscais/${idNotaFiscal}/download`, { responseType: 'blob' })
    }

    static getDemonstrativoAnaliseXml(idLote: string): Promise<AxiosResponse<Blob>> {
        return apiFaturamentoPrestador.get(`${baseUrl}/lotes/${idLote}/processamento/demonstrativo/xml/download`)
    }

    static getDemonstrativoAnaliseXlsx(idLote: string): Promise<AxiosResponse<Blob>> {
        return apiFaturamentoPrestador.get(`${baseUrl}/lotes/${idLote}/processamento/demonstrativo/xlsx/download`)
    }

    static getDemonstrativoAnalisePdf(idLote: string): Promise<AxiosResponse<Blob>> {
        return apiFaturamentoPrestador.get(`${baseUrl}/lotes/${idLote}/processamento/demonstrativo/pdf/download`, { responseType: 'blob' })
    }

    static getRelatorioErrosPdf(idLote: string): Promise<AxiosResponse<Blob>> {
        return apiFaturamentoPrestador.get(`${baseUrl}/lotes/${idLote}/processamento/relatorio-erros/pdf/download`, { responseType: 'blob' })
    }

    static getModeloCSVGeracaoManualLote(tipoArquivo: TipoArquivoModeloCSV): Promise<AxiosResponse<Blob>> {
        return apiFaturamentoPrestador.get(`${baseUrl}/lote/geracao-manual/modelo`, { responseType: 'blob', params: { tipoArquivo: tipoArquivo } })
    }

    // static getRelatorioErrosXlsx(idLote: string): string {
    //     return `${process.env.NEXT_PUBLIC_API_FATURAMENTO}${baseUrl}/lotes/${idLote}/processamento/relatorio-erros/xlsx/download`
    // }

    static async getValorApresentado(competencia: string, idPrestador: string): Promise<AxiosResponse<IValoresDeLotesApresentadosDTO>> {
        return apiFaturamentoPrestador.get(`${baseUrl}/valor-apresentado?modulo=MEDICO&competencia=${competencia}&prestadorId=${idPrestador}`)
    }

    static async getCompetenciasEnvio(props?: IGetPropsCalendarioEnvioProps): Promise<AxiosResponse<ICompetenciaCalendarioDTO[]>> {
        const params = ObjectUtils.propsToParams(props)
        const url = props ? `${baseUrl}/competencia/competencias-envio?${params}` : `${baseUrl}/competencia/competencias-envio`
        return apiFaturamentoPrestador.get(url)
    }

    static getDemonstrativoPagamentoXlsx(props: any): Promise<AxiosResponse<Blob>> {
        const params = ObjectUtils.propsToParams(props)

        return apiFaturamentoPrestador.get(`${baseUrl}/processamento/demonstrativo-analise-contas/download?${params}`, {
            responseType: 'blob'
        })
    }

    static getDemonstrativoPagamentoXML(props: any): Promise<AxiosResponse<Blob>> {
        const params = ObjectUtils.propsToParams(props)

        return apiFaturamentoPrestador.get(`${baseUrl}/processamento/demonstrativo-analise-contas/download?${params}`, {
            responseType: 'blob'
        })
    }

    static async getExtratoDisponivel(exercicio: string, idPrestador: string, tipoFaturamento: string): Promise<AxiosResponse> {
        return apiFaturamentoPrestador.get(
            `${baseUrl}/exercicio/${exercicio}/detalhe-competencia?prestadorId=${idPrestador}&tipoFaturamento=${tipoFaturamento}`
        )
    }

    static async getLotesSimulacao(
        competencia: string,
        idPrestador: string,
        situacao?: string,
        props?: IGetCobrancaProps
    ): Promise<AxiosResponse<IPageLote>> {
        return apiFaturamentoPrestador.get(`${baseUrl}/lotes-simulacao`, {
            params: {
                modulo: 'MEDICO',
                competencia: competencia,
                situacaoLote: situacao,
                idPrestador: idPrestador,
                ...props
            }
        })
    }

    static async postLoteEnvioEletronicoSimulacao({
        competencia,
        arquivo,
        options
    }: {
        competencia: string
        arquivo: File
        options?: AxiosRequestConfig
    }): Promise<AxiosResponse<ISolicitacaoEnvioEletronicoDTO>> {
        const formData = new FormData()

        formData.append('arquivoLote', arquivo)

        return apiFaturamentoPrestador.post(`${baseUrl}/lote/envio-eletronico/simulacao`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            },
            params: {
                modulo: 'MEDICO',
                competencia: competencia
            },
            ...options
        })
    }

    static async getTipoEnvioNfPrestador(competencia: string): Promise<AxiosResponse<IGetTipoEnvioNfPrestador>> {
        return apiFaturamentoPrestador.get(`${baseUrl}/prestador/tipo-envio-nota-fiscal`, {
            params: {
                competencia: competencia
            }
        })
    }
}
