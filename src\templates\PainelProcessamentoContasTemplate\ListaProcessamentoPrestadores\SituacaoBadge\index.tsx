import { SituacaoProcessamentoLoteEnum } from 'src/services/processadorRegrasApi/RegistrosProcessamentoContas/enums'
import { SituacaoLote } from 'types/common/enums'
import * as S from './styles'

type SituacaoBadgeProps = {
    situacao: SituacaoProcessamentoLoteEnum | SituacaoLote
}
const SituacaoBadge = ({ situacao }: SituacaoBadgeProps) => {
    return (
        <S.Container badgeTheme={situacao}>
            <p>
                {{
                    [SituacaoProcessamentoLoteEnum.FALHA]: 'Falha',
                    [SituacaoProcessamentoLoteEnum.AGENDADO]: 'Agendado',
                    [SituacaoProcessamentoLoteEnum.PROCESSADO]: 'Processado',
                    [SituacaoProcessamentoLoteEnum.EM_PROCESSAMENTO]: 'Em processamento',
                    [SituacaoLote.RECUSADO]: 'Recusado',
                    [SituacaoLote.CANCELADO]: 'Cancelado'
                }[situacao] || situacao}
            </p>
        </S.Container>
    )
}

export default SituacaoBadge
