/* eslint-disable prettier/prettier */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import Input from 'components/atoms/Inputs/Input'
import CardBeneficiary from 'components/molecules/Cards/CardBeneficiary'

import React, { useState, useEffect } from 'react'
import { getInsured } from 'src/services/contratoApi/seguradoServices'
import { Beneficiary } from 'src/types/faturamentoOperadora/beneficiary'
import * as S from './styles'

type BeneficiarySearchProps = {
    label?: string
    onFocus?: any
    onClick: (data: any) => void
    setValue?: any
    value: string
    className?: string
}

const BeneficiarySearch = ({ label = 'Insira o texto aqui', onFocus, setValue, value, onClick, className }: BeneficiarySearchProps) => {
    const [valueState, setValueState] = useState('')
    const [dataBeneficiary, setDataBeneficiary] = useState<Beneficiary[]>()
    const [loading, setLoading] = useState(false)
    const refElement = React.useRef<any>(null)
    setValue(valueState)

    // FUNÇÃO PARA FECHAR MODAL DE BENEFICIÁRIOS AO CLICAR DO LADO DE FORA
    function handleClickOutside(event: any) {
        if (refElement.current && !refElement.current?.contains(event.target)) {
            setValueState('')
        }
    }

    React.useEffect(() => {
        document.addEventListener('mousedown', handleClickOutside)
        return () => {
            document.removeEventListener('mousedown', handleClickOutside)
        }
    }, [refElement])

    //   useEffect(() => {
    //     //criando lista com todos o beneficiarios, titulares e dependentes
    //     const auxList: any[] = [];
    //     dataBeneficiary?.forEach((beneficiary: Holder) => {
    //       auxList?.push(beneficiary);
    //       if (beneficiary.dependentes !== undefined) {
    //         beneficiary.dependentes.forEach((dependent: Dependents) => {
    //           auxList.push(dependent);
    //         });
    //       }
    //     });
    //     //filtrando lista, deixando somente os beneficiarios que contém no seu cpf o valor informado
    //     // const filterClients = auxList?.filter(
    //     //   (data: any) =>
    //     //     data?.cpf?.toUpperCase().indexOf(valueState.toUpperCase()) > -1
    //     // );
    //     // setando a varivel que contem a lista dos benef filtrados
    //     setListbeneficiaryFilter(auxList);
    //     // Desativando o spinner de loading
    //     setLoading(false);
    //   }, [dataBeneficiary]);

    //Realizando uma busca na api toda vez que o valor do input for alterado
    useEffect(() => {
        //verificando se o input não esta em branco
        if (value?.length >= 3) {
            // Ativando o spinner de loading
            setLoading(true)
            //Realizando uma busca na api de beneficiarios
            getInsured(value, 0, 10).then((response: any) => {
                if (response?.data !== undefined) {
                    const data = response?.data.content
                    setDataBeneficiary(data)
                    console.log(data)
                    // Desativando o spinner de loading
                    setLoading(false)
                } else {
                    //setando a variavel que contem todo os beneficiarios retornados pela api
                    setDataBeneficiary([])
                    // Desativando o spinner de loading
                    setLoading(false)
                }
            })
        }
    }, [value])
    return (
        <S.Wrapper className={className}>
            <Input
                label={label}
                isDefault={'default'}
                value={valueState}
                handleOnFocus={onFocus}
                handleOnChange={(value: string) => setValueState(value)}
                loading={loading}
                type="number"
                maxLength={20}
            />

            {/* OPTIONS */}

            {valueState && (
                <S.Background>
                    <S.Options ref={refElement}>
                        {dataBeneficiary?.map((beneficiary: Beneficiary, index: any) => (
                            <div key={index}>
                                <hr></hr>
                                <CardBeneficiary
                                    key={index}
                                    holder={beneficiary.nome}
                                    type={beneficiary.tipoSegurado}
                                    cpf={beneficiary.cpfCnpj}
                                    plan={''}
                                    onClick={() => {
                                        onClick(beneficiary)
                                    }}
                                />
                            </div>
                        ))}

                        {dataBeneficiary?.length === 0 && (
                            <CardBeneficiary
                            holder={'Nenhum beneficiário encontrado'}
                            type={''}
                            cpf={''}
                            plan={''}                            
                        />
                        )}
                    </S.Options>
                </S.Background>
            )}
        </S.Wrapper>
    )
}

export default BeneficiarySearch

