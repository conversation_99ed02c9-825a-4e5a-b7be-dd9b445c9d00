import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react'
import { useRouter } from 'next/router'
import Button from 'components/atoms/Button'
import { Button as ButtonMui, Checkbox, FormControl, FormControlLabel, InputLabel } from '@mui/material'
import Layout from 'components/molecules/Layout'
import SearchBar from 'components/molecules/SearchBar'
import TitleSection from 'components/atoms/TitleSection'
import Pagination from 'components/molecules/Pagination'
import ContentDataInfo from 'components/molecules/ShowDataConfirm'
import DividerSectionCard from 'components/atoms/DividerSectionCard'
import * as S from './styles'
import { IPagination } from 'types/common/pagination'
import { AnaliseContasService } from 'src/services/analiseContasApi/analiseContasServices'
import { DateUtils } from 'utils/dateUtils'
import { IResumoQuery } from 'types/analiseContas/resumo'
import { NumberUtils } from 'utils/numberUtils'
import { IGetPropsPrestador, IPagePrestador, IPrestadorQuery, IQuantidadePrestadoresSituacaoQuery } from 'types/analiseContas/prestador'
import { PaginationHelper } from 'utils/helpers/paginationHelper'
import { useToast } from 'src/hooks/toast'
import { IExercicioQuery } from 'types/analiseContas/exercicio'
import { ICompetenciaQuery, IValidaEncerramentoCompetencia } from 'types/analiseContas/competencia'
import CardSliderCompetencias from 'components/molecules/CardSliderCompetencias'
import { capitalize, getMessageErrorFromApiResponse, StringUtils } from 'utils/stringUtils'
import { SituacaoPrestadorAnalise, SituacaoPrestadorAnaliseDeContas } from 'types/common/enums'
import moment from 'moment'
import Item from 'components/atoms/Item'
import InfoCard from 'components/molecules/InfoCard'
import { CobrancaService } from 'src/services/analiseContasApi/cobrancaServices'
import { SituacaoModuloEnum } from 'utils/enum/tipo-modulo'
import NoContent from 'components/molecules/NoContent'
import Box from '@mui/material/Box'
import CircularProgress from '@mui/material/CircularProgress'
import ModalGenericComponent from 'components/organisms/ModalGeneric'
import Badge from 'components/atoms/Badge'
import { ReactSVG } from 'react-svg'
import { MenuItem, Select } from '@mui/material'
import { ISortPage } from 'types/pagination'
import { pl } from 'date-fns/locale'
import { FechamentoCompetencia } from 'src/services/analiseContasApi/fechamento-competencia'
import { GlosaBiometria } from 'src/services/analiseContasApi/glosa-biometria'
import dynamic from 'next/dynamic'

const AnimatedLoadingLottie = dynamic(() => import('components/atoms/AnimatedLoadingLottie'), { ssr: false })

type newFilter = {
    categoriaPrestador: 'TODOS' | 'CREDENCIADO' | 'EVENTUAL'
    tipoProcesso: 'TODOS' | 'REGULAR' | 'LIMINAR' | 'EXCEPCIONALIDADE'
    loteSemGlosa: boolean
    loteComGlosa: boolean
}

const AnaliseContasTemplate = () => {
    const currentYear = new Date().getFullYear()
    const router = useRouter()

    const { addToast } = useToast()

    const FILTER_STORAGE_KEY = '@filters:analise-contas:main'

    const defaultFilters = {
        categoriaPrestador: 'TODOS' as const,
        tipoProcesso: 'TODOS' as const,
        loteComGlosa: false,
        loteSemGlosa: false
    }

    const loadFiltersFromStorage = () => {
        try {
            const stored = sessionStorage.getItem(FILTER_STORAGE_KEY)
            return stored ? JSON.parse(stored) : defaultFilters
        } catch (error) {
            console.error('Erro ao carregar filtros:', error)
            return defaultFilters
        }
    }

    const saveFiltersToStorage = (filters: newFilter) => {
        try {
            sessionStorage.setItem(FILTER_STORAGE_KEY, JSON.stringify(filters))
        } catch (error) {
            console.error('Erro ao salvar filtros:', error)
        }
    }

    const clearFiltersFromStorage = () => {
        try {
            sessionStorage.removeItem(FILTER_STORAGE_KEY)
        } catch (error) {
            console.error('Erro ao limpar filtros:', error)
        }
    }
    const [showFilter, setShowFilter] = useState(false)
    const [loadingProviders, setLoadingProviders] = useState(false)
    const [loadingEncerrar, setLoadingEncerrar] = useState(false)
    const [loadingEnviarFinanceiro, setLoadingEnviarFinanceiro] = useState(false)
    const [isLoadingCompetenciaSelecionada, setIsLoadingCompetenciaSelecionada] = useState(false)
    const [paymentProcess, setPaymentProcess] = useState(false)
    const [validToCloseInfo, setValidToCloseInfo] = useState<IValidaEncerramentoCompetencia>()
    const [loadingInfo, setLoadingInfo] = useState(false)
    const [showInfo, setShowInfo] = useState(false)
    const [finishedProcess, setFinishedProcess] = useState(false)
    const [competencias, setCompetencias] = useState<ICompetenciaQuery[]>()
    const [anoExercicio, setAnoExercicio] = useState<number>()
    const [competenciaSelecionada, setCompetenciaSelecionada] = useState<ICompetenciaQuery>()
    const [resumo, setResumo] = useState<IResumoQuery>()
    const [anos, setAnos] = useState<IExercicioQuery[]>([])
    const [prestadoresPage, setPrestadoresPage] = useState<IPagePrestador>()
    const [pagination, setPagination] = useState<IPagination>()
    const [numberPage, setNumberPage] = useState<number>(0)
    const [filtro, setFiltro] = useState<string>('')
    const [newFilter, setNewFilter] = useState<newFilter>(loadFiltersFromStorage)

    const [tabSelected, setTabSelected] = useState<SituacaoPrestadorAnaliseDeContas>()
    const [resumoPrestadoresPorSituacao, setResumoPrestadoresPorSituacao] = useState<IQuantidadePrestadoresSituacaoQuery>()
    const [dataEnvio, setDataEnvio] = useState<string>()

    const initialValueSelect = useMemo(() => anoExercicio && { label: anoExercicio.toString(), value: anoExercicio.toString() }, [anoExercicio])
    const anosExercicio = useMemo(() => anos.map(({ ano }) => ({ label: ano.toString(), value: ano.toString() })), [anos])

    const [btnComposicaoDisabled, setBtnComposicaoDisabled] = useState(false)
    const [modalComposicao, setModalComposicao] = useState(false)

    const faseCompetencia: 'PROCESSAMENTO_DE_CONTAS_EM_ANDAMENTO' | 'ANALISE_DE_CONTAS_ENCERRADA' | 'ANALISE_DE_CONTAS_FECHADA' =
        'PROCESSAMENTO_DE_CONTAS_EM_ANDAMENTO'

    const resetValues = useCallback(() => {
        setCompetencias(undefined)
        setFiltro(undefined)
        setPagination(undefined)
        setNumberPage(0)
        setResumo(undefined)
        setPrestadoresPage(undefined)
        setCompetenciaSelecionada(undefined)
        setFinishedProcess(false)
        setLoadingEnviarFinanceiro(false)
        setTabSelected(undefined)
    }, [])

    useEffect(() => {
        AnaliseContasService.getExercicios().then(({ data }) => {
            const exercicios = data.sort((a, b) => b.ano - a.ano)
            setAnos(exercicios)
            setAnoExercicio(exercicios?.find((e) => e.ano === currentYear)?.ano || exercicios?.[0]?.ano)
        })
    }, [])

    const getCompetencias = useCallback(() => {
        if (!anoExercicio || competencias) return
        AnaliseContasService.getCompetencias(anoExercicio).then(({ data }) => {
            const sortCompetencias = data.sort((a, b) => moment(a.competencia).diff(b.competencia))

            setCompetencias(sortCompetencias)

            const selecionada = sortCompetencias[sortCompetencias.length - 1]
            const competenciaLocal = getCompetenciaIfNotExpired()

            if (competenciaLocal) {
                setCompetenciaSelecionada(competenciaLocal)
            } else {
                setCompetenciaSelecionada(selecionada)
            }

            setFinishedProcess(selecionada.situacao === SituacaoPrestadorAnalise.ENCERRADO)

            CobrancaService.getDatasCobranca({
                competencia: selecionada.competencia,
                modulo: SituacaoModuloEnum.MEDICO
                // prestadorId: ID_PRESTADOR
            }).then(({ data }) => {
                setDataEnvio(data?.dataLimiteEnvio)
            })
        })
    }, [anoExercicio])

    useEffect(() => {
        if (!competenciaSelecionada) return
        AnaliseContasService.validaEncerramento(competenciaSelecionada.competencia).then((data) => {
            setValidToCloseInfo({
                ...data
            })
        })
    }, [competenciaSelecionada])

    const handleReabrir = useCallback(() => {
        AnaliseContasService.putReabrirCompetencia(competenciaSelecionada.competencia)
            .then(() => getCompetencias())
            .catch(() => {
                addToast({ type: 'error', title: 'Erro ao reabrir competencia' })
            })
    }, [competenciaSelecionada, getCompetencias])

    const onChangeAnoExercicio = useCallback(
        (ano) => {
            resetValues()
            setAnoExercicio(ano)
        },
        [setAnoExercicio]
    )

    useEffect(() => {
        setShowInfo(false)
        if (!competenciaSelecionada) return

        setIsLoadingCompetenciaSelecionada(true)

        AnaliseContasService.getQuantidadePrestadoresPorSituacao(competenciaSelecionada.competencia)
            .then(({ data }) => {
                setResumoPrestadoresPorSituacao(data)
            })
            .catch(() => {
                addToast({ type: 'error', title: 'Erro ao carregar resumo de prestadores por situação' })
            })

        handleGetResumo()
    }, [competenciaSelecionada])

    useEffect(() => {
        setIsLoadingCompetenciaSelecionada(false)
    }, [resumo])

    const carregarPrestadores = useCallback(
        (page?: number, filter?: string, isSearch?: boolean, newFilter?: newFilter) => {
            if (!competenciaSelecionada) return

            const getProps: IGetPropsPrestador & ISortPage = {
                competencia: competenciaSelecionada?.competencia,
                categoriaPrestador: newFilter?.categoriaPrestador === 'TODOS' ? null : newFilter?.categoriaPrestador,
                tipoProcesso: newFilter?.tipoProcesso === 'TODOS' ? null : newFilter?.tipoProcesso,
                lotesComGlosa: newFilter?.loteComGlosa,
                lotesSemGlosa: newFilter?.loteSemGlosa,
                size: 10,
                page: isSearch || !page ? 0 : page,
                ...(filter && { nomePrestador: filter }),
                ...(tabSelected && { situacaoPrestador: tabSelected })
            }

            setLoadingProviders(true)

            AnaliseContasService.getPrestador(getProps)
                .then(async ({ data }) => {
                    setPrestadoresPage(data)

                    const objectPagination = PaginationHelper.parserPagination<IPrestadorQuery>(data, setNumberPage)
                    setPagination(objectPagination)

                    // if (isSearch) setTabSelected(undefined)
                })
                .catch((response) =>
                    addToast({
                        title: 'Erro ao carregar prestadores: ' + response?.data?.message,
                        type: 'error',
                        duration: 4000
                    })
                )
                .finally(() => setLoadingProviders(false))
        },
        [competenciaSelecionada, tabSelected]
    )

    const handleGetResumo = () => {
        setShowInfo(true)
        setLoadingInfo(true)
        AnaliseContasService.getResumo(competenciaSelecionada?.competencia)
            .then(({ data }) => {
                setResumo(data)
            })
            .catch(() => {
                addToast({ type: 'error', title: 'Erro ao carregar as informações' })
            })
            .finally(() => setLoadingInfo(false))
    }

    useEffect(() => {
        if (filtro?.length === 0) {
            setNumberPage(0)
            carregarPrestadores(numberPage, filtro, true, newFilter)
        }
    }, [carregarPrestadores, competenciaSelecionada, tabSelected])

    useEffect(() => {
        !loadingProviders && carregarPrestadores(numberPage, filtro, false, newFilter)
    }, [numberPage])

    useEffect(() => {
        if (!competencias) {
            getCompetencias()
        }
    }, [anoExercicio, competencias, getCompetencias])

    const parseCompetencias = () => {
        return competencias?.map((item) => {
            return {
                month: DateUtils.getMonthName(item.competencia),
                competencia: item.competencia,
                status: item.situacao.toString()
            }
        })
    }

    const handlePesquisar = () => {
        saveFiltersToStorage(newFilter)
        carregarPrestadores(numberPage, filtro, true, newFilter)
    }

    const handleOnClosePesquisa = () => {
        setFiltro('')
        carregarPrestadores(numberPage, '', true, newFilter)
    }

    const handleClickTotal = () => {
        setFiltro('')
        setNewFilter(defaultFilters)
        setTabSelected(undefined)
    }

    const handleClickNotStarted = () => {
        setFiltro('')
        setNewFilter(defaultFilters)
        setTabSelected(SituacaoPrestadorAnaliseDeContas.NAO_INICIADO)
    }

    const handleClickSAnalyzing = () => {
        setFiltro('')
        setNewFilter(defaultFilters)
        setTabSelected(SituacaoPrestadorAnaliseDeContas.EM_ANALISE)
    }

    const handleClickFinish = () => {
        setFiltro('')
        setNewFilter(defaultFilters)
        setTabSelected(SituacaoPrestadorAnaliseDeContas.FINALIZADO)
    }

    const handlerEnviarParaFinanceiro = useCallback(() => {
        setLoadingEnviarFinanceiro(true)
        AnaliseContasService.putEncerrarCompetencia(competenciaSelecionada.competencia)
            .then(() => setPaymentProcess(true))
            .catch((err) =>
                addToast({
                    title: 'Erro ao encerrar competência',
                    type: 'error',
                    duration: 3000
                })
            )
            .finally(() => setLoadingEnviarFinanceiro(false))
    }, [competenciaSelecionada])

    const handlerEnviarParaComposicao = () => {
        setBtnComposicaoDisabled(true)
        FechamentoCompetencia.postFecharCompetencia(competenciaSelecionada.competencia)
            .then(() => {
                setPaymentProcess(true)
                getCompetencias()
                setModalComposicao(false)
            })
            .catch((err) =>
                addToast({
                    title: 'Erro ao encerrar competência',
                    type: 'error',
                    duration: 3000
                })
            )
            .finally(() => setBtnComposicaoDisabled(false))
    }

    const handleFinalizarCompetencia = useCallback(() => {
        setLoadingEncerrar(true)
        GlosaBiometria.getInfoBanner(competenciaSelecionada.competencia)
            .then(() => getCompetencias())
            .catch((err) => {
                addToast({
                    title: 'Erro ao encerrar competência',
                    type: 'error',
                    description: getMessageErrorFromApiResponse(err),
                    duration: 3000
                })
            })
            .finally(() => setLoadingEncerrar(false))
    }, [competenciaSelecionada, getCompetencias])

    const handleSetCompetencia = (competencia: any) => {
        setCompetenciaSelecionada(competencia)

        // Obtém o ano atual
        const currentYear = new Date().getFullYear()

        // Salva competencia no localStorage com uma data de expiração de 12 horas somente se anoExercicio for igual ao ano atual
        if (anoExercicio === currentYear) {
            const expirationDate = new Date()
            expirationDate.setHours(expirationDate.getHours() + 12) // Adiciona 12 horas à data atual
            const storedData = {
                competenciaValue: competencia,
                expiration: expirationDate.toISOString()
            }
            localStorage.setItem('@analise-contas-competenciaData', JSON.stringify(storedData))
        }
    }

    const getCompetenciaIfNotExpired = () => {
        const storedData = localStorage.getItem('@analise-contas-competenciaData')

        if (storedData) {
            const { competenciaValue, expiration } = JSON.parse(storedData)
            const currentDate = new Date()

            if (new Date(expiration) > currentDate) {
                // A data de expiração ainda não foi atingida, retorna o valor de competencia
                return competenciaValue
            } else {
                // A data de expiração foi atingida, remove competenciaData do localStorage
                localStorage.removeItem('@analise-contas-competenciaData')
            }
        }

        return null
    }

    return (
        <Layout isLoggedIn title="Análise de contas">
            <DividerSectionCard style={{ padding: 0 }}>
                <S.Container>
                    <CardSliderCompetencias
                        data={parseCompetencias()}
                        dateFilter={anosExercicio}
                        valueSelect={initialValueSelect}
                        onChangeAnoExercicio={onChangeAnoExercicio}
                        competenciaSelecionada={competenciaSelecionada}
                        setCompetenciaSelecionada={handleSetCompetencia}
                    />
                </S.Container>
            </DividerSectionCard>
            {validToCloseInfo?.faseCompetencia === 'ANALISE_DE_CONTAS_FECHADA' && (
                <InfoCard
                    title={'Análise de contas fechada'}
                    subtitle={
                        <p>
                            As contas já estão na fase de <strong>composição de pagamento</strong>. Acesse a seção
                            <S.LinkAlert
                                onClick={() =>
                                    router.push(
                                        process.env.NEXT_PUBLIC_DEV
                                            ? 'http://localhost:3000/faturamento/composicao-pagamento'
                                            : process.env.NEXT_PUBLIC_BASEURL_WEB
                                            ? `${process.env.NEXT_PUBLIC_BASEURL_WEB}/faturamento/composicao-pagamento`
                                            : 'https://planserv.maida.health/faturamento/composicao-pagamento'
                                    )
                                }
                            >
                                {' '}
                                Composição de pagamento
                            </S.LinkAlert>{' '}
                            para conferir
                        </p>
                    }
                    type="info"
                />
            )}
            {validToCloseInfo?.faseCompetencia === 'ANALISE_DE_CONTAS_ENCERRADA' && (
                <InfoCard
                    title={'Análise de contas encerrada'}
                    subtitle={`Os lotes estão prontos para ser enviados para composição de pagamento. Os lotes podem ser reabertos até ${
                        dataEnvio ? DateUtils.formatDatePTBR(dataEnvio) : ''
                    }.`}
                    type="info"
                />
            )}
            {loadingEncerrar ||
                (validToCloseInfo?.faseCompetencia === 'PROCESSAMENTO_DE_CONTAS_EM_ANDAMENTO' && (
                    <InfoCard
                        title={'Processamento de contas em andamento'}
                        subtitle={'O tempo do processamento varia de acordo com a quantidade de dados'}
                        type="alert"
                    />
                ))}

            {/*DECREPTED VALIDATION POSTAL */}
            {/* {finishedProcess && !paymentProcess ? (
                <InfoCard
                    title={'Análise de contas encerrada'}
                    subtitle={`Os lotes estão prontos para ser enviados para contas a pagar. Os lotes podem ser reabertos até ${
                        dataEnvio ? DateUtils.formatDatePTBR(dataEnvio) : ''
                    }.`}
                    type="info"
                />
            ) : !finishedProcess && paymentProcess ? (
                <InfoCard
                    title={'Análise de contas fechada'}
                    subtitle={
                        <>
                            As contas já estão na fase de <b>pagamento</b>. Acesse a seção{' '}
                            <S.Link href={`${process.env.NEXT_PUBLIC_BASEURL_WEB}/contas/contas-a-pagar`}>Contas a pagar </S.Link>
                            para conferir
                        </>
                    }
                    type="info"
                />
            ) : (
                loadingEncerrar && (
                    <InfoCard
                        title={'Processamento de contas em andamento'}
                        subtitle={'O tempo do processamento varia de acordo com a quantidade de dados'}
                        type="alert"
                    />
                )
            )} */}

            <DividerSectionCard dividerContent={true}>
                <S.Header>
                    <S.TitleCard style={{ display: 'flex', gap: '8px' }}>
                        Informações gerais{' '}
                        {/* <Button
                            themeButton={'warning'}
                            style={{ width: 'fit-content' }}
                            onClick={handleGetResumo}
                            title={validToCloseInfo?.mensagem}
                            disabled={loadingInfo}
                        >
                            {loadingInfo ? 'Carregando' : 'Carregar'} informações
                        </Button> */}
                    </S.TitleCard>

                    {
                        // !finishedProcess && paymentProcess ? (
                        //     <S.ActionsContainer>
                        //         <S.InfoCard>
                        //             <img src="/faturamento/assets/icons/info.svg" />
                        //             <p>A análise de contas já foi encerrada e se encontra na fase de pagamento</p>
                        //         </S.InfoCard>
                        //     </S.ActionsContainer>
                        // ) :
                        <>
                            {validToCloseInfo?.faseCompetencia === 'ANALISE_DE_CONTAS_ENCERRADA' && (
                                <S.ActionsContainer>
                                    <Button
                                        typeButton="text"
                                        themeButton="ihealth"
                                        onClick={handleReabrir}
                                        style={{ width: 'fit-content' }}
                                        iconLeft="/faturamento/assets/icons/refresh.svg"
                                    >
                                        Reabrir
                                    </Button>
                                    <Button
                                        typeButton="text"
                                        themeButton="ihealth"
                                        style={{ width: 'fit-content' }}
                                        // disabled={btnComposicaoDisabled}
                                        onClick={() => setModalComposicao(true)}
                                        iconLeft="/faturamento/assets/icons/money.svg"
                                    >
                                        Enviar para composição
                                    </Button>
                                </S.ActionsContainer>
                            )}

                            {(validToCloseInfo?.faseCompetencia === null ||
                                validToCloseInfo?.faseCompetencia === 'PROCESSAMENTO_DE_CONTAS_EM_ANDAMENTO') && (
                                <Button
                                    themeButton={'warning'}
                                    style={{ width: 'fit-content' }}
                                    onClick={handleFinalizarCompetencia}
                                    title={validToCloseInfo?.mensagem}
                                    disabled={
                                        validToCloseInfo?.faseCompetencia === 'PROCESSAMENTO_DE_CONTAS_EM_ANDAMENTO' ||
                                        loadingEncerrar ||
                                        !validToCloseInfo?.isValidaParaFechamento
                                        //DECREPTED VALIDATION POSTAL
                                        // ||(!finishedProcess && resumo && resumo.finalizados !== resumo.total)
                                    }
                                >
                                    {validToCloseInfo?.faseCompetencia === 'PROCESSAMENTO_DE_CONTAS_EM_ANDAMENTO' || loadingEncerrar
                                        ? 'Processando...'
                                        : 'Encerrar processamento'}
                                </Button>
                            )}
                        </>
                    }
                </S.Header>

                {
                    <>
                        {isLoadingCompetenciaSelecionada ? (
                            <Box sx={{ display: 'flex', minHeight: 218, alignItems: 'center', justifyContent: 'center' }}>
                                <CircularProgress />
                            </Box>
                        ) : (
                            <>
                                <TitleSection style={{ paddingTop: '24px' }}>Valores</TitleSection>
                                <S.Header>
                                    <S.ContentData>
                                        <ContentDataInfo
                                            className={'center-item'}
                                            label={'Apresentado'}
                                            value={
                                                resumo?.valorApresentado !== '0' && resumo?.valorApresentado !== '0.00'
                                                    ? NumberUtils.maskMoney(resumo?.valorApresentado)
                                                    : 'R$ 0,00'
                                            }
                                            background={'grey'}
                                            standart={'expanded'}
                                        />
                                        <ContentDataInfo
                                            className={'center-item'}
                                            label={'Apurado'}
                                            value={
                                                resumo?.valorApurado !== '0.00' && resumo?.valorApurado !== '0'
                                                    ? NumberUtils.maskMoney(resumo?.valorApurado)
                                                    : 'R$ 0,00'
                                            }
                                            background={'grey'}
                                            standart={'expanded'}
                                        />
                                        <ContentDataInfo
                                            className={'center-item'}
                                            label={'Glosado'}
                                            value={
                                                resumo?.valorGlosado !== '0.00' && resumo?.valorGlosado !== '0'
                                                    ? NumberUtils.maskMoney(resumo?.valorGlosado)
                                                    : 'R$ 0,00'
                                            }
                                            background={'grey'}
                                            standart={'expanded'}
                                        />
                                    </S.ContentData>
                                </S.Header>

                                <TitleSection style={{ paddingTop: '24px' }}>Lotes</TitleSection>
                                <S.Header>
                                    <S.ContentData>
                                        <ContentDataInfo
                                            className={'center-item'}
                                            label={'Total'}
                                            value={resumo?.total}
                                            background={'grey'}
                                            standart={'expanded'}
                                        />
                                        <ContentDataInfo
                                            className={'center-item'}
                                            label={'Abertos'}
                                            value={resumo?.abertos}
                                            background={'grey'}
                                            standart={'expanded'}
                                        />
                                        <ContentDataInfo
                                            className={'center-item'}
                                            label={'Finalizados'}
                                            value={resumo?.finalizados}
                                            background={'grey'}
                                            standart={'expanded'}
                                        />
                                    </S.ContentData>
                                </S.Header>
                            </>
                        )}
                    </>
                }
            </DividerSectionCard>

            <DividerSectionCard dividerContent={true}>
                <TitleSection style={{ marginBottom: '24px', fontSize: '16px', lineHeight: '24px' }}>Prestadores</TitleSection>
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <SearchBar
                        value={filtro}
                        placeholder="Procure por um prestador"
                        handleOnSearch={handlePesquisar}
                        handleOnClose={handleOnClosePesquisa}
                        handleOnChange={(e) => {
                            if (e?.target?.value === '') {
                                return handleOnClosePesquisa()
                            }
                            setFiltro(e.target.value)
                        }}
                    />
                    <S.FilterButtonWrapper onClick={() => setShowFilter(!showFilter)}>
                        <ReactSVG src="/faturamento/assets/icons/filter.svg" />
                        <p>Filtros</p>
                    </S.FilterButtonWrapper>
                </div>
                {showFilter && (
                    <S.FilterSelectWrapper>
                        <div className="row1">
                            <FormControl>
                                <InputLabel>Categoria do prestador</InputLabel>
                                <Select
                                    defaultValue="TODOS"
                                    value={newFilter?.categoriaPrestador}
                                    onChange={(e) => {
                                        setNewFilter({ ...newFilter, categoriaPrestador: e.target.value as 'TODOS' | 'CREDENCIADO' | 'EVENTUAL' })
                                    }}
                                >
                                    <MenuItem value="TODOS">Todos</MenuItem>
                                    <MenuItem value="CREDENCIADO">Credenciado</MenuItem>
                                    <MenuItem value="EVENTUAL">Eventual</MenuItem>
                                </Select>
                            </FormControl>
                            <FormControl>
                                <InputLabel>Tipo de processo</InputLabel>
                                <Select
                                    defaultValue="TODOS"
                                    value={newFilter?.tipoProcesso}
                                    onChange={(e) => {
                                        setNewFilter({
                                            ...newFilter,
                                            tipoProcesso: e.target.value as 'TODOS' | 'REGULAR' | 'LIMINAR' | 'EXCEPCIONALIDADE'
                                        })
                                    }}
                                >
                                    <MenuItem value="TODOS">Todos</MenuItem>
                                    <MenuItem value="REGULAR">Regular</MenuItem>
                                    <MenuItem value="LIMINAR">Liminar</MenuItem>
                                    <MenuItem value="EXCEPCIONALIDADE">Excepcionalidade</MenuItem>
                                </Select>
                            </FormControl>
                        </div>
                        <div className="row2">
                            <div>
                                <FormControlLabel
                                    control={
                                        <Checkbox
                                            checked={newFilter?.loteSemGlosa}
                                            onChange={({ target }) => {
                                                setNewFilter({ ...newFilter, loteComGlosa: false, loteSemGlosa: target.checked })
                                            }}
                                        />
                                    }
                                    label={<S.LabelCheckbox>Lotes sem glosa</S.LabelCheckbox>}
                                />
                                <FormControlLabel
                                    control={
                                        <Checkbox
                                            checked={newFilter?.loteComGlosa}
                                            onChange={({ target }) => {
                                                setNewFilter({ ...newFilter, loteComGlosa: target.checked, loteSemGlosa: false })
                                            }}
                                        />
                                    }
                                    label={<S.LabelCheckbox>Lotes com glosa</S.LabelCheckbox>}
                                />
                            </div>
                            <div style={{ gap: '8px', display: 'flex' }}>
                                <ButtonMui
                                    color="neutral"
                                    variant="text"
                                    onClick={() => {
                                        clearFiltersFromStorage()
                                        setNewFilter(defaultFilters)
                                    }}
                                >
                                    Limpar
                                </ButtonMui>
                                <ButtonMui
                                    color="primary"
                                    variant="outlined"
                                    onClick={() => {
                                        handlePesquisar()
                                    }}
                                >
                                    Filtrar
                                </ButtonMui>
                            </div>
                        </div>
                    </S.FilterSelectWrapper>
                )}

                <S.FilterCard>
                    <S.CardItem isFocus={!tabSelected} onClick={handleClickTotal}>
                        <S.CardName>Total</S.CardName>
                        <S.CardQuantity isFocus={!tabSelected} status={'total'}>
                            {resumoPrestadoresPorSituacao?.quantidadePrestadores || '--'}
                        </S.CardQuantity>
                    </S.CardItem>
                    <S.CardItem isFocus={tabSelected === SituacaoPrestadorAnaliseDeContas.NAO_INICIADO} onClick={handleClickNotStarted}>
                        <S.CardName>Não iniciado</S.CardName>
                        <S.CardQuantity isFocus={tabSelected === SituacaoPrestadorAnaliseDeContas.NAO_INICIADO} status={'notStarted'}>
                            {resumoPrestadoresPorSituacao
                                ? resumoPrestadoresPorSituacao?.situacoes?.find(
                                      (s) => s.valorSituacao === SituacaoPrestadorAnaliseDeContas.NAO_INICIADO
                                  )?.quantidade || '0'
                                : '--'}
                        </S.CardQuantity>
                    </S.CardItem>
                    <S.CardItem isFocus={tabSelected === SituacaoPrestadorAnaliseDeContas.EM_ANALISE} onClick={handleClickSAnalyzing}>
                        <S.CardName>Em análise</S.CardName>
                        <S.CardQuantity isFocus={tabSelected === SituacaoPrestadorAnaliseDeContas.EM_ANALISE} status={'analyzing'}>
                            {resumoPrestadoresPorSituacao
                                ? resumoPrestadoresPorSituacao?.situacoes?.find(
                                      (s) => s.valorSituacao === SituacaoPrestadorAnaliseDeContas.EM_ANALISE
                                  )?.quantidade || '0'
                                : '--'}
                        </S.CardQuantity>
                    </S.CardItem>
                    <S.CardItem isFocus={tabSelected === SituacaoPrestadorAnaliseDeContas.FINALIZADO} onClick={handleClickFinish}>
                        <S.CardName>Finalizados</S.CardName>
                        <S.CardQuantity isFocus={tabSelected === SituacaoPrestadorAnaliseDeContas.FINALIZADO} status={'finish'}>
                            {resumoPrestadoresPorSituacao
                                ? resumoPrestadoresPorSituacao?.situacoes?.find(
                                      (s) => s.valorSituacao === SituacaoPrestadorAnaliseDeContas.FINALIZADO
                                  )?.quantidade || '0'
                                : '--'}
                        </S.CardQuantity>
                    </S.CardItem>
                </S.FilterCard>

                {loadingProviders ? (
                    <AnimatedLoadingLottie style={{ display: 'flex', alignItems: 'center', margin: 'auto', minHeight: 400 }} />
                ) : prestadoresPage?.content?.length === 0 ? (
                    <div style={{ marginTop: '20px' }}>
                        <NoContent title="Por enquanto não há prestadores" />
                    </div>
                ) : (
                    <>
                        <div>
                            <S.ProviderHeaderCard>
                                <Item>Prestador</Item>
                                <Item>Categoria</Item>
                                <Item>Lotes fechados</Item>
                                <Item>Status</Item>
                                <Item> </Item>
                            </S.ProviderHeaderCard>

                            {prestadoresPage?.content?.map((item: IPrestadorQuery, index: number) => (
                                <S.ProviderCard
                                    key={index}
                                    onClick={() =>
                                        router.push(
                                            `/processamento-contas/analise-contas/resumo-cliente/${item?.prestadorId}?competencia=${competenciaSelecionada?.competencia}`
                                        )
                                    }
                                >
                                    <S.InfoContent>
                                        <S.InfoDirection>
                                            <h4>{capitalize(item.nomeFantasia)}</h4>
                                            <p>CNPJ - {StringUtils.maskCnpj(item.cnpj)}</p>
                                        </S.InfoDirection>
                                    </S.InfoContent>
                                    <Item>
                                        <S.Lotes>
                                            <Badge style={{ padding: '0 8px', fontWeight: 600 }} color="#0079B8" background="rgba(0, 138, 210, 0.16)">
                                                {item?.categoria}
                                            </Badge>
                                        </S.Lotes>
                                    </Item>
                                    <Item>
                                        <S.Lotes>
                                            {item.lotesFechados !== undefined && item.lotesTotal !== undefined
                                                ? `${item.lotesFechados} de ${item.lotesTotal}`
                                                : '---'}
                                        </S.Lotes>
                                    </Item>
                                    <Item>
                                        {item.situacao === SituacaoPrestadorAnaliseDeContas.FINALIZADO ? (
                                            <S.SlugFinish>Finalizado</S.SlugFinish>
                                        ) : item.situacao === SituacaoPrestadorAnaliseDeContas.NAO_INICIADO ? (
                                            <S.SlugNotStarted>Não iniciado</S.SlugNotStarted>
                                        ) : item.situacao === SituacaoPrestadorAnaliseDeContas.EM_ANALISE ? (
                                            <S.SlugAnalyzing>Em análise</S.SlugAnalyzing>
                                        ) : (
                                            <S.SlugNotStarted>---</S.SlugNotStarted>
                                        )}
                                    </Item>
                                    <Item> </Item>
                                </S.ProviderCard>
                            ))}
                        </div>
                        <S.PaginationContainer>
                            <Pagination
                                totalPage={pagination?.totalPaginas}
                                actualPage={pagination?.paginaAtual}
                                setNumberPage={pagination?.setNumberPage}
                                totalRegister={pagination?.totalRegistros}
                            />
                            <S.Page>
                                <span className="pageNumber">
                                    {pagination?.paginaAtual * pagination?.linhasPorPagina + 1} -{' '}
                                    {prestadoresPage?.content?.length + pagination?.paginaAtual * pagination?.linhasPorPagina}
                                    {' de '}
                                    {pagination?.totalRegistros}
                                </span>
                            </S.Page>
                        </S.PaginationContainer>
                    </>
                )}
            </DividerSectionCard>
            <ModalGenericComponent
                widthModal="40vh"
                setOpenModal={setModalComposicao}
                open={modalComposicao}
                disabledButton={btnComposicaoDisabled}
                title="Enviar para composição de pagamento?"
                cancelButton="Cancelar"
                actionButton="Enviar"
                actionFunction={handlerEnviarParaComposicao}
            >
                <span></span>
            </ModalGenericComponent>
        </Layout>
    )
}

export default AnaliseContasTemplate
