import * as S from './styles'
import dynamic from 'next/dynamic'
const AnimatedLoadingLottie = dynamic(() => import('components/atoms/AnimatedLoadingLottie'), { ssr: false })

type SpinnerLoadingProps = {
    spinnerHeight?: string
    containerStyles?: React.CSSProperties
    animationStyles?: React.CSSProperties
}
const SpinnerLoading = ({ spinnerHeight = '4.8rem', containerStyles, animationStyles }: SpinnerLoadingProps) => {
    return (
        <S.Container style={{ ...containerStyles }}>
            <S.Spinner style={{ ...animationStyles, height: spinnerHeight, width: spinnerHeight }}></S.Spinner>
        </S.Container>
    )
}

export default SpinnerLoading
