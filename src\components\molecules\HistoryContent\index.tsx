import DividerSectionCard from 'components/atoms/DividerSectionCard'
import Item from 'components/atoms/Item'
import TitleSection from 'components/atoms/TitleSection'
import React, { useState } from 'react'
import * as S from './styles'
import { ICalendarioHistoricoAlteracaoDTO } from 'src/services/analiseContasApi/Calendario/types'

type HistoryContentProps = {
    data: ICalendarioHistoricoAlteracaoDTO[]
    title: string
}

const HistoryContent = ({ data, title }: HistoryContentProps) => {
    const [isActive, setIsActive] = useState(true)
    return (
        <DividerSectionCard dividerContent={true}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
                <S.ButtonAccordion onClick={() => setIsActive(!isActive)}>
                    {isActive ? (
                        <img src="/faturamento/assets/icons/chevron.svg" style={{ transform: 'rotate(180deg)' }} />
                    ) : (
                        <img src="/faturamento/assets/icons/chevron.svg" />
                    )}
                </S.ButtonAccordion>
                <TitleSection>{title}</TitleSection>
            </div>
            {!isActive && (
                <>
                    <S.ContentItemProcedure>
                        <Item>
                            <p>Data</p>
                        </Item>
                        <div></div>
                        {/* <Item>
                            <p>Usuário</p>
                        </Item> */}
                        <Item>
                            <p>Tipo de Alteração</p>
                        </Item>
                    </S.ContentItemProcedure>
                    {data?.map((obj, index) => (
                        <S.ContentItemResultProcedure key={index}>
                            <Item>
                                <span>{obj?.dataHoraAlteracao}</span>
                            </Item>
                            <div></div>
                            {/* <Item>
                                <span>{obj?.nomeUsuario}</span>
                            </Item> */}
                            <Item>
                                <span>{obj?.tipoAlteracao}</span>
                            </Item>
                        </S.ContentItemResultProcedure>
                    ))}
                </>
            )}
        </DividerSectionCard>
    )
}

export default HistoryContent
