import { SituacaoLote, Tip<PERSON>En<PERSON>, TipoLote } from 'types/common/enums'

export const MockGetProcessamentoContasQuantitativoDto = {
    percentualFalha: 0.02,
    percentualRecusa: 42.23,
    quantidadeLotesNaoSicronizados: 1,
    resumoLoteProcessado: [
        {
            tipoProcessamento: 'NORMAL',
            situacao: 'EM_ANALISE',
            quantidadeLotes: 2777
        },
        {
            tipoProcessamento: 'NORMAL',
            situacao: 'RECUSADO',
            quantidadeLotes: 2030
        }
    ],
    resumoProcessamentoLote: [
        {
            quantidadeLotes: 7,
            tipoProcessamento: 'NORMAL',
            situacaoProcessamento: 'EM_PROCESSAMENTO',
            situacaoRegistoProcessamento: 'AGENDADO'
        },
        {
            quantidadeLotes: 1,
            tipoProcessamento: 'NORMAL',
            situacaoProcessamento: 'FALHA',
            situacaoRegistoProcessamento: 'FINALIZADO'
        },
        {
            quantidadeLotes: 4807,
            tipoProcessamento: 'NORMAL',
            situacaoProcessamento: 'PROCESSADO',
            situacaoRegistoProcessamento: 'FINALIZADO'
        }
    ]
}

export const MockGetItemProcessamentoPrestadorPage = {
    content: [
        {
            prestadorId: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
            nomeFantasia: 'Hospital São Pedro',
            cpfCnpj: '00.106.435/0002-04',
            categoria: 'Eventual',
            quantidadeLotes: 1
        },
        {
            prestadorId: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
            nomeFantasia: 'Hospital de Terapia Intensiva',
            cpfCnpj: '00.106.435/0002-04',
            categoria: 'Eventual',
            quantidadeLotes: 1
        },
        {
            prestadorId: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
            nomeFantasia: 'Hospital A',
            cpfCnpj: '00.106.435/0002-04',
            categoria: 'Eventual',
            quantidadeLotes: 1
        }
    ],
    pageable: {
        sort: {
            empty: false,
            unsorted: false,
            sorted: true
        },
        offset: 0,
        pageNumber: 0,
        pageSize: 10,
        paged: true,
        unpaged: false
    },
    totalPages: 1,
    totalElements: 3,
    last: true,
    size: 10,
    number: 0,
    sort: {
        empty: false,
        unsorted: false,
        sorted: true
    },
    numberOfElements: 3,
    first: true,
    empty: false
}

export const MockGetRegistroProcessamentoLotePage = {
    content: [
        {
            uuid: 'cc8d734e-e9ed-49c2-9067-453849925efa',
            identificadorLote: 101,
            tipoLote: TipoLote.CONSULTA,
            tipoEnvio: TipoEnvio.MANUAL,
            dataEnvio: '2025-02-07T16:09:06.803198',
            situacaoLote: SituacaoLote.PROCESSANDO
        },
        {
            uuid: 'cc8d734e-e9ed-49c2-9067-453849925efa',
            identificadorLote: 102,
            tipoLote: TipoLote.SPSADT,
            tipoEnvio: TipoEnvio.ELETRONICO,
            dataEnvio: '2025-02-07T16:09:06.803198',
            situacaoLote: SituacaoLote.PROCESSANDO
        },
        {
            uuid: 'cc8d734e-e9ed-49c2-9067-453849925efa',
            identificadorLote: 103,
            tipoLote: TipoLote.RESUMO_INTERNACAO,
            tipoEnvio: TipoEnvio.ELETRONICO,
            dataEnvio: '2025-02-07T16:09:06.803198',
            situacaoLote: SituacaoLote.PROCESSANDO
        }
    ],
    pageable: {
        sort: {
            empty: false,
            unsorted: false,
            sorted: true
        },
        offset: 0,
        pageNumber: 0,
        pageSize: 10,
        paged: true,
        unpaged: false
    },
    totalPages: 1,
    totalElements: 3,
    last: true,
    size: 10,
    number: 0,
    sort: {
        empty: false,
        unsorted: false,
        sorted: true
    },
    numberOfElements: 3,
    first: true,
    empty: false
}
