import { GrauParticipacaoEnum, StatusItemEnum, TipoItemEnum, ViaAcessoEnum } from 'src/services/faturamentoPrestadorApi/geracao-recurso-glosa/enuns'
import { StatusAuditoriaHistoricoRAH, StatusHistoricoRAH } from 'types/common/enums'
import { IPageResult } from 'types/common/pagination'

export type IPageGuiaItens = IPageResult<IGuiaItensQuery>
export interface IItemGuiaDetalhesDTO {
    calculosDeGlosa: IItemGuiaCalculosDeGlosaDTO
    codigoTuss: string
    dataRealizacao: string
    descricao: string
    grauParticipacao: GrauParticipacaoEnum
    horarioEspecial: boolean
    nomeProfissionalResponsavel: string
    status: StatusItemEnum
    tabelaTuss: string
    tipo: TipoItemEnum
    viaAcesso: ViaAcessoEnum
    motivosDeGlosaTecnica: IMotivosDeGlosa[]
    motivosDeGlosaAdministrativa: IMotivosDeGlosa[]
}

export interface IItemGuiaCalculosDeGlosaDTO {
    grauParticipacaoPorcentagem: number
    viaAcessoPorcentagem: number
    horarioEspecial: boolean
    quantidadeApresentada: number
    quantidadeApurada: number
    quantidadeAGlosar: number
    valorUnitarioApresentado: number
    valorUnitarioApurado: number
    valorGlosado: number
    totalApresentado: number
    totalApurado: number
    totalAPagar: number
}

export interface IMotivosDeGlosa {
    codigo: string
    descricao: string
    justificativa: string
    nomeUsuario: string
    dataAtualizacao: string
}
export interface IGuiaItensQuery {
    uuid: string
    id: string
    codigoItem: string
    descricaoItem: string
    quantidadeApresentada: number
    valorApresentado: number
    horarioEspecial: boolean
    tipo: string
    valorGlosado: number
    valorApurado: number
    situacao: string
    status: string
    grauParticipacao: string
    viaAcesso
    valorUnitarioApresentado: string
    totalApresentado: string
    valorUnitarioApurado: string
    totalApurado: string
    status: StatusItemEnum
    quantidadeApurada: number
    quantidadeGlosada: number
    totalPagar: string
}

//

export interface SituacaoGuiaQuery {
    descricao: string
    porcentagem: number
    quantidadeDeGuias: number
    valor: string
}

// INFORMAÇÕES GERAIS E HISTORICO DE RAH

export interface IGuiaInfoGeraisQuery {
    arquivos: Arquivo[]
    autenticacao: string
    autorizacao: string
    autorizacaoGuiaOrigem: string
    beneficiario: BeneficiarioGuiaQuery
    dataAtendimento: string
    dataSituacao: string
    especialidade: string
    etapaGuiaAnalise: 'PROCESSAMENTO' | 'ANALISE_ADMINISTRATIVA' | 'ANALISE_TECNICA' | 'FECHAMENTO'
    historicoRAH: HistoricoRAH[]
    id: string
    identificadorLote: number
    numeroGuiaOperadora: string
    numeroGuiaPrestador: string
    prestador: PrestadorGuiaQuery
    retroatividade: string
    situacaoAtual: string
    situacaoLote: string
    tipo: string
    tipoProcesso?: 'LIMINAR' | 'EXCEPCIONALIDADE'
    valorApresentado: number
    valorApurado: number
    valorGlosado: number
    dataInicioFaturamento?: string
    dataFinalFaturamento?: string
    isBenficiarioCancelado?: boolean
    isBenficiarioEmCarencia?: boolean
    isBenficiarioSuspenso?: boolean
}

interface Arquivo {
    absPath: string
    filename: string
    id: string
    tipo: string
}

interface HistoricoRAH {
    anexoUrl: string
    auditorResponsavel: string
    dataRegistro: string
    periodoCobrancaFinal: string
    periodoCobrancaInicio: string
    status: string
}

interface HistoricoRAHResponse {
    auditorResponsavel: string
    auditorResponsavelId: string
    dataRegistro: string
    idAuditoria: number
    periodoCobrancaFim: string
    periodoCobrancaInicio: string
    relatorio: DocumentoAnexoResponse
    status: StatusHistoricoRAH
    statusAuditoria: StatusAuditoriaHistoricoRAH
}

interface DocumentoAnexoResponse {
    dataEnvio: string
    descricao: string
    id: number
    idTipoDocumento: number
    nome: string
    tipoDocumento: string
    url: string
    usuario: string
}

export interface BeneficiarioGuiaQuery {
    bairro: string
    dataNascimento: string
    genero: string
    municipio: string
    nomeBeneficiario: string
    numeroCartao: string
    numeroCasa: string
    numeroTelefone: string
    rua: string
    tipoBeneficiario: string
}

export interface PrestadorGuiaQuery {
    bairro: string
    municipio: string
    nomePrestador: string
    nomeProfissionalResponsavel: string
    numero: string
    numeroTelefone: string
    rua: string
}

export interface IMensagens {
    nomeUsuario: string
    data: string
    comentario: string
    imageUrl: string
    minhaMensagem: boolean
}
