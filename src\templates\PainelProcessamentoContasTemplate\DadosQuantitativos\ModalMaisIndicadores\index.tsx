import Modal from 'components/atoms/Modal'
import { IProcessamentoContasQuantitativoResumo } from 'src/services/processadorRegrasApi/RegistrosProcessamentoContas/types'
import * as S from './styles'
import { useEffect, useState } from 'react'
import Button from 'components/atoms/Button'
import { CardInfoIcon, FlexColumn, HorizontalCard } from '../styles'
import CardToolTipComponent from 'components/molecules/TooltipInfo'
import { formatPercent } from 'utils/masks/formatPercent'
import { ReactSVG } from 'react-svg'
import SpinnerLoading from 'components/molecules/SpinnerLoading'
import zIndex from '@mui/material/styles/zIndex'

type ModalMaisIndicadoresProps = {
    isOpen: boolean
    competencia: string
    dadosQuantitativos: IProcessamentoContasQuantitativoResumo
    loadingDadosQuantitativos: boolean
    onRefreshDadosQuantitativosPainel: () => void
    onClose: () => void
}
const ModalMaisIndicadores = ({
    isOpen,
    competencia,
    dadosQuantitativos,
    loadingDadosQuantitativos,
    onRefreshDadosQuantitativosPainel,
    onClose
}: ModalMaisIndicadoresProps) => {
    return (
        <Modal isOpen={isOpen} onClose={onClose} style={{ width: 'min(48rem, 95%)', padding: '2.4rem', zIndex: '1100' }}>
            <S.Content>
                <S.Header>
                    <p className="title">Mais indicadores</p>
                    <div
                        className="refresh-button"
                        style={loadingDadosQuantitativos ? { opacity: '0.6', cursor: 'not-allowed' } : undefined}
                        onClick={onRefreshDadosQuantitativosPainel}
                    >
                        <ReactSVG src="/faturamento/assets/icons/refresh.svg" />
                    </div>
                </S.Header>
                {loadingDadosQuantitativos ? (
                    <SpinnerLoading containerStyles={{ height: '32rem' }} />
                ) : (
                    <>
                        <HorizontalCard cardTheme="pink" style={{ padding: '0.8rem 1.6rem', justifyContent: 'space-between' }}>
                            <div className="card-data">
                                <p className="label">Taxa de falha</p>
                                <p className="value">{formatPercent(dadosQuantitativos?.percentualFalha)}</p>
                            </div>
                            <CardInfoIcon>
                                <CardToolTipComponent
                                    location="right-end"
                                    title={'O que é a taxa de falha?'}
                                    description={
                                        'A taxa de falha representa a porcentagem de lotes com falhas no processamento, em relação à quantidade total de lotes enviados na competência.'
                                    }
                                />
                            </CardInfoIcon>
                        </HorizontalCard>
                        <HorizontalCard cardTheme="pink" style={{ padding: '0.8rem 1.6rem', justifyContent: 'space-between' }}>
                            <div className="card-data">
                                <p className="label">Taxa de recusa</p>
                                <p className="value">{formatPercent(dadosQuantitativos?.percentualRecusa)}</p>
                            </div>
                            <CardInfoIcon>
                                <CardToolTipComponent
                                    location="right-end"
                                    title={'O que é a taxa de recusa?'}
                                    description={
                                        'A taxa de recusa representa a porcentagem de lotes recusados, em relação à quantidade total de lotes processados.'
                                    }
                                />
                            </CardInfoIcon>
                        </HorizontalCard>
                        <HorizontalCard cardTheme="grey" style={{ padding: '1.6rem' }}>
                            <FlexColumn>
                                <p className="label" style={{ textAlign: 'left' }}>
                                    Tempo de processamento
                                </p>
                                <FlexColumn style={{ gap: '0.4rem' }}>
                                    <div className="card-data">
                                        <p className="label" style={{ fontWeight: '400' }}>
                                            Em até 15min:
                                        </p>
                                        <p className="value">
                                            {`${formatPercent(
                                                (dadosQuantitativos?.resumoTempoProcessamento?.menor15 /
                                                    dadosQuantitativos?.resumoTempoProcessamento?.totalRegistros) *
                                                    100
                                            )} dos lotes`}
                                        </p>
                                    </div>
                                    <div className="card-data">
                                        <p className="label" style={{ fontWeight: '400' }}>
                                            Entre 15 e 60min:
                                        </p>
                                        <p className="value">{`${formatPercent(
                                            (dadosQuantitativos?.resumoTempoProcessamento?.entre15e60 /
                                                dadosQuantitativos?.resumoTempoProcessamento?.totalRegistros) *
                                                100
                                        )} dos lotes`}</p>
                                    </div>
                                    <div className="card-data">
                                        <p className="label" style={{ fontWeight: '400' }}>
                                            Mais de 60min:
                                        </p>
                                        <p className="value">{`${formatPercent(
                                            (dadosQuantitativos?.resumoTempoProcessamento?.maior60 /
                                                dadosQuantitativos?.resumoTempoProcessamento?.totalRegistros) *
                                                100
                                        )} dos lotes`}</p>
                                    </div>
                                    <div className="card-data">
                                        <p className="label" style={{ fontWeight: '400' }}>
                                            Tempo médio:
                                        </p>
                                        <p className="value">
                                            {dadosQuantitativos?.resumoTempoProcessamento?.mediaTempoProcessamento.toFixed(1)} min
                                        </p>
                                    </div>
                                    <div className="card-data">
                                        <p className="label" style={{ fontWeight: '400' }}>
                                            Status:
                                        </p>
                                        <p className="value">
                                            {(function () {
                                                const tempoMedio = dadosQuantitativos?.resumoTempoProcessamento?.mediaTempoProcessamento || 0
                                                if (tempoMedio <= 15) return 'Rápido'
                                                if (tempoMedio > 15 && tempoMedio <= 60) return 'Rápido'
                                                return 'Lento'
                                            })()}
                                        </p>
                                        <CardInfoIcon style={{ marginTop: '0.4rem' }}>
                                            <CardToolTipComponent
                                                location="right-end"
                                                title={'Status do processamento'}
                                                description={
                                                    'O status é baseado no tempo médio de processamento na competência: em até 15min - Rápido, entre 15 e 60min - Médio, mais de 60min - Lento.'
                                                }
                                            />
                                        </CardInfoIcon>
                                    </div>
                                </FlexColumn>
                            </FlexColumn>
                        </HorizontalCard>
                    </>
                )}
                <S.ButtonsWrapper>
                    <Button themeButton="secondary" style={{ width: 'fit-content' }} onClick={onClose}>
                        Fechar
                    </Button>
                </S.ButtonsWrapper>
            </S.Content>
        </Modal>
    )
}

export default ModalMaisIndicadores
