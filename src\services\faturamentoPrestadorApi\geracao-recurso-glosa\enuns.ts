export enum TipoLoteGuiaEnum {
    CONSULTA = 'CONSULTA',
    HONORARIO = 'HONORARIO',
    SPSADT = 'SPSADT',
    TRATAMENTO_ODONTOLOGICO = 'TRATAMENTO_ODONTOLOGICO',
    RECURSO_GLOSA = 'RECURSO_GLOSA',
    RESUMO_INTERNACAO = 'RESUMO_INTERNACAO'
}

export enum TipoGuiaEnum {
    SPSADT = 'SPSADT',
    RESUMO_INTERNACAO = 'RESUMO_INTERNACAO',
    CONSULTA = 'CONSULTA',
    HONORARIOS = 'HONORARIOS',
    ODONTO = 'ODONTO',
    RECURSO = 'RECURSO'
}

export enum SituacaoGuiaEnum {
    AGUARDANDO_RECURSO = 'AGUARDANDO_RECURSO',
    RECURSADA = 'RECURSADA'
}

export enum GrauParticipacaoEnum {
    CIRURGIAO = 'CIRURGIAO',
    PRIMEIRO_AUXILIAR = 'PRIMEIRO_AUXILIAR',
    SEGUNDO_AUXILIAR = 'SEGUNDO_AUXILIAR',
    TERCEIRO_AUXILIAR = 'TERCEIRO_AUXILIAR',
    QUARTO_AUXILIAR = 'QUARTO_AUXILIAR',
    INSTRUMENTADOR = 'INSTRUMENTADOR',
    ANESTESISTA = 'ANESTESISTA',
    AUXILIAR_ANESTESISTA = 'AUXILIAR_ANESTESISTA',
    CONSULTOR = 'CONSULTOR',
    PERFUSIONISTA = 'PERFUSIONISTA',
    PEDIATRA_NA_SALA_DE_PARTO = 'PEDIATRA_NA_SALA_DE_PARTO',
    PEDIATRA_SALA_PARTO = 'PEDIATRA_NA_SALA_DE_PARTO',
    AUXILIAR_SADT = 'AUXILIAR_SADT',
    CLINICO = 'CLINICO',
    INTENSIVISTA = 'INTENSIVISTA'
}

export enum StatusItemEnum {
    SEM_GLOSA = 'SEM_GLOSA',
    AGUARDANDO_ANALISE = 'AGUARDANDO_ANALISE',
    GLOSADO = 'GLOSADO',
    RECURSADO = 'RECURSADO',
    INCOMPLETO = 'INCOMPLETO'
}

export enum TipoItemEnum {
    PROCEDIMENTO = 'PROCEDIMENTO',
    DIARIA = 'DIARIA',
    GASOTERAPIA = 'GASOTERAPIA',
    TAXA = 'TAXA',
    OPME = 'OPME',
    MATERIAL = 'MATERIAL',
    MEDICAMENTO = 'MEDICAMENTO'
}

export enum ViaAcessoEnum {
    UNICA = 'UNICA',
    MESMA_VIA = 'MESMA_VIA',
    DIFERENTE_VIAS = 'DIFERENTE_VIAS'
}

export enum ModuloEnum {
    MEDICO = 'MEDICO',
    ODONTO = 'ODONTO',
    RECURSO = 'RECURSO'
}

export enum TipoEnvioEnum {
    XML = 'XML',
    ELETRONICO = 'ELETRONICO',
    MANUAL = 'MANUAL',
    MIGRACAO = 'MIGRACAO'
}

export enum TipoLoteEnum {
    LOTE_ORIGEM = 'LOTE_ORIGEM',
    LOTE_RECURSO = 'LOTE_RECURSO'
}

export enum SituacaoRecursoEnum {
    ABERTO = 'ABERTO',
    ENCERRADO = 'ENCERRADO',
    FECHADO = 'FECHADO',
    PAGO = 'PAGO'
}

export enum ItemStatusEnum {
    TODOS = 'TODOS',
    GLOSADO = 'GLOSADO',
    RECURSADO = 'RECURSADO'
}

export enum TipoItemElegivelEnum {
    PROCEDIMENTO = 'PROCEDIMENTO',
    TAXA_DIARIA_GASES = 'TAXA_DIARIA_GASES',
    HONORARIO = 'HONORARIO',
    MATERIAL_OPME = 'MATERIAL_OPME',
    MEDICAMENTO = 'MEDICAMENTO'
}
