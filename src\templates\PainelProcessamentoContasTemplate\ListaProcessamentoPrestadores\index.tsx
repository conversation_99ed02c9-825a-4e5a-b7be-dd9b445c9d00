import { useEffect, useState } from 'react'
import * as S from './styles'
import SpinnerLoading from 'components/molecules/SpinnerLoading'
import { RegistrosProcessamentoContasService } from 'src/services/processadorRegrasApi/RegistrosProcessamentoContas'
import { useToast } from 'src/hooks/toast'
import {
    IGetItemProcessamentoPrestadorDto,
    IProcessamentoContasQuantitativoResumo
} from 'src/services/processadorRegrasApi/RegistrosProcessamentoContas/types'
import { IPagination } from 'types/pagination'
import { PaginationHelper } from 'utils/helpers/paginationHelper'
import Pagination from 'components/molecules/Pagination'
import { Table } from 'components/molecules/Table'
import theme from 'styles/theme'
import SearchBar from 'components/molecules/SearchBar'
import Button from 'components/atoms/Button'
import NavTabs from 'components/molecules/NavTabs'
import { SituacaoProcessamentoLoteEnum } from 'src/services/processadorRegrasApi/RegistrosProcessamentoContas/enums'
import { SituacaoLote } from 'types/common/enums'
import SituacaoBadge from './SituacaoBadge'
import { maskCNPJ } from 'utils/masks/formatCNPJ'
import maskCpf from 'utils/masks/formatCPF'
import { useAuth } from 'src/hooks/auth'
import { PrestadorService } from 'src/services/faturamentoOperadoraApi/prestadorServices'
import { useRouter } from 'next/router'

const currentYear = new Date().getFullYear()
const currentMonth = new Date().getMonth() + 1

const getTabsData = (counters?: IProcessamentoContasQuantitativoResumo, hasFiltroEmAbertoPor?: boolean) =>
    [
        {
            label: 'Falhas',
            identifier: SituacaoProcessamentoLoteEnum.FALHA,
            counter: counters?.quantidadeLotesComFalha
        },
        {
            label: 'Agendados',
            identifier: SituacaoProcessamentoLoteEnum.AGENDADO,
            counter: counters?.quantidadeLotesAgendados
        },
        {
            label: 'Em processamento',
            identifier: SituacaoProcessamentoLoteEnum.EM_PROCESSAMENTO,
            counter: counters?.quantidadeLotesProcessando
        },
        {
            label: 'Recusados',
            identifier: SituacaoLote.RECUSADO,
            counter: counters?.quantidadeLotesRecusados
        },
        {
            label: 'Cancelados',
            identifier: SituacaoLote.CANCELADO,
            counter: counters?.quantidadeLotesCancelados
        }
    ].filter(({ identifier }) =>
        hasFiltroEmAbertoPor ? ![SituacaoLote.RECUSADO, SituacaoLote.CANCELADO].includes(identifier as SituacaoLote) : true
    )

const PAGE_SIZE = 8

type ListaProcessamentoPrestadoresProps = {
    competencia: string
    isCompetenciaAtual: boolean
    isCompetenciaAberta: boolean
    filtroCodigoContratado?: string
    filtroEmAbertoPor?: number
    situacaoCompetencia: string
    dadosQuantitativos: IProcessamentoContasQuantitativoResumo
    refreshDados?: boolean
    onRefreshDadosQuantitativosPainel: () => void
}
const ListaProcessamentoPrestadores = ({
    competencia,
    isCompetenciaAtual,
    isCompetenciaAberta,
    filtroCodigoContratado,
    filtroEmAbertoPor,
    situacaoCompetencia,
    dadosQuantitativos,
    refreshDados,
    onRefreshDadosQuantitativosPainel
}: ListaProcessamentoPrestadoresProps) => {
    const [loadingDados, setLoadingDados] = useState(true)
    const [listaPrestadores, setListaPrestadores] = useState<IGetItemProcessamentoPrestadorDto[]>()
    const [currentPageNumber, setCurrentPageNumber] = useState(0)
    const [pagination, setPagination] = useState<IPagination>()
    const [solicitandoReprocessamentoCompetencia, setSolicitandoReprocessamentoCompetencia] = useState(false)
    const [currentTab, setCurrentTab] = useState<SituacaoProcessamentoLoteEnum | SituacaoLote>(getTabsData()[0].identifier)

    const { isOwner } = useAuth()

    const { addToast } = useToast()

    const router = useRouter()

    const loadDados = ({
        competencia,
        codigoContratado,
        emAbertoPor,
        situacao = getTabsData()[0].identifier,
        pageNumber = 0
    }: {
        competencia: string
        codigoContratado?: string
        emAbertoPor?: number
        situacao?: SituacaoLote | SituacaoProcessamentoLoteEnum
        pageNumber?: number
    }) => {
        setLoadingDados(true)
        RegistrosProcessamentoContasService.getProcessamentoPrestadores({
            competencia,
            tipoProcessamentoLote: 'NORMAL',
            codigoContratado,
            situacaoLote: [SituacaoLote.RECUSADO, SituacaoLote.CANCELADO].includes(situacao as SituacaoLote) ? (situacao as SituacaoLote) : undefined,
            situacaoProcessamentoLote: [
                SituacaoProcessamentoLoteEnum.AGENDADO,
                SituacaoProcessamentoLoteEnum.EM_PROCESSAMENTO,
                SituacaoProcessamentoLoteEnum.FALHA
            ].includes(situacao as SituacaoProcessamentoLoteEnum)
                ? (situacao as SituacaoProcessamentoLoteEnum)
                : undefined,
            emAbertoPor: emAbertoPor || undefined,
            page: pageNumber,
            size: PAGE_SIZE
        })
            .then(async ({ data }) => {
                let prestadores = data?.content

                setCurrentTab(situacao)
                setPagination(PaginationHelper.parserPagination<IGetItemProcessamentoPrestadorDto>(data, (page) => setCurrentPageNumber(page)))

                if (prestadores?.length > 0) {
                    await PrestadorService.getListaPrestadores(
                        data?.content?.map(({ prestadorId }) => prestadorId),
                        0,
                        PAGE_SIZE
                    )
                        .then(({ data }) => {
                            prestadores = prestadores.map((prestador) => ({
                                ...prestador,
                                nomeFantasia: data?.content?.find(({ uuid }) => uuid === prestador.prestadorId)?.nomeFantasia || null
                            }))
                        })
                        .catch(() => undefined)
                }

                setListaPrestadores(prestadores)
                setLoadingDados(false)
            })
            .catch((err) => {
                addToast({
                    title: 'Ocorreu um erro ao tentar carregar os dados do processamento de contas',
                    type: 'error',
                    duration: 5000
                })
            })
    }

    const reprocessarLotesComFalhaCompetencia = (competencia: string) => {
        setSolicitandoReprocessamentoCompetencia(true)
        RegistrosProcessamentoContasService.reprocessarLotesComFalha({
            competencia
        })
            .then(() => {
                loadDados({ competencia })
                onRefreshDadosQuantitativosPainel()
            })
            .catch((err) => {
                addToast({
                    title: 'Ocorreu um erro ao tentar reprocessar os lotes com falha da competência',
                    type: 'error',
                    duration: 5000
                })
            })
            .finally(() => setSolicitandoReprocessamentoCompetencia(false))
    }

    useEffect(() => {
        competencia && loadDados({ competencia, codigoContratado: filtroCodigoContratado, emAbertoPor: filtroEmAbertoPor })
    }, [competencia])

    useEffect(() => {
        competencia && loadDados({ competencia, codigoContratado: filtroCodigoContratado, emAbertoPor: filtroEmAbertoPor })
    }, [filtroCodigoContratado, filtroEmAbertoPor])

    useEffect(() => {
        refreshDados && competencia && loadDados({ competencia, codigoContratado: filtroCodigoContratado, emAbertoPor: filtroEmAbertoPor })
    }, [refreshDados])

    return (
        <S.Container>
            <S.Header>
                <S.Title>Prestadores</S.Title>
                {!loadingDados && isOwner && (
                    <Button
                        style={{ width: 'fit-content' }}
                        iconLeft="/faturamento/assets/icons/Sync.svg"
                        themeButton="dark-danger"
                        onClick={() => reprocessarLotesComFalhaCompetencia(competencia)}
                        disabled={
                            solicitandoReprocessamentoCompetencia ||
                            !dadosQuantitativos?.quantidadeLotesComFalha ||
                            !isCompetenciaAtual ||
                            !isCompetenciaAberta ||
                            !!filtroCodigoContratado ||
                            !!filtroEmAbertoPor
                        }
                    >
                        Reprocessar lotes com falhas
                    </Button>
                )}
            </S.Header>

            <NavTabs
                key={`${competencia}`}
                variant="type_3"
                tabs={getTabsData(dadosQuantitativos, !!filtroEmAbertoPor)}
                setTabSelected={(index) => {
                    setCurrentTab(getTabsData()[index].identifier)
                    loadDados({
                        competencia,
                        situacao: getTabsData()[index].identifier,
                        codigoContratado: filtroCodigoContratado,
                        emAbertoPor: filtroEmAbertoPor
                    })
                }}
            />

            {loadingDados ? (
                <SpinnerLoading />
            ) : (
                <Table.Root>
                    <Table.Body>
                        {listaPrestadores?.map((element, index) => (
                            <Table.Card
                                style={{
                                    display: 'grid',
                                    gridTemplateColumns: '2fr 1fr 1fr 1fr',
                                    gap: '4rem',
                                    height: 'fit-content',
                                    minHeight: '7.2rem',
                                    padding: '1.6rem 0',
                                    backgroundColor: currentTab === SituacaoProcessamentoLoteEnum.FALHA ? 'rgba(178, 18, 6, 0.08)' : '#F6F6F9'
                                }}
                                key={index}
                            >
                                <Table.Item.TitleSubTitle
                                    title={element?.nomeFantasia || '---'}
                                    subTitle={element?.cpfCnpj?.length > 11 ? maskCNPJ(element?.cpfCnpj || '') : maskCpf(element?.cpfCnpj || '')}
                                />
                                <Table.Item.TitleSubTitle
                                    titleStyles={{ fontSize: '1.2rem', lineHeight: '1.6rem', fontWeight: '400', color: '#3E4E65' }}
                                    title={
                                        {
                                            [SituacaoProcessamentoLoteEnum.FALHA]: 'Lotes com falhas',
                                            [SituacaoProcessamentoLoteEnum.AGENDADO]: 'Lotes agendados',
                                            [SituacaoProcessamentoLoteEnum.EM_PROCESSAMENTO]: 'Lotes processando',
                                            [SituacaoLote.RECUSADO]: 'Lotes recusados',
                                            [SituacaoLote.CANCELADO]: 'Lotes cancelados'
                                        }[currentTab]
                                    }
                                    subTitle={String(element?.quantidadeLotes || 0)}
                                />
                                <SituacaoBadge situacao={currentTab} />
                                <Button
                                    style={{ width: 'fit-content' }}
                                    iconLeft="/faturamento/assets/icons/audit.svg"
                                    themeButton="ihealth"
                                    typeButton="text"
                                    onClick={() =>
                                        router.push(
                                            `/painel-processamento-contas/prestador/${element?.prestadorId}?situacao=${currentTab}&competencia=${competencia}`
                                        )
                                    }
                                >
                                    Ver detalhes
                                </Button>
                            </Table.Card>
                        ))}
                        {!listaPrestadores?.length && <S.EmptyListMessage>Não há dados nesta seção</S.EmptyListMessage>}
                    </Table.Body>
                </Table.Root>
            )}
            <S.WrapperPagination>
                <Pagination
                    totalPage={pagination?.totalPaginas}
                    totalRegister={pagination?.totalRegistros}
                    actualPage={pagination?.paginaAtual}
                    setNumberPage={(pageNumber) => {
                        setCurrentPageNumber(pageNumber)
                        loadDados({
                            competencia,
                            codigoContratado: filtroCodigoContratado,
                            emAbertoPor: filtroEmAbertoPor,
                            situacao: currentTab,
                            pageNumber
                        })
                    }}
                />
                {!loadingDados && (
                    <p>
                        {pagination?.paginaAtual * pagination?.linhasPorPagina + 1} -{' '}
                        {listaPrestadores?.length + pagination?.paginaAtual * pagination?.linhasPorPagina}
                        {' de '}
                        {pagination?.totalRegistros}
                    </p>
                )}
            </S.WrapperPagination>
        </S.Container>
    )
}

export default ListaProcessamentoPrestadores
