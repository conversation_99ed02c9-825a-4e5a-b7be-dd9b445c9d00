export enum TipoLote {
    CONSULTA = 'CONSULTA',
    HONORARIO = 'HONORARIO',
    SPSADT = 'SPSADT',
    TRATAMENTO_ODONTOLOGICO = 'TRATAMENTO_ODONTOLOGICO',
    RECURSO_GLOSA = 'RECURSO_GLOSA',
    RESUMO_INTERNACAO = 'RESUMO_INTERNACAO'
}

export enum TipoEnvio {
    ELETRONICO = 'ELETRONICO',
    MANUAL = 'MANUAL'
}

export enum TipoEnvioLabel {
    ELETRONICO = 'Eletrônico',
    MANUAL = 'Manual'
}

export enum ModuloCobranca {
    MEDICO = 'MEDICO',
    ODONTO = 'ODONTO',
    RECURSO = 'RECURSO'
}

export enum SituacaoLote {
    AGUARDANDO_DOCUMENTACAO = 'AGUARDANDO_DOCUMENTACAO',
    PROCESSANDO = 'PROCESSANDO',
    EM_AUDITORIA = 'EM_AUDITORIA',
    EM_ANALISE = 'EM_ANALISE',
    FECHADO = 'FECHADO',
    CANCELADO = 'CANCELADO',
    RECUSADO = 'RECUSADO',
    DEVOLVIDO = 'DEVOLVIDO'
}

export enum SituacaoNotaFiscal {
    NAO_SOLICITADA = 'NAO_SOLICITADA',
    AGUARDANDO_ENVIO = 'AGUARDANDO_ENVIO',
    ENVIADA = 'ENVIADA',
    CONFIRMADA = 'CONFIRMADA',
    RECUSADA = 'RECUSADA'
}

export enum TipoEnvioNotaFiscal {
    PRE_PROCESSAMENTO = 'PRE_PROCESSAMENTO',
    POS_PROCESSAMENTO = 'POS_PROCESSAMENTO',
    DISPENSADO = 'DISPENSADO'
}

export enum TipoEnvioDoc {
    ELETRONICO = 'ELETRONICO',
    MANUAL = 'MANUAL'
}

export enum TipoDocumento {
    LAUDO_MEDICO = 'LAUDO_MEDICO',
    EXAME = 'EXAME',
    RECEITUARIO = 'RECEITUARIO',
    HISTORICO_MEDICO = 'HISTORICO_MEDICO',
    NOTA_FISCAL = 'NOTA_FISCAL',
    PROTOCOLO_RECEBIMENTO = 'PROTOCOLO_RECEBIMENTO',
    DEMONSTRATIVO_ANALISE = 'DEMONSTRATIVO_ANALISE',
    RECURSO_GLOSA = 'RECURSO_GLOSA',
    RELATORIO_ERROS = 'RELATORIO_ERROS',
    ARQUIVO_COBRANCA = 'ARQUIVO_COBRANCA',
    DOCUMENTACAO_DIVERSA = 'DOCUMENTACAO_DIVERSA'
}

export enum TipoFaturamento {
    NORMAL = 'NORMAL',
    FATURAMENTO = 'FATURAMENTO',
    RECURSO = 'RECURSO'
}

export enum SituacaoPrestadorAnalise {
    ABERTO = 'ABERTO',
    ENCERRADO = 'ENCERRADO',
    FECHADO = 'FECHADO',
    PAGO = 'PAGO',
    EMPTY = 'EMPTY'
}

export enum SituacaoCompeteciaContasAPagar {
    SEM_DEMONSTRATIVO = 'SEM_DEMONSTRATIVO',
    AGUARDANDO_ENVIO_NF = 'AGUARDANDO_ENVIO_NF',
    AGUARDANDO_APROVACAO_NF = 'AGUARDANDO_APROVACAO_NF',
    NF_APROVADA = 'NF_APROVADA',
    NF_REPROVADA = 'NF_REPROVADA',
    GERADO = 'GERADO',
    CANCELADO = 'CANCELADO'
}

export enum SituacaoGuiaGlosa {
    GLOSADA = 'GLOSADA',
    ENVIADA = 'ENVIADA',
    FECHADA = 'FECHADA'
}

export enum SituacaoGuia {
    ABERTA = 'ABERTA',
    ANALISADA = 'ANALISADA'
}

export enum TipoDiariaTaxaGas {
    DIARIA = 'DIÁRIA',
    TAXA = 'TAXA',
    GASOTERAPIA = 'GASOTERAPIA'
}

export enum ProcedimentoViaAcesso {
    UNICA = 'UNICA',
    MESMA_VIA = 'MESMA VIA',
    DIFERENTE_VIAS = 'DIFERENTE VIAS'
}

export enum MatlMedClassificacao {
    SEM_CLASSIFICACAO = 'SEM CLASSIFICAÇÃO',
    BEM_DURAVEL = 'BEM DURÁVEL',
    MATERIAL_CONSUMO = 'MATERIAL DE CONSUMO',
    MATERIAL_ESPECIAL = 'MATERIAL ESPECIAL',
    ORTESE = 'ÓRTESE',
    PROTESE = 'PRÓTESE',
    SINTESE = 'SÍNTESE',
    INSTRUMENTAL = 'INSTRUMENTAL'
}

export enum MatlMedSimproCodigoMercado {
    MEDICAMENTO = 'MEDICAMENTO',
    SANEANTE = 'SANEANTE',
    MATERIAL_HOSPITALAR = 'MATERIAL HOSPITALAR',
    REAGENTE = 'REAGENTE'
}

export enum HonorarioGrauParticipacao {
    PRIMEIRO_AUXILIAR = 'PRIMEIRO AUXILIAR',
    SEGUNDO_AUXILIAR = 'SEGUNDO AUXILIAR',
    TERCEIRO_AUXILIAR = 'TERCEIRO AUXILIAR',
    QUARTO_AUXILIAR = 'QUARTO AUXILIAR',
    INSTRUMENTADOR = 'INSTRUMENTADOR',
    ANESTESISTA = 'ANESTESISTA',
    AUXILIAR_ANESTESISTA = 'AUXILIAR ANESTESISTA',
    CONSULTO = 'CONSULTOR',
    PERFUSIONISTA = 'PERFUSIONISTA',
    PEDIATRA_NA_SALA_DE_PARTO = 'PEDIATRA NA SALA DE PARTO',
    AUXILIAR_SADT = 'AUXILIAR SADT',
    CLINICO = 'CLINICO',
    INTENSIVISTA = 'INTENSIVISTA'
}

export enum TabelaReferencia {
    BRASINDICE = 'BRASINDICE',
    SIMPRO = 'SIMPRO'
}

export enum GuiaFaturamentoTipoGuia {
    SPSADT = 'SPSADT',
    RESUMO_INTERNACAO = 'RESUMO INTERNACAO',
    CONSULTA = 'CONSULTA',
    HONORARIOS = 'HONORARIOS',
    ODONTO = 'ODONTO',
    GLOSA = 'GLOSA',
    INTERNACAO = 'INTERNACAO'
}

export enum TipoSolicitacaoGuia {
    SOLICITACAO_INTERNACAO = 'SOLICITACAO_INTERNACAO',
    GUIA_DE_CONSULTA = 'GUIA_DE_CONSULTA',
    SP_SADT = 'SP_SADT',
    RESUMO_DE_INTERNACAO = 'RESUMO_DE_INTERNACAO',
    PRORROGACAO_DE_INTERNACAO = 'PRORROGACAO_DE_INTERNACAO',
    GUIA_DE_HONORARIOS = 'GUIA_DE_HONORARIOS',
    TRATAMENTO_ODONTOLOGICO = 'TRATAMENTO_ODONTOLOGICO',
    TRATAMENTO_ODONTOLOGICO_INICIAL = 'TRATAMENTO_ODONTOLOGICO_INICIAL',
    ANEXO_DE_OUTRAS_DESPESAS = 'ANEXO_DE_OUTRAS_DESPESAS',
    SOLICITACAO_DE_OPME = 'SOLICITACAO_DE_OPME',
    SOLICITACAO_DE_QUIMIOTERAPIA = 'SOLICITACAO_DE_QUIMIOTERAPIA',
    SOLICITACAO_DE_RADIOTERAPIA = 'SOLICITACAO_DE_RADIOTERAPIA',
    COMPROVANTE_PRESENCIAL = 'COMPROVANTE_PRESENCIAL'
}

export enum TipoArquivoModeloCSV {
    CONSULTA = 'CONSULTA',
    HONORARIO = 'HONORARIO',
    SPSADT = 'SPSADT',
    TRATAMENTO_ODONTOLOGICO = 'TRATAMENTO_ODONTOLOGICO',
    RESUMO_INTERNACAO = 'RESUMO_INTERNACAO',
    ANEXO_DE_OUTRAS_DESPESAS = 'ANEXO_DE_OUTRAS_DESPESAS'
}

export enum SituacaoPrestadorAnaliseDeContas {
    NAO_INICIADO = 'NAO_INICIADO',
    EM_ANALISE = 'EM_ANALISE',
    FINALIZADO = 'FINALIZADO'
}

export enum StatusHistoricoRAH {
    CONTESTADO = 'Contestado',
    EM_VALIDACAO = 'Em validação',
    REALIZADO = 'Realizado',
    DISPENSADO = 'Dispensado',
    CANCELADO = 'Cancelado'
}

export enum StatusAuditoriaHistoricoRAH {
    AUTORIZACAO_MEDICO_PENDENTE = 'AUTORIZACAO_MEDICO_PENDENTE',
    AUTORIZACAO_PRESTADOR_PENDENTE = 'AUTORIZACAO_PRESTADOR_PENDENTE',
    EM_ANDAMENTO = 'EM_ANDAMENTO',
    REALIZADA = 'REALIZADA',
    CONTESTADA = 'CONTESTADA',
    CANCELADA = 'CANCELADA'
}
