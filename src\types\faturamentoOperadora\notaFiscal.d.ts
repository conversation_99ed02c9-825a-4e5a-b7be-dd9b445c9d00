export interface INotaFiscalDTO {
    tipoFaturamento?: string
    id: number
    uuid: string
    nomePrestador: string
    cnpj: string
    numeroNF: string
    situacao: string
    competencia: string
    dataCadastroNF: string
    valorBruto: number
    existeBloqueio: any
    reprovadaAnteriormente: any
    dataAnalise: string
    dataEmissaoNF: string
    dataEnvioNF: string
    motivoReprovacao: any
    tipoNF: string
}
