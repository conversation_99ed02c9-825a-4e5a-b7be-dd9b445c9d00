import Input from 'components/atoms/Inputs/Input'
import Item from 'components/atoms/Item'
import DragAndDrop from 'components/molecules/DragAndDrop'
import Progress from 'components/molecules/Progress'
import TemplateModal from 'components/organisms/TemplateModal'
import { useRouter } from 'next/router'
import { useCallback, useEffect, useState } from 'react'
import { ReactSVG } from 'react-svg'
import { useToast } from 'src/hooks/toast'
import { CobrancaServices } from 'src/services/faturamentoPrestadorApi/cobrancaServices'
import { LocalData } from 'types/cobrancaPrestador/loteCobranca'
import { TipoArquivoModeloCSV, TipoEnvioNotaFiscal } from 'types/common/enums'
import { base64ToFile, fileToBase64 } from 'utils/fileUtils'
import * as S from './styles'
import { IDetalhesLote, IGetLotes } from 'src/services/faturamentoPrestadorApi/geracao-recurso-glosa/types'
import { RecursoGlosaServices } from 'src/services/faturamentoPrestadorApi/recursoGlosaServices'
import { Button } from '@mui/material'

interface ModalRecursarLoteProps {
    isOpen: boolean
    setIsOpen: (isOpen: boolean) => void
    loteListInfo: IGetLotes
    getList: () => void
    infoLotes: IDetalhesLote
}

const ModalRecursarLote = ({ isOpen, setIsOpen, loteListInfo, getList, infoLotes }: ModalRecursarLoteProps) => {
    const route = useRouter()
    const { addToast } = useToast()
    const [step, setStep] = useState(1)
    const [fileLotUploadModal, setFileLotUploadModal] = useState<File | null>(null)
    const [loadingFileLot, setLoadingFileLot] = useState(false)
    const [disableButton, setDisableButton] = useState(false)

    function resetStates() {
        setStep(1)
        setFileLotUploadModal(null)
    }

    function uploadLotFile(file: File | null) {
        if (file !== null) {
            setFileLotUploadModal(file)
            setDisableButton(true)
            RecursoGlosaServices.postLoteEnvioEletronico({ arquivo: file })
                .then(({ data }) => {
                    resetStates()
                    getList()
                    setIsOpen(false)
                    addToast({
                        title: `Lote gerado e enviado para processamento`,
                        type: 'success'
                    })
                })
                .catch(({ response }) => {
                    addToast({
                        title: response?.data?.message
                            ? response?.data?.message
                            : 'Ocorreu erro ao enviar o lote de recurso de glosa. Tente novamente.',
                        duration: 4000,
                        type: 'error'
                    })
                    setFileLotUploadModal(null)
                })
                .finally(() => setDisableButton(false))
        } else {
            setFileLotUploadModal(null)
        }
    }

    const handleSetUploadedFile = (file: File) => {
        setFileLotUploadModal(file)
    }

    useEffect(() => {
        if (!isOpen) {
            resetStates()
        }
    }, [isOpen])

    return (
        <TemplateModal
            title={
                step === 2
                    ? loadingFileLot
                        ? 'Adicionando arquivo...'
                        : fileLotUploadModal
                        ? 'Arquivo adicionado'
                        : 'Adicionar o arquivo XML'
                    : `Como deseja recursar lote ${loteListInfo?.identificadorLote}?`
            }
            isOpen={isOpen}
            setIsOpen={setIsOpen}
            showIconClosed
            closeOnTouchOverlay
            style={{ width: '600px' }}
        >
            <S.ContentModal>
                {step === 1 && (
                    <>
                        <S.CardButton
                            onClick={() => {
                                setStep(2)
                            }}
                        >
                            <p>Automático</p>
                            <span>Enviar um arquivo XML gerado automaticamente por sistema no padrão TISS.</span>
                        </S.CardButton>
                        {!infoLotes?.possuiLoteRecursoEnviado && (
                            <S.CardButton
                                onClick={() => {
                                    setIsOpen(false)
                                    route.push(`/prestador/medico/cobranca/lotes/${loteListInfo?.loteId}`)
                                }}
                            >
                                <p>Manual</p>
                                <span>Selecionar manualmente as guias com glosa.</span>
                            </S.CardButton>
                        )}
                    </>
                )}

                {step === 2 && (
                    <S.UploadSection>
                        {fileLotUploadModal !== null && (
                            <Progress
                                name={fileLotUploadModal?.name}
                                onClick={() => {
                                    uploadLotFile(null)
                                }}
                                setLoadingFile={setLoadingFileLot}
                                permiteRemover
                            />
                        )}

                        {fileLotUploadModal === null && (
                            <DragAndDrop
                                file={fileLotUploadModal}
                                acceptExtensions={'.xml'}
                                uploadFile={handleSetUploadedFile}
                                setLoadingFile={fileLotUploadModal}
                            />
                        )}

                        <S.UploadDescription>
                            Adicione apenas um arquivo em XML, com tamanho máximo de <strong>2MB</strong>
                        </S.UploadDescription>
                        <S.ActionWrapper>
                            <Button
                                variant="text"
                                color="neutral"
                                onClick={() => {
                                    setIsOpen(false)
                                }}
                            >
                                Cancelar
                            </Button>

                            <Button
                                color="warning"
                                disabled={fileLotUploadModal === null || loadingFileLot || disableButton}
                                onClick={() => uploadLotFile(fileLotUploadModal)}
                            >
                                Próximo
                            </Button>
                        </S.ActionWrapper>
                    </S.UploadSection>
                )}
            </S.ContentModal>
        </TemplateModal>
    )
}

export default ModalRecursarLote
