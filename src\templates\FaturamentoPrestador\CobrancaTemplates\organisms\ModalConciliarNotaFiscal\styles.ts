import styled from 'styled-components'
export const ContentModal = styled.div`
    display: flex;
    flex-direction: column;

    p {
        margin: 16px 0px;
    }

    table {
        margin-top: 12px;
        font-size: 12px;
    }

    td {
        height: auto;
        padding: 10px 5px;
        display: flex;
        align-items: center;
    }

    .disclaimer {
        font-size: 16px;
        color: #f00;
    }

    .nota-fiscal {
        color: #2b45d4;
        font-weight: 600;
    }

    .visualizar {
        justifycontent: flex-end;
        cursor: pointer;
        color: #2b45d4;
    }
`

export const ContainerBottom = styled.div`
    display: flex;
    justify-content: end;
    align-items: center;
    gap: 10px;
    margin-top: 2rem;
    margin-bottom: 0.5rem;
    button {
        width: fit-content;
        padding: 12px 24px;
    }
    button > span {
        font-size: 1.4rem !important;
    }
`
