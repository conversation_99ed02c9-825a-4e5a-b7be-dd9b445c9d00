import styled from 'styled-components'
import theme from 'styles/theme'

export const Content = styled.div`
    display: flex;
    flex-direction: column;
    gap: 1.6rem;
`

export const Header = styled.div`
    margin-bottom: 0.8rem;
    padding-bottom: 1.6rem;
    border-bottom: 1px solid ${theme.colors.black[16]};
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2.4rem;

    .title {
        font-size: 2.4rem;
        line-height: 3.2rem;
        font-weight: 600;
        color: ${theme.colors.black[88]};
    }

    .refresh-button {
        width: 4.8rem;
        height: 4.8rem;
        border-radius: 50%;
        display: grid;
        place-items: center;
        cursor: pointer;
        background-color: rgba(43, 69, 212, 0.04);

        transition: background-color 0.2s;
        &:hover {
            background-color: ${theme.colors.black[16]};
        }
    }
`

export const ButtonsWrapper = styled.div`
    margin-top: 0.8rem;
    display: flex;
    justify-content: right;
    gap: 1.6rem;
`
