import React from 'react'
import { useEffect, useState } from 'react'

import { ReactSVG } from 'react-svg'

import Button from 'components/atoms/Button'

import NavTabs, { Tab } from 'components/molecules/NavTabs'

import Modal from 'components/atoms/Modal'
import InputDate from 'components/molecules/InputDate'
import SimpleSelect from 'components/molecules/SimpleSelect'
import { useToast } from 'src/hooks/toast'
import { GuiaLoteManualService } from 'src/services/faturamentoPrestadorApi/lote-manual-guia'
import { TipoLoteManualGuiaEnum } from 'src/services/faturamentoPrestadorApi/lote-manual-guia/enuns'
import { IGuiaLoteManualDTO, IPeriodoCobrancaPayload } from 'src/services/faturamentoPrestadorApi/lote-manual-guia/types'
import { DateUtils } from 'utils/dateUtils'
import { handleFieldsChange } from 'utils/form-utils'
import { resetObjectData } from 'utils/functions'
import { getMessageErrorFromApiResponse } from 'utils/stringUtils'
import * as S from './styles'
import moment from 'moment'

export interface IFieldValues {
    dataFinalFaturamento: Date | string
    dataInicioFaturamento: Date | string
    tipoFaturamento: IOptions
}

export interface IOptions {
    label: string
    value: 'PARCIAL' | 'FINAL' | 'COMPLEMENTAR' | 'TOTAL'
}

type ModalType = {
    guideData: IGuiaLoteManualDTO
    isOpen: boolean
    setIsOpen: React.Dispatch<React.SetStateAction<boolean>>
    setPeriodoRegistrado: React.Dispatch<React.SetStateAction<boolean>>
    cobrarItem: () => void
}

function ModalInfoCobranca({ guideData, isOpen = false, setIsOpen, cobrarItem, setPeriodoRegistrado }: ModalType) {
    const [fieldValues, setFieldValues] = useState<IFieldValues>()
    const [refreshModal, setRefreshModal] = useState<boolean>(false)
    const [btnDisable, setBtnDisable] = useState<boolean>(true)
    const { addToast } = useToast()

    function handleClickToPeriodoCobranca() {
        const payload: IPeriodoCobrancaPayload = {
            dataFinalFaturamento: moment(fieldValues?.dataFinalFaturamento).format('YYYY-MM-DD') + 'T' + '00:00',
            dataInicioFaturamento: moment(fieldValues?.dataInicioFaturamento).format('YYYY-MM-DD') + 'T' + '00:00',
            tipoFaturamento: fieldValues?.tipoFaturamento?.value
        }

        GuiaLoteManualService?.patchPeriodoCobranca(payload, guideData?.uuid)
            .then(() => {
                setPeriodoRegistrado(true)
                cobrarItem()
            })
            .catch((err) => {
                addToast({
                    type: 'error',
                    title: 'Erro ao tentar cobrar o item',
                    duration: 5000,
                    description: getMessageErrorFromApiResponse(err)
                })
            })
            .finally(() => {
                setFieldValues(resetObjectData(fieldValues))
                setIsOpen(false)
                setRefreshModal(!refreshModal)
            })
    }

    // VERIFICA SE OS CAMPOS DE HORA ESTÃO PREENCHIDOS

    function isEmpty(fieldValues: IFieldValues) {
        if (guideData?.tipo === TipoLoteManualGuiaEnum.RESUMO_INTERNACAO || guideData?.tipo === TipoLoteManualGuiaEnum.HONORARIOS) {
            if (guideData?.tipo === TipoLoteManualGuiaEnum.RESUMO_INTERNACAO) {
                return fieldValues?.dataFinalFaturamento && fieldValues?.dataInicioFaturamento && fieldValues?.tipoFaturamento
            } else {
                return fieldValues?.dataFinalFaturamento && fieldValues?.dataInicioFaturamento
            }
        } else {
            return true
        }
    }

    // VERIFICA TIPO DE GUIA E CHAMA O RESPECTIVO PATCH

    function handleClickToFinishBtn() {
        if (guideData?.tipo === TipoLoteManualGuiaEnum.RESUMO_INTERNACAO || guideData?.tipo === TipoLoteManualGuiaEnum.HONORARIOS) {
            handleClickToPeriodoCobranca()
        } else {
            cobrarItem()
        }
    }

    useEffect(() => {
        const empty = isEmpty(fieldValues)
        setBtnDisable(!empty)
    }, [fieldValues, guideData])

    return (
        <Modal onClose={() => setIsOpen(false)} isOpen={isOpen} style={{ padding: '24px', width: '783px' }}>
            <S.WrapperModal key={refreshModal as any}>
                <h1>Informar período de cobrança</h1>

                {guideData?.tipo === TipoLoteManualGuiaEnum.RESUMO_INTERNACAO && (
                    <p>
                        Para as guias de resumo de internação é necessário informar o tipo de faturamento e período de cobrança para validação das
                        regras de cobrança.
                    </p>
                )}

                {guideData?.tipo === TipoLoteManualGuiaEnum.HONORARIOS && (
                    <p>Para as guias de honorário é necessário informar o período de cobrança para validação das regras de cobrança.</p>
                )}

                {guideData?.tipo === TipoLoteManualGuiaEnum.RESUMO_INTERNACAO && (
                    <SimpleSelect
                        options={[
                            { label: 'PARCIAL', value: 'PARCIAL' },
                            { label: 'FINAL', value: 'FINAL' },
                            { label: 'COMPLEMENTAR', value: 'COMPLEMENTAR' },
                            { label: 'TOTAL', value: 'TOTAL' }
                        ]}
                        onChange={(event) => {
                            setFieldValues({
                                ...fieldValues,
                                tipoFaturamento: event
                            })
                        }}
                        isClearable
                        defaultValue={''}
                        required
                        label="Tipo de faturamento"
                    />
                )}

                {(guideData?.tipo === TipoLoteManualGuiaEnum.RESUMO_INTERNACAO || guideData?.tipo === TipoLoteManualGuiaEnum.HONORARIOS) && (
                    <S.WrapperInputs>
                        <InputDate
                            minDate={moment().add(-10, 'year').toDate()}
                            value={
                                fieldValues?.dataInicioFaturamento !== undefined && fieldValues?.dataInicioFaturamento !== null
                                    ? new Date(fieldValues?.dataInicioFaturamento)
                                    : null
                            }
                            label="Período de cobrança - Inicial"
                            required={true}
                            placeholder="00/00/00"
                            onChange={(date: Date) => {
                                setFieldValues({
                                    ...fieldValues,
                                    dataInicioFaturamento: date?.toString()
                                })
                            }}
                            key={'input-date-1'}
                        />

                        <InputDate
                            value={
                                fieldValues?.dataFinalFaturamento !== undefined && fieldValues?.dataFinalFaturamento !== null
                                    ? new Date(fieldValues?.dataFinalFaturamento)
                                    : null
                            }
                            required={true}
                            label="Período de cobranca - Final"
                            minDate={moment().add(-1, 'month').toDate()}
                            placeholder="00/00/00"
                            onChange={(date: Date) => {
                                setFieldValues({
                                    ...fieldValues,
                                    dataFinalFaturamento: date?.toString()
                                })
                            }}
                            key={'input-date-2'}
                        />
                    </S.WrapperInputs>
                )}

                <S.WrapperButtonsModal>
                    <Button typeButton="text" themeButton="gray" onClick={() => setIsOpen(false)}>
                        Cancelar
                    </Button>
                    <Button themeButton="secondary" onClick={() => handleClickToFinishBtn()} disabled={btnDisable}>
                        Finalizar cobrança
                    </Button>
                </S.WrapperButtonsModal>
            </S.WrapperModal>
        </Modal>
    )
}

export default ModalInfoCobranca
