import React, { HtmlHTMLAttributes, useContext, createContext, useState } from 'react'
import { Wrapper } from './styles'

interface IFiltroContext<T = unknown> {
    filter: T
    setFilter: React.Dispatch<T>
    onSubmit: React.MouseEventHandler<HTMLButtonElement>
    onClear: React.MouseEventHandler<HTMLButtonElement>
    setShow: React.Dispatch<boolean>
    setFieldValue: (value: T, field: T) => void
}

export const FiltroContext = createContext({
    filter: undefined,
    setFilter: function (value: unknown): void {
        throw new Error('Function not implemented.')
    },
    onSubmit: function (event: React.MouseEvent<HTMLButtonElement, MouseEvent>): void {
        throw new Error('Function not implemented.')
    },
    onClear: function (event: React.MouseEvent<HTMLButtonElement, MouseEvent>): void {
        throw new Error('Function not implemented.')
    },
    setShow: function (value: boolean): void {
        throw new Error('Function not implemented.')
    },
    setFieldValue: function (value: never, field: never): void {
        throw new Error('Function not implemented.')
    }
})

const useFiltroContext = <T,>() => {
    const context = useContext<IFiltroContext<T>>(FiltroContext)

    if (!context) {
        throw new Error('useInputContext precisa estar dentro do FiltroLateralContext.Provider')
    }

    return context
}

export { useFiltroContext }

interface IFilterProps<T> {
    filter: T
    onSubmitFilter: React.Dispatch<React.SetStateAction<T>>
    setShow: React.Dispatch<boolean>
    show: boolean
}

export default function Root<T>({ filter, onSubmitFilter, setShow, show = false, ...props }: IFilterProps<T> & HtmlHTMLAttributes<HTMLDivElement>) {
    const [values, setValues] = useState<T>(filter)

    const handleOnSubmit: React.MouseEventHandler<HTMLButtonElement> = (event) => {
        event?.preventDefault()
        onSubmitFilter(values)
        setShow(false)
    }

    const handleOnClear: React.MouseEventHandler<HTMLButtonElement> = (event) => {
        event?.preventDefault()
        onSubmitFilter(undefined)
        setValues(undefined)
    }

    function handleOnChange(value: T[keyof T], field: string) {
        setValues({ ...values, [field]: value })
    }

    return (
        <FiltroContext.Provider
            value={{
                filter: values,
                setFilter: setValues,
                onSubmit: handleOnSubmit,
                onClear: handleOnClear,
                setShow,
                setFieldValue: handleOnChange
            }}
        >
            <Wrapper style={{ display: !show ? 'none' : '' }} {...props} />
        </FiltroContext.Provider>
    )
}
