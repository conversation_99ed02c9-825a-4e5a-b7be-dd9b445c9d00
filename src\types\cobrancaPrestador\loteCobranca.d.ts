import { ModuloCobranca, SituacaoLote, TipoEnvio, TipoLote, TipoDocumento, TipoEnvioNotaFiscal } from '../common/enums'
import { IPageResult } from '../common/pagination'

export type IPageLote = IPageResult<ILoteCobrancaDTO>

interface IErrosXml {
    coluna: string
    linha: string
    messagem: string
}

export interface ISolicitacaoEnvioEletronicoDTO {
    arquivoValido: boolean
    erros: IErrosXml[]
    loteEnviado: ILoteCobrancaDTO
    nomeArquivo: string
}

export interface ILoteCobrancaDTO {
    competencia: string
    dataCancelamento: Date
    dataRecusa: Date
    documentosObrigatorios: string[]
    loteCobrancaId: string
    prestadorId?: string
    numeroLote: number
    dataEnvio: string
    dataProcessamento: string
    tipoLote: TipoLote
    tipoEnvio: TipoEnvio
    moduloCobranca?: ModuloCobranca
    situacaoLote: SituacaoLote
    numeroLotePrestador?: string
    codigoContratado?: string
    quantidadeGuias: number
    nomeUsuario: string
    tipoEnvioNotaFiscal?: TipoEnvioNotaFiscal
    documentosObrigatorios?: TipoDocumento[]
    valorApresentado?: number
    situacaoNotaFiscal?: any
    valorGlosado?: number
    valorApurado?: number
}

type FileBase64 = {
    data: string
    name: string
}

type LocalData = {
    fileBase64?: FileBase64
    result?: ILoteCobrancaDTO
}

export interface ILotNfForm {
    numeroNota: string
    valor: string
    servicoPrestado: string
    dataEmissao: string
    nfFiles: File[]
    conciliarNotaFiscal?: boolean
}

export interface IGetTipoEnvioNfPrestador {
    tipoEnvioNotaFiscal: TipoEnvioNotaFiscal
}
