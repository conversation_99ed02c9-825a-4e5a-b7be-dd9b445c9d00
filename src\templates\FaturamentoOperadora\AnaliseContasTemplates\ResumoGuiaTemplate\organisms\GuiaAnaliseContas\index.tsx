/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useMemo } from 'react'
import GeneralInformation from '../TabInformacoesGerais'
import NavTabs, { Tab } from 'components/molecules/NavTabs'
import * as S from './styles'
import TabItemGuia from '../TabItemGuia'
import { IGuiaInfoGeraisQuery } from 'types/analiseContas/guia'
import Status from 'components/atoms/Status'
import { IItemGuiaDTO } from 'types/analiseContas/guiasLote'
import { ProfilesEnum } from 'src/enum/profiles.enum'
import { useAuth } from 'src/hooks/auth'
import { getCurrentClient } from 'utils/parseAssets'
import { ClientEnum } from 'utils/parseAssets/client-enum'

const GuiaAnaliseContas = ({
    uuidGuia,
    infoGeraisGuia,
    itensGuia,
    setItensGuia
}: {
    uuidGuia: string
    infoGeraisGuia: IGuiaInfoGeraisQuery
    itensGuia: IItemGuiaDTO
    setItensGuia: React.Dispatch<React.SetStateAction<IItemGuiaDTO>>
}) => {
    const { userRoles, isAdmin } = useAuth()

    const objectLength = (obj: any) => {
        if (!obj) return 0

        return obj.length
    }

    // const list = useMemo(() => {
    //     if (Object.keys(itensGuia)?.length > 0) {
    //         return {
    //             honoraios: itensGuia.itensGuiaHonorarioDTO,
    //             procedimentos: itensGuia.itensGuiaProcedimentoDTO,
    //             taxasDiariasGases: [
    //                 ...(itensGuia.itensGuiaTaxaDTO || []),
    //                 ...(itensGuia.itensGuiaDiariaDTO || []),
    //                 ...(itensGuia.itensGuiaGasoterapiaDTO || [])
    //             ],
    //             materiaiseopme: [...(itensGuia.itensGuiaMaterialDTO || []), ...(itensGuia.itensGuiaOPMEDTO || [])],
    //             medicamentos: itensGuia.itensGuiaMedicamentoDTO
    //         }
    //     } else {
    //         return {
    //             honoraios: [],
    //             procedimentos: [],
    //             taxasDiariasGases: [],
    //             materiaiseopme: [],
    //             medicamentos: []
    //         }
    //     }
    // }, [itensGuia])

    const tabs: Tab[] = [
        {
            label: 'Informações Gerais',
            component: <GeneralInformation uuidGuia={uuidGuia} infoGeraisGuia={infoGeraisGuia} />,
            identifier: 'INFORMACOES_GERAIS'
        },
        {
            label: (
                <S.TabLabel>
                    <>Honorários</>
                    <Status status="primary" text={objectLength(itensGuia?.itensGuiaHonorarioDTO)} />
                </S.TabLabel>
            ),
            component: (
                <TabItemGuia
                    infoGeraisGuia={infoGeraisGuia}
                    setItensGuia={setItensGuia}
                    itensGuia={itensGuia?.itensGuiaHonorarioDTO}
                    permission={userRoles.includes('MEDICO_AUDITOR') || isAdmin}
                />
            ),
            identifier: 'HONORARIOS'
        },
        {
            label: (
                <S.TabLabel>
                    <>Procedimentos</>
                    <Status status="primary" text={objectLength(itensGuia?.itensGuiaProcedimentoDTO)} />
                </S.TabLabel>
            ),
            component: (
                <TabItemGuia
                    infoGeraisGuia={infoGeraisGuia}
                    setItensGuia={setItensGuia}
                    itensGuia={itensGuia?.itensGuiaProcedimentoDTO}
                    permission={userRoles.includes('MEDICO_AUDITOR') || isAdmin}
                />
            ),
            identifier: 'PROCEDIMENTOS'
        },
        {
            label: (
                <S.TabLabel>
                    <>Taxas, Diárias e Gases</>
                    <Status
                        status="primary"
                        text={objectLength([
                            ...(itensGuia?.itensGuiaTaxaDTO || []),
                            ...(itensGuia?.itensGuiaDiariaDTO || []),
                            ...(itensGuia?.itensGuiaGasoterapiaDTO || [])
                        ])}
                    />
                </S.TabLabel>
            ),
            component: (
                <TabItemGuia
                    setItensGuia={setItensGuia}
                    infoGeraisGuia={infoGeraisGuia}
                    itensGuia={[
                        ...(itensGuia?.itensGuiaTaxaDTO || []),
                        ...(itensGuia?.itensGuiaDiariaDTO || []),
                        ...(itensGuia?.itensGuiaGasoterapiaDTO || [])
                    ]}
                    permission={userRoles.includes('ENFERMEIRO_AUDITOR') || isAdmin}
                />
            ),
            identifier: 'TAXAS_DIARIAS_GASES'
        },
        {
            label: (
                <S.TabLabel>
                    <>Materiais e OPME</>
                    <Status
                        status="primary"
                        text={objectLength([...(itensGuia?.itensGuiaMaterialDTO || []), ...(itensGuia?.itensGuiaOPMEDTO || [])])}
                    />
                </S.TabLabel>
            ),
            component: (
                <TabItemGuia
                    infoGeraisGuia={infoGeraisGuia}
                    setItensGuia={setItensGuia}
                    itensGuia={[...(itensGuia?.itensGuiaMaterialDTO || []), ...(itensGuia?.itensGuiaOPMEDTO || [])]}
                    permission={userRoles.includes('ENFERMEIRO_AUDITOR') || isAdmin}
                />
            ),
            identifier: 'MATERIAIS_OPME'
        },
        {
            label: (
                <S.TabLabel>
                    <>Medicamentos</>
                    <Status status="primary" text={objectLength(itensGuia?.itensGuiaMedicamentoDTO)} />
                </S.TabLabel>
            ),
            component: (
                <TabItemGuia
                    infoGeraisGuia={infoGeraisGuia}
                    setItensGuia={setItensGuia}
                    itensGuia={itensGuia?.itensGuiaMedicamentoDTO}
                    permission={userRoles.includes('ENFERMEIRO_AUDITOR') || isAdmin}
                />
            ),
            identifier: 'MEDICAMENTOS'
        }
    ]

    const getNavTabsByProfile = () => {
        const configTabsByProfile = {
            [ProfilesEnum.MEDICO_AUDITOR]: [
                'INFORMACOES_GERAIS',
                'HONORARIOS',
                'PROCEDIMENTOS',
                'TAXAS_DIARIAS_GASES',
                'MATERIAIS_OPME',
                'MEDICAMENTOS'
            ],
            [ProfilesEnum.ENFERMEIRO_AUDITOR]: [
                'INFORMACOES_GERAIS',
                'HONORARIOS',
                'PROCEDIMENTOS',
                'TAXAS_DIARIAS_GASES',
                'MATERIAIS_OPME',
                'MEDICAMENTOS'
            ]
        }

        return isAdmin || getCurrentClient() !== ClientEnum.GDF
            ? tabs
            : tabs?.filter((tabs) => userRoles?.some((p) => configTabsByProfile[p]?.includes(tabs?.identifier)))
    }

    return (
        <S.Container>
            <NavTabs tabs={getNavTabsByProfile()} />
        </S.Container>
    )
}

export default GuiaAnaliseContas
