import { situacaoEnum } from 'utils/enum/situatio-enum'
import { tipoPessoaEnum } from 'utils/enum/tipo-pessoa-enum'

export interface IUsuarioDTO {
    email: string
    id: number
    nome: string
    token: string
}

export interface IPrestadorDiariaDTO {
    codigoTuss: string
    id: number
    prestadorServicoId: number
}

export interface IPrestadorEspecialidadeDTO {
    caraterAtendimento: 'ELETIVO' | 'URGENCIA_EMERGENCIA'
    descricao: string
    id: number
    idEspecialidadeTabAuxApi: number
    prestadorServicoId: number
    tipoAtendimento: ITipoAtendimentoDTO
}

export interface IPrestadorProcedimentoDTO {
    codigoTussFinal: string
    codigoTussInicial: string
    id: number
    prestadorServicoId: number
}

export interface IPrestadorTaxasGasDTO {
    codigoTuss: string
    id: number
    prestadorServicoId: number
}

export interface IPrestadorServicoDTO {
    faturaDieta: boolean
    faturaMaterial: boolean
    faturaMedicamento: boolean
    id: number
    prestadorDiarias: IPrestadorDiariaDTO[]
    prestadorEspecialidades: IPrestadorEspecialidadeDTO[]
    prestadorProcedimentos: IPrestadorProcedimentoDTO[]
    prestadorTaxasGas: IPrestadorTaxasGasDTO[]
}

export interface IRegraPagamentoDTO {
    aliquota: IAliquotaDTO
    dadoBancario: IDadoBancarioDTO
    id: number
    simplesNacional: simplesNacionalEnum
}

export interface IPrestadorDTO {
    cnesId: number
    cnesProprio: boolean
    cnpj: string
    codigoCnes: string
    codigoOperadoraIntermediaria: string
    cpf: string
    dataCredenciamento: string
    endereco: IEnderecoDTO
    etapaPreCadastro: 'PRIMEIRA' | 'SEGUNDA' | 'TERCEIRA' | 'QUARTA' | 'FINALIZADA'
    uuid: string
    idPrestadorMatriz: number
    inscricaoEstadual: string
    inscricaoMunicipal: string
    nomeCompleto: string
    nomeFantasia: string
    prazoSuspensao: string
    prestadorServico: IPrestadorServicoDTO
    razaoSocial: string
    regraPagamento: IRegraPagamentoDTO
    site: string
    situacao: situacaoEnum
    tipoPessoa: tipoPessoaEnum
    tipoPrestador: string
    tipoPrestadorId: number
    usuario: IUsuarioDTO
    categoria: string
    emailAcessoEco: string
}

export interface IEnderecoDTO {
    bairro: string
    cep: string
    codigoIbgeMunicipio: string
    complemento: string
    email: string
    id: number
    idBairro: number
    idEstado: number
    logradouro: string
    municipio: string
    numero: string
    telefones: string[]
    tipoEndereco: 'SEDE' | 'UNIDADE_ATENDIMENTO'
    uf: string
}

export interface IPrestadorEnderecoDTO {
    bairro: string
    cnpj: string
    codigoIbgeMunicipio: string
    complemento: string
    cpf: string
    idEstadoApi: number
    logradouro: string
    municipio: string
    nomeCompleto: string
    nomeFantasia: string
    numero: string
    uuid: string
}
