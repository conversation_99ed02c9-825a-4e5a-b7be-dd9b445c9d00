// import { CaraterAtendimentoEnum } from 'src/services/auditoria-services/censo-hospitalar/enums';
import styled from 'styled-components'

export enum CaraterAtendimentoEnum {
    'Urgência/Emergência' = 'URGENCIA',
    'Eletivo' = 'ELETIVO'
}

export const Wrapper = styled.div`
    display: flex;
    align-items: center;
    justify-content: flex-start;
`

const caraterType = {
    [CaraterAtendimentoEnum['Urgência/Emergência']]: `
    color: rgba(178, 18, 6, 1);
    border-color: rgba(178, 18, 6, 1);
    background-color: rgba(178, 18, 6, 0.08);
    `,
    [CaraterAtendimentoEnum.Eletivo]: `
    color: rgba(0, 86, 133, 1);
    border-color: rgba(0, 86, 133, 1);
    background-color: rgba(0, 86, 133, 0.08);
    `
}

export const Badge = styled.div<{ type: CaraterAtendimentoEnum }>`
    display: flex;
    gap: 8px;
    border-radius: 8px;
    height: 32px;
    padding: 8px;
    border: 1px solid;
    ${({ type }) => caraterType[type]}
`
