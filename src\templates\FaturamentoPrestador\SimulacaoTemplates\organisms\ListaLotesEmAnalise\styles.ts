import styled from 'styled-components'

export const Wrapper = styled.div`
    background: #fff;

    .contentInput {
        display: flex;
        justify-content: flex-end;
        align-items: center;

        div {
            max-width: 592px;
        }

        .contentNumber {
            display: flex;
            .pageNumber {
                font-weight: 400;
                font-size: 16px;
                line-height: 24px;
                margin-right: 20px;
            }

            svg {
                cursor: pointer;
            }
        }
    }

    .itemClose {
        display: flex;
        align-items: center;
        gap: 19px;

        .item-new {
            padding: 4px 8px;
            background: #2b45d4;
            color: #fff;
            border-radius: 50px;
            font-weight: 600;
            font-size: 10px;
            line-height: 14px;
        }

        .text-new-bold {
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;
            color: rgba(0, 0, 0, 0.88);
        }
    }
`
export const CheckboxWrapper = styled.div`
    margin: 24px 0;
    display: flex;
    gap: 16px;
`
export const ContentDropLot = styled.div`
    display: flex;
    align-items: center;
    justify-content: space-between;
    p {
        font-family: 'Open Sans';
        font-style: normal;
        font-weight: 400;
        font-size: 16px;
        line-height: 24px;
        /* identical to box height, or 150% */

        color: #000000;
    }
`

export const HeaderLabel = styled.div`
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    margin: 16px 0;
    padding: 0 8px;

    div {
        p {
            font-weight: 600;
            font-size: 12px;
            line-height: 16px;
        }
    }
`

export const RowButtons = styled.div`
    display: flex;
    gap: 16px;
`

export const HeaderSubtitle = styled.div`
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 16px 8px;
    gap: 10px;
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;

    color: rgba(0, 0, 0, 0.56);
`
