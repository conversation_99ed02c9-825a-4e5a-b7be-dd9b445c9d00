import * as S from './styles'
import moment from 'moment'
import React, { useEffect, useCallback, useState } from 'react'
import { IPagination } from 'types/common/pagination'
import { IPrestadorAnaliseQuery } from 'types/cobrancaPrestador/prestadorAnalise'
import { ILoteCancelamentoQuery, IPageLoteCancelamento } from 'types/cobrancaPrestador/visaoCancelamento'
import { useAuth } from 'src/hooks/auth'
import { CobrancaServices, IGetCobrancaProps } from 'src/services/faturamentoPrestadorApi/cobrancaServices'
import { PaginationHelper } from 'utils/helpers/paginationHelper'
import { capitalize } from 'utils/stringUtils'
import DropLote from 'components/molecules/DropLote'
import Pagination from 'components/molecules/Pagination'
import NoContent from 'components/molecules/NoContent'
import { IPageLote } from 'types/cobrancaPrestador/loteCobranca'
import { Box, CircularProgress } from '@mui/material'

type LotesCanceladosProps = {
    competenciaSelecionada: IPrestadorAnaliseQuery
    searchLote: string
    forceUpdate: boolean
    refreshList: boolean
    setLotes: React.Dispatch<React.SetStateAction<IPageLoteCancelamento>>
    lotes?: IPageLoteCancelamento
    loadingLotes: boolean
    setLoadingLotes: React.Dispatch<React.SetStateAction<boolean>>
}
const labels: string[] = ['Lote', 'Data de envio', 'Data de cancelamento', 'Usuário', 'Envio']

function createDropLot(item: ILoteCancelamentoQuery) {
    return [
        {
            component: (
                <div>
                    <p>{item.numeroLote}</p>
                </div>
            )
        },
        {
            component: (
                <div>
                    <p>{moment(item.dataEnvio).format('DD/MM/YYYY [-] HH:mm')}</p>
                </div>
            )
        },
        {
            component: (
                <div>
                    <p>{item.dataCancelamento ? moment(item.dataCancelamento).format('DD/MM/YYYY [-] HH:mm') : '---'}</p>
                </div>
            )
        },
        {
            component: (
                <div>
                    <p>{capitalize(item.nomeUsuario)}</p>
                </div>
            )
        },
        {
            component: (
                <div>
                    <p>{item.tipoEnvio === 'ELETRONICO' ? 'Eletrônico' : capitalize(item.tipoEnvio)}</p>
                </div>
            )
        }
    ]
}

const ListaLotesCancelados = ({
    competenciaSelecionada,
    searchLote,
    forceUpdate,
    setLotes,
    lotes,
    refreshList,
    loadingLotes,
    setLoadingLotes
}: LotesCanceladosProps) => {
    const { prestadorVinculado } = useAuth()

    const [pagination, setPagination] = useState<IPagination>()
    const [numberPage, setNumberPage] = useState(0)

    const carregarLotes = useCallback(
        (filter?: string, page?: number) => {
            const getProps: IGetCobrancaProps =
                filter === ''
                    ? {
                          size: 10,
                          page: page || 0
                      }
                    : {
                          size: 10,
                          page: page || 0,
                          filtroNumeroLote: filter
                      }

            if (competenciaSelecionada?.competencia && prestadorVinculado?.uuid) {
                setLoadingLotes(true)
                CobrancaServices.getLotesVisaoCancelamento(competenciaSelecionada.competencia, prestadorVinculado.uuid, getProps)
                    .then(({ data }) => {
                        setLotes(data)

                        const objectPagination = PaginationHelper.parserPagination<ILoteCancelamentoQuery>(data, setNumberPage)
                        setPagination(objectPagination)
                    })
                    .finally(() => setLoadingLotes(false))
            }
        },
        [competenciaSelecionada, prestadorVinculado]
    )

    useEffect(() => {
        carregarLotes(searchLote, numberPage)
    }, [numberPage, competenciaSelecionada, refreshList])

    return (
        <>
            {loadingLotes ? (
                <Box sx={{ display: 'flex', minHeight: 218, alignItems: 'center', justifyContent: 'center' }}>
                    <CircularProgress sx={{ color: '#2B45D4' }} />
                </Box>
            ) : (
                <S.Wrapper>
                    {!lotes || lotes?.content.length === 0 ? (
                        <NoContent title="No momento não existe nenhum lote cancelado" path="/parametros/regras-de-excludencia/nova-regra" />
                    ) : (
                        <>
                            <div className="contentInput">
                                <div className="contentNumber">
                                    <span className="pageNumber">
                                        {pagination?.paginaAtual * pagination?.linhasPorPagina + 1} -{' '}
                                        {lotes?.content?.length + pagination?.paginaAtual * pagination?.linhasPorPagina}
                                        {' de '}
                                        {pagination?.totalRegistros}
                                    </span>
                                </div>
                            </div>
                            <S.HeaderLabel>
                                {labels.map((item, index) => (
                                    <div key={index}>
                                        <p>{item}</p>
                                    </div>
                                ))}
                            </S.HeaderLabel>

                            {lotes?.content?.map((lot: any, index) => {
                                return <DropLote items={createDropLot(lot)} key={index} />
                            })}
                            <div style={{ marginTop: '40px' }}>
                                <Pagination
                                    totalPage={pagination?.totalPaginas}
                                    totalRegister={pagination?.totalRegistros}
                                    actualPage={pagination?.paginaAtual}
                                    setNumberPage={pagination?.setNumberPage}
                                />
                            </div>
                        </>
                    )}
                </S.Wrapper>
            )}
        </>
    )
}

export default ListaLotesCancelados
