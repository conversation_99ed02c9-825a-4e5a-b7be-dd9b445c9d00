import { TextField } from '@mui/material'
import Button from 'components/atoms/Button'
import Modal from 'components/atoms/Modal'
import AttachmentFileCard from 'components/molecules/AttachmentFileCard'
import DragAndDrop from 'components/molecules/DragAndDrop'
import ErrorExpanded from 'components/molecules/ErrorExpanded'
import InputDate from 'components/molecules/InputDate'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import { ReactSVG } from 'react-svg'
import { useToast } from 'src/hooks/toast'
import { CobrancaServices } from 'src/services/faturamentoPrestadorApi/cobrancaServices'
import { LoteManualService } from 'src/services/faturamentoPrestadorApi/lote-manual'
import { ISolicitacaoEnvioEletronicoDTO } from 'types/cobrancaPrestador/loteCobranca'
import { TipoEnvioDoc, TipoEnvioNotaFiscal } from 'types/common/enums'
import { DateUtils } from 'utils/dateUtils'
import { handleFieldsChange } from 'utils/form-utils'
import LocDocCopmpForm from '../organisms/LotDocCompForm'
import LotNfForm, { NfFormType } from '../organisms/LotNfForm'
import * as S from './styles'
import dynamic from 'next/dynamic'

const AnimatedLoadingLottie = dynamic(() => import('components/atoms/AnimatedLoadingLottie'), { ssr: false })

type LotFormType = {
    date: string
    number: string
    competence: string
    xmlFiles: File[]
}

type ElectronicSendingLotTemplateProps = {
    competence: string
}

const ElectronicSendingLotTemplate = ({ competence }: ElectronicSendingLotTemplateProps) => {
    const [lotFormValues, setLotFormValues] = useState<LotFormType>({
        competence,
        date: new Intl.DateTimeFormat('en-US').format(),
        number: '---',
        xmlFiles: []
    })
    const [nfFormValues, setNfFormValues] = useState<NfFormType>({
        number: '',
        services: { label: 'Produção Médico Hospitalar', value: 'PRODUCAO_MEDICO_HOSPITALAR' },
        date: '',
        nfValue: '',
        nfFiles: []
    })
    const [docFormValues, setDocFormValues] = useState<File[]>([])
    const [sendLotInProgress, setSendLotInProgress] = useState(false)
    const [isPreSendNF, setIsPreSendNF] = useState(true)
    const [loadingConfigs, setLoadingConfigs] = useState(true)
    const [showErrorsModal, setShowErrorsModal] = useState(false)
    const [lotWithError, setLotWithError] = useState<ISolicitacaoEnvioEletronicoDTO>()

    const { addToast } = useToast()
    const router = useRouter()

    const handleClickOnSendLotButton = () => {
        setSendLotInProgress(true)

        CobrancaServices.postEnvioEletronicoLoteComNf({
            competencia: lotFormValues?.competence,
            loteXMLs: lotFormValues?.xmlFiles,
            ...(isPreSendNF && {
                notaFiscal: {
                    numeroNota: nfFormValues?.number,
                    servicoPrestado: nfFormValues?.services?.value,
                    dataEmissao: DateUtils.formatDateDataPicker(nfFormValues?.date, 'yyyy-mm-dd'),
                    valor: nfFormValues?.nfValue,
                    nfFiles: nfFormValues?.nfFiles,
                    conciliarNotaFiscal: nfFormValues?.reconcileNf
                }
            }),
            docComp: docFormValues
        })
            .then(({ data }) => {
                const lotWithError = data?.find(({ erros }) => erros?.length > 0)

                if (lotWithError) {
                    setLotWithError(lotWithError)
                    setShowErrorsModal(true)
                    setSendLotInProgress(false)
                    return
                }

                const lotNumbers = data?.map(({ loteEnviado: { numeroLote } }) => numeroLote)

                addToast({
                    title: `Os lotes de número ${lotNumbers?.join(', ')} foi gerado com sucesso e está em processamento.`,
                    type: 'success',
                    duration: 10000
                })

                setDocFormValues([])

                setTimeout(() => {
                    window.location.href = `/faturamento/prestador/medico/cobranca`
                }, 5000)
            })
            .catch((error) => {
                setSendLotInProgress(false)
                if (error?.response?.status === 422) {
                    const lotWithError = error?.response?.data?.find(({ erros }) => erros?.length > 0)
                    if (lotWithError) {
                        setLotWithError(lotWithError)
                        setShowErrorsModal(true)
                        return
                    }
                    return
                }
                addToast({ title: error?.response?.data?.message, type: 'error', duration: 10000 })
            })
    }

    const validateForm = () => {
        const lotIsValid = lotFormValues?.xmlFiles?.length > 0 && docFormValues?.length > 0 && !!lotFormValues?.competence
        return isPreSendNF ? lotIsValid && nfFormValues?.nfIsValid : lotIsValid
    }

    const getFormattedCompetence = (competence: string) => `${DateUtils.getMonthFullName(competence)} de ${competence?.substring(0, 4)}`

    const deleteXmlFile = (fileIndex: number) => {
        handleFieldsChange(
            setLotFormValues,
            'xmlFiles',
            lotFormValues?.xmlFiles?.filter((_, index) => index !== fileIndex)
        )
    }

    const getConfigTipoEnvioNf = (competence: string) => {
        setLoadingConfigs(true)
        CobrancaServices.getTipoEnvioNfPrestador(competence)
            .then(({ data: { tipoEnvioNotaFiscal } }) => {
                setIsPreSendNF(tipoEnvioNotaFiscal === TipoEnvioNotaFiscal.PRE_PROCESSAMENTO)
                setLoadingConfigs(false)
            })
            .catch(() => {
                addToast({ title: 'Ocorreu erro ao buscar as configurações do prestador', type: 'error', duration: 3000 })
            })
    }

    useEffect(() => {
        getConfigTipoEnvioNf(competence)
    }, [])

    return (
        <S.Container>
            <S.Header>
                <S.Title>Novo lote - Envio eletrônico</S.Title>
                <S.Subtitle>Faça o envio de um novo lote de cobrança para a operadora preenchendo os campos abaixo</S.Subtitle>
            </S.Header>
            {loadingConfigs ? (
                <AnimatedLoadingLottie style={{ width: '240px', margin: 'auto' }} />
            ) : (
                <>
                    <S.Box>
                        <S.BoxHeader>
                            <S.BoxTitle>Informações do lote</S.BoxTitle>
                            <S.BoxSubtitle>
                                Estes campos são preenchidos automaticamente quando você inicia o processo de criação do lote
                            </S.BoxSubtitle>
                        </S.BoxHeader>
                        <S.LoteInfoForm>
                            <InputDate
                                value={lotFormValues?.date ? new Date(lotFormValues?.date) : null}
                                label="Data de inclusão do lote"
                                placeholder="00/00/00"
                                onChange={undefined}
                                required
                                disabled
                            />
                            <TextField
                                className="MuiTextField-disabled"
                                label="Número do lote"
                                type="text"
                                value={lotFormValues?.number}
                                disabled
                                required
                            />
                            <TextField
                                className="MuiTextField-disabled"
                                label="Competência de envio"
                                value={getFormattedCompetence(lotFormValues?.competence)}
                                disabled
                                required
                            />
                        </S.LoteInfoForm>
                    </S.Box>
                    <S.Box>
                        <S.BoxHeader>
                            <S.BoxTitle>Upload do arquivo XML</S.BoxTitle>
                            <S.BoxSubtitle>Adicione o arquivo com as cobranças médicas</S.BoxSubtitle>
                        </S.BoxHeader>
                        <DragAndDrop
                            acceptExtensions={'.xml'}
                            uploadFile={(file: File) => handleFieldsChange(setLotFormValues, 'xmlFiles', [...(lotFormValues?.xmlFiles ?? []), file])}
                            file={undefined}
                            setLoadingFile={undefined}
                        />
                        {lotFormValues?.xmlFiles?.length > 0 && (
                            <S.AttachmentGrid>
                                {lotFormValues?.xmlFiles?.map((file, index) => (
                                    <AttachmentFileCard key={index} file={file} onDelete={() => deleteXmlFile(index)} />
                                ))}
                            </S.AttachmentGrid>
                        )}
                    </S.Box>
                    {isPreSendNF && (
                        <S.Box>
                            <S.BoxHeader>
                                <S.BoxTitle>Envio de nota fiscal</S.BoxTitle>
                                <S.BoxSubtitle>Preencha os dados abaixo e adicione os arquivos da nota fiscal nos formatos PDF e XML</S.BoxSubtitle>
                            </S.BoxHeader>
                            <LotNfForm data={nfFormValues} setData={setNfFormValues} />
                        </S.Box>
                    )}
                    <S.Box style={{ marginTop: '2.4rem' }}>
                        <S.BoxHeader>
                            <S.BoxTitle>Documentos complementares</S.BoxTitle>
                            <S.BoxSubtitle>
                                Por favor, anexe abaixo todos os documentos complementares necessários para apoiar a análise da conta pela operadora.{' '}
                            </S.BoxSubtitle>
                        </S.BoxHeader>
                        <LocDocCopmpForm data={docFormValues} setData={setDocFormValues} />
                    </S.Box>

                    <Button
                        typeButton="flat"
                        themeButton={!validateForm() || sendLotInProgress ? 'secondary-gray' : 'secondary'}
                        style={{ width: 'fit-content', marginLeft: 'auto' }}
                        onClick={() => handleClickOnSendLotButton()}
                        disabled={!validateForm() || sendLotInProgress}
                    >
                        {sendLotInProgress ? 'Enviando...' : 'Enviar cobrança'}
                    </Button>
                </>
            )}

            <div style={{ position: 'relative' }}>
                <Modal isOpen={showErrorsModal} onClose={() => setShowErrorsModal(false)} style={{ width: 'min(80rem, 95%)' }}>
                    <ErrorExpanded
                        initialOpen={true}
                        text={
                            lotWithError?.arquivoValido
                                ? lotWithError?.erros?.map(({ messagem }) => messagem)?.join('; ')
                                : 'O arquivo enviado pode está corrompido ou sua estrutura é inválida. Contate o fornecedor do software emissor do arquivo.'
                        }
                    >
                        {!lotWithError?.arquivoValido && (
                            <S.ErrorsWrapper>
                                <S.PresentationErrors>{JSON.stringify(lotWithError?.erros)}</S.PresentationErrors>
                                <div
                                    onClick={() => {
                                        addToast({
                                            title: 'Erros copiados para área de transferência',
                                            type: 'success'
                                        })
                                        navigator.clipboard.writeText(JSON.stringify(lotWithError?.erros))
                                    }}
                                >
                                    <ReactSVG
                                        style={{ cursor: 'pointer' }}
                                        src="/faturamento/assets/icons/copy-clipboard.svg"
                                        wrapper="div"
                                        className="icon"
                                    />
                                </div>
                            </S.ErrorsWrapper>
                        )}
                    </ErrorExpanded>
                </Modal>
            </div>
        </S.Container>
    )
}

export default ElectronicSendingLotTemplate
