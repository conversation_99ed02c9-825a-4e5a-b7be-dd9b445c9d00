import React, { useEffect, useState } from 'react'
import * as S from './styles'
import { ReactSVG } from 'react-svg'
import Input from 'components/atoms/Input'
import AddFileModal from 'components/organisms/AddFileModal'
import Button from 'components/atoms/Button'
import Modal from 'components/atoms/Modal'
import TitleSection from 'components/atoms/TitleSection'
import { FormHelperText, InputAdornment, TextField } from '@mui/material'
import CardToolTipComponent from 'components/molecules/TooltipInfo'
import { RecursoGlosaOperadoraGuia } from 'src/services/recurso-glosa-operadora/guia'
import { IGetDetailsDTO, IGetTableContent } from 'src/services/recurso-glosa-operadora/guia/type'
import { formatMonetary } from 'utils/masks/formatMonetary'
import { currencyMaskBRL } from 'utils/helpers/currencyMaskBRL'
import { useToast } from 'src/hooks/toast'
import ModalGenericComponent from 'components/organisms/ModalGeneric'
import ModalIndeferirItem from '../ModalIndeferirItem'
import { NumberUtils } from 'utils/numberUtils'
import { DateUtils } from 'utils/dateUtils'
import { useRouter } from 'next/router'
import { downloadFileV2, fileNameFromBlob } from 'utils/download'
import { capitalize } from 'utils/stringUtils'

type ListDetailContentProps = {
    item?: IGetTableContent
    itemId?: string
    setRefresh: any
    refresh: any
}

type InputsFieldsProps = {
    valorDeferido?: number
    // justificativaRecurso?: string
}

enum EnumStatus {
    AGUARDANDO_ANALISE = 'AGUARDANDO_ANALISE',
    DEFERIDO = 'DEFERIDO',
    DEFERIDO_PARCIAL = 'DEFERIDO_PARCIAL',
    INDEFERIDO = 'INDEFERIDO'
}

const ListDetailContent = ({ item, itemId, setRefresh, refresh }: ListDetailContentProps) => {
    const [files, setFiles] = useState<any[]>([])
    const [dataFile, setDataFile] = useState()
    const [detailList, setDetailList] = useState<IGetDetailsDTO>()
    const { addToast } = useToast()
    const route = useRouter()
    const [isOpen, setIsOpen] = useState({
        isOpenCancelModal: false,
        isOpenAttachFile: false,
        isOpenShowDetails: false,
        indeferir: false,
        isOpenDropDown: false,
        isOpenDropDownCalculo: false,
        isOpenDropDownMotivoIndeferimento: false,
        isOpenDropDownRecursoGlosa: false,

        // AQUI

        openDesfazerModal: false,
        openDeferidoDesfazer: false
    })
    const [helperTextController, setHelperTextController] = useState(false)

    const [inputFields, setInputFields] = useState<InputsFieldsProps>({
        valorDeferido: 0
    })
    const totalIndeferido = detailList?.valorRecursado - inputFields?.valorDeferido < 0 ? 0 : detailList?.valorRecursado - inputFields?.valorDeferido

    useEffect(() => {
        getItemGuia()
    }, [refresh])

    const getItemGuia = () => {
        RecursoGlosaOperadoraGuia.getItemDetalhes(itemId)
            .then(({ data }) => {
                setDetailList(data)
                // setLoadingItemGuides(false)
            })
            .catch(({ err }) => {
                addToast({
                    title: err?.data?.message,
                    type: 'error',
                    duration: 3000
                })
            })
    }

    useEffect(() => {
        setFiles(detailList?.anexos)
    }, [detailList?.anexos])

    // PATCH INDIVIDUAL DEFERIR

    const handleClickToDeferir = () => {
        RecursoGlosaOperadoraGuia?.patchDeferirItemIndividual(detailList?.itemGuiaRecursoId)
            .then(({ data }) => {
                addToast({
                    title: 'Item deferido com sucesso!',
                    type: 'success',
                    duration: 3000
                })

                setRefresh(!refresh)
            })
            .catch(({ response }) =>
                addToast({
                    title: response?.data?.message,
                    type: 'error',
                    duration: 3000
                })
            )
    }

    // DESFAZER DEFERIDO

    const handleClickToDesfazer = () => {
        RecursoGlosaOperadoraGuia?.patchDesfazer(detailList?.itemGuiaRecursoId)
            .then(({ data }) => {
                addToast({
                    title: 'Ação desfeita com sucesso!',
                    type: 'success',
                    duration: 3000
                })

                setRefresh(!refresh)
            })
            .catch(({ response }) =>
                addToast({
                    title: response?.data?.message,
                    type: 'error',
                    duration: 3000
                })
            )
            .finally(() => {
                setIsOpen({
                    ...isOpen,
                    openDesfazerModal: false
                })

                setIsOpen({
                    ...isOpen,
                    openDeferidoDesfazer: false
                })
            })
    }

    // // DESFAZER PARCIAL E INDEFERIDO

    // const handleClickToDesfazerOutrosStatus = () => {
    //     RecursoGlosaOperadoraGuia?.patchDesfazerOutrosStatus(detailList?.itemGuiaRecursoId)
    //         .then(({ data }) => {
    //             addToast({
    //                 title: 'Ação desfeita com sucesso!',
    //                 type: 'success',
    //                 duration: 3000
    //             })

    //             setRefresh(!refresh)
    //         })
    //         .catch(({ response }) =>
    //             addToast({
    //                 title: response?.data?.message,
    //                 type: 'error',
    //                 duration: 3000
    //             })
    //         )
    // }

    // DOWNLOAD ARCHIVE

    type File = {
        documentoId: string
        nome: string
    }

    const handleClickToDownloadArchive = (doc: File) => {
        RecursoGlosaOperadoraGuia?.getDownloadArchive(doc?.documentoId)
            .then((response) => {
                downloadFileV2(response?.data, doc?.nome)
            })
            .catch(({ response }) => {
                addToast({
                    title: response?.data?.message,
                    type: 'error',
                    duration: 3000
                })
            })
    }

    return (
        <>
            <S.BadgeTop>
                <S.NewBadge>
                    Grau de participação <span>{detailList?.grauParticipacao || '-'}</span>
                </S.NewBadge>
                <S.NewBadge>
                    Via de acesso <span>{detailList?.viaAcesso || '-'}</span>
                </S.NewBadge>
                <S.NewBadge>
                    Horário especial <span>{detailList?.horarioEspecial ? 'Sim' : 'Não'}</span>
                </S.NewBadge>
            </S.BadgeTop>

            <S.Wrapper>
                <S.Top>
                    <div>
                        <p>Quantidade apresentada</p>
                        <span>{detailList?.quantidadeApresentada}</span>
                    </div>
                    <div>
                        <p>Quantidade glosada</p>
                        <span>{detailList?.quantidadeGlosada}</span>
                    </div>
                    <div>
                        <p>Valor unitário apresentado</p>
                        <span>{currencyMaskBRL(detailList?.valorUnitarioApresentado)}</span>
                    </div>
                    <div>
                        <p>Valor unitário apurado</p>
                        <span>{currencyMaskBRL(detailList?.valorUnitarioApurado)}</span>
                    </div>
                </S.Top>
                <S.BottomSpecialGrid>
                    <div>
                        <p>Total apresentado</p>
                        <span>{currencyMaskBRL(detailList?.valorTotalApresentado)}</span>
                    </div>
                    <div>
                        <p>Valor a pagar</p>
                        <span>{currencyMaskBRL(detailList?.valorApurado)}</span>
                    </div>
                    <div>
                        <p>Valor glosado</p>
                        <span>{currencyMaskBRL(detailList?.valorGlosado)}</span>
                    </div>

                    <div>
                        <p>Valor recursado</p>
                        <span>{currencyMaskBRL(detailList?.valorRecursado)}</span>
                    </div>
                </S.BottomSpecialGrid>

                <S.BottomFieldAwaitAnalysis>
                    <S.ExtraFieldAwaitAnalysis>
                        <div>
                            <p>Justificativa do recurso</p>
                            <span>{detailList?.justificativaRecurso}</span>
                        </div>
                    </S.ExtraFieldAwaitAnalysis>

                    <S.Archive>
                        {files?.map((archive, index) => (
                            <S.File key={index} onClick={() => handleClickToDownloadArchive(archive)}>
                                <ReactSVG src="/faturamento/assets/icons/file-doc.svg" wrapper="span" />
                                <p>{archive?.nome}</p>

                                {/* {(item?.status === EnumStatus.GLOSADO || editavel) && (
                                    <ReactSVG
                                        src="/faturamento/assets/icons/close-grey.svg"
                                        wrapper="div"
                                        onClick={() => setFiles((prev: any) => prev?.filter((files: any, index2: number) => index2 !== index))}
                                    />
                                )} */}
                            </S.File>
                        ))}
                    </S.Archive>
                </S.BottomFieldAwaitAnalysis>
            </S.Wrapper>

            <S.Wrapper>
                {item?.statusAnaliseItemRecurso === EnumStatus.AGUARDANDO_ANALISE && (
                    <S.ExtraInputAwaitAnalysis>
                        <TextField
                            onFocus={() => setHelperTextController(true)}
                            onBlur={() => setHelperTextController(false)}
                            required
                            label="Valor deferido"
                            value={NumberUtils?.maskMoney(
                                inputFields?.valorDeferido > detailList?.valorRecursado ? detailList?.valorRecursado : inputFields?.valorDeferido
                            )}
                            onChange={({ target: { value } }) => setInputFields({ ...inputFields, valorDeferido: NumberUtils?.unMaskMoney(value) })}
                            InputProps={{
                                endAdornment: (
                                    <InputAdornment position="end">
                                        <CardToolTipComponent
                                            location="top-start"
                                            clicavel={true}
                                            title="Valor deferido"
                                            description="É o valor recursado acatado pela operadora. Portanto, esse valor não pode ser maior que o valor recursado pelo prestador."
                                        />
                                    </InputAdornment>
                                )
                            }}
                            helperText={
                                inputFields?.valorDeferido > detailList?.valorRecursado &&
                                helperTextController &&
                                'Valor deferido não pode ser maior que o valor recursado pelo prestador'
                            }
                        />

                        <S.BottomAwaitAnalysis>
                            <div>
                                <p>Total indeferido</p>
                                <span>{currencyMaskBRL(totalIndeferido || 0)}</span>
                            </div>
                        </S.BottomAwaitAnalysis>
                    </S.ExtraInputAwaitAnalysis>
                )}

                {(item?.statusAnaliseItemRecurso === EnumStatus.DEFERIDO ||
                    item?.statusAnaliseItemRecurso === EnumStatus.DEFERIDO_PARCIAL ||
                    item?.statusAnaliseItemRecurso === EnumStatus.INDEFERIDO) && (
                    <S.ExtraInputAwaitAnalysis>
                        <S.BottomAwaitAnalysis>
                            <div>
                                <p>Valor deferido</p>
                                <span>{currencyMaskBRL(detailList?.valorDeferido || 0)}</span>
                            </div>
                        </S.BottomAwaitAnalysis>

                        <S.BottomAwaitAnalysis>
                            <div>
                                <p>Total indeferido</p>
                                {/*FIXME:              
                                <span>{currencyMaskBRL(totalIndeferido - detailList?.valorDeferido || 0)}</span> */}
                                <span>{currencyMaskBRL(detailList?.valorIndeferido || 0)}</span>
                            </div>
                        </S.BottomAwaitAnalysis>
                    </S.ExtraInputAwaitAnalysis>
                )}
            </S.Wrapper>

            <S.BtnContainer>
                <div>
                    <Button
                        typeButton="text"
                        onClick={() =>
                            setIsOpen({
                                ...isOpen,
                                isOpenShowDetails: true
                            })
                        }
                    >
                        Ver detalhes
                    </Button>
                </div>
                <S.OneBtn>
                    {/* {item?.status === EnumStatus.GLOSADO || editavel ? (
                        <Button
                            typeButton="text"
                            iconLeft="/faturamento/assets/icons/clip.svg"
                            onClick={() => {
                                setIsOpen({
                                    ...isOpen,
                                    isOpenAttachFile: true
                                })
                            }}
                        >
                            Anexar arquivos
                        </Button>
                    ) : (
                        <Button
                            typeButton="text"
                            iconLeft="/faturamento/assets/icons/icon-close.svg"
                            onClick={() => {
                                setIsOpen({
                                    ...isOpen,
                                    isOpenCancelModal: true
                                })
                            }}
                            themeButton="danger"
                        >
                            Cancelar
                        </Button>
                    )}

                    {item?.status === EnumStatus.RECURSADO ? (
                        <Button
                            typeButton="ghost"
                            iconLeft={editavel ? '/faturamento/assets/icons/success-blue.svg' : '/faturamento/assets/icons/pen.svg'}
                            onClick={() => {
                                editavel ? handleClickToEdit() : setEditavel(true)
                            }}
                        >
                            {editavel ? 'Salvar' : 'Editar'}
                        </Button>
                    ) : (
                        <Button typeButton="ghost" iconLeft="/faturamento/assets/icons/pen.svg" onClick={() => alert('Recursou')}>
                            Recursar
                        </Button>
                    )} */}

                    {item?.statusAnaliseItemRecurso === EnumStatus.AGUARDANDO_ANALISE &&
                        (totalIndeferido > 0 ? (
                            <Button
                                typeButton="ghost"
                                onClick={() => {
                                    setIsOpen({
                                        ...isOpen,
                                        indeferir: true
                                    })
                                }}
                            >
                                Indeferir
                            </Button>
                        ) : (
                            <Button typeButton="ghost" onClick={handleClickToDeferir}>
                                Deferir
                            </Button>
                        ))}

                    {item?.statusAnaliseItemRecurso === EnumStatus.DEFERIDO && (
                        <Button
                            typeButton="ghost"
                            onClick={() => {
                                setIsOpen({
                                    ...isOpen,
                                    openDeferidoDesfazer: true
                                })
                            }}
                        >
                            Desfazer
                        </Button>
                    )}

                    {(item?.statusAnaliseItemRecurso === EnumStatus.DEFERIDO_PARCIAL || item?.statusAnaliseItemRecurso === EnumStatus.INDEFERIDO) && (
                        <Button
                            typeButton="ghost"
                            onClick={() => {
                                setIsOpen({
                                    ...isOpen,
                                    openDesfazerModal: true
                                })
                            }}
                        >
                            Desfazer
                        </Button>
                    )}
                </S.OneBtn>
            </S.BtnContainer>

            {/* MODAL ATTACHFILE */}

            <AddFileModal
                openFileModal={isOpen?.isOpenAttachFile}
                modalTitle="Anexar arquivos"
                files={files}
                setFiles={setFiles}
                setOpenFileModal={() =>
                    setIsOpen({
                        ...isOpen,
                        isOpenAttachFile: false
                    })
                }
            />

            {/* CANCEL MODAL */}

            <Modal
                title="Cancelar o recurso?"
                isOpen={isOpen?.isOpenCancelModal}
                style={{ width: '520px', padding: '20px' }}
                titleStyle={{ fontSize: '2.8rem', marginBottom: '10px' }}
                onClose={() => {
                    setIsOpen({
                        ...isOpen,
                        isOpenCancelModal: false
                    })
                }}
            >
                <S.ModalContent>
                    <p>O Item irá retornar para o estado anterior glosado.</p>
                </S.ModalContent>

                <S.BtnModal>
                    <Button
                        themeButton="gray"
                        typeButton="text"
                        onClick={() =>
                            setIsOpen({
                                ...isOpen,
                                isOpenCancelModal: false
                            })
                        }
                    >
                        Não
                    </Button>
                    <Button className="yesCancel" themeButton="danger" typeButton="flat" onClick={() => alert('Cancelou')}>
                        Sim, Cancelar
                    </Button>
                </S.BtnModal>
            </Modal>

            {/* MODAL  INDEFERIR*/}
            <ModalIndeferirItem
                uuid={itemId}
                valorDeferido={inputFields?.valorDeferido}
                openModal={isOpen?.indeferir}
                setCloseModal={() =>
                    setIsOpen({
                        ...isOpen,
                        indeferir: false
                    })
                }
                handleIndeferir={() => {
                    setRefresh(!refresh)
                    setIsOpen({
                        ...isOpen,
                        indeferir: false
                    })
                }}
                item={item}
            />

            {/* DESFAZER MODAL - DEFERIDO */}

            <ModalGenericComponent
                open={isOpen?.openDeferidoDesfazer}
                setOpenModal={() =>
                    setIsOpen({
                        ...isOpen,
                        openDeferidoDesfazer: !isOpen?.openDeferidoDesfazer
                    })
                }
                widthModal="720px"
                title="Desfazer o status do item?"
                cancelButton="Cancelar"
                actionButton="Desfazer"
                colorButton={'error'}
                // disabledButton={Boolean(!recursoJustificativaRecurso)}
                actionFunction={handleClickToDesfazer}
            >
                <S.Text>O item irá retornar para o estado original apresentada pelo prestador.</S.Text>
            </ModalGenericComponent>

            {/* DESFAZER MODAL - INDEFERIDO e DEFERIDO PARCIAL */}

            <ModalGenericComponent
                open={isOpen?.openDesfazerModal}
                setOpenModal={() =>
                    setIsOpen({
                        ...isOpen,
                        openDesfazerModal: !isOpen?.openDesfazerModal
                    })
                }
                widthModal="720px"
                title="Desfazer o indeferimento do item?"
                cancelButton="Cancelar"
                actionButton="Desfazer"
                colorButton={'error'}
                // disabledButton={Boolean(!recursoJustificativaRecurso)}
                actionFunction={handleClickToDesfazer}
            >
                <S.Text>O item irá retornar para o estado original apresentada pelo prestador.</S.Text>
            </ModalGenericComponent>

            {/* MOSTRAR DETALHES MODAL */}

            <Modal
                title="Detalhes do item"
                isOpen={isOpen?.isOpenShowDetails}
                style={{ width: '720px', padding: '24px' }}
                titleStyle={{ fontSize: '2.8rem', marginBottom: '10px' }}
                onClose={() => {
                    setIsOpen({
                        ...isOpen,
                        isOpenShowDetails: false
                    })
                }}
            >
                <S.ModalContent dropDownClosed={isOpen?.isOpenDropDown || isOpen?.isOpenDropDownCalculo}>
                    <p>
                        {detailList?.codigo} - {detailList?.descricao}
                    </p>

                    <S.Columns columns={3}>
                        <S.ColumnsContent>
                            <span>Código</span>
                            <p>{detailList?.codigo}</p>
                        </S.ColumnsContent>

                        <S.ColumnsContent>
                            <span>Tabela</span>
                            <p>{detailList?.codigoTabela}</p>
                        </S.ColumnsContent>

                        <S.ColumnsContent>
                            <span>Data</span>
                            <p>{DateUtils.formatDateTimePTBR(detailList?.dataInicioExecucao)}</p>
                        </S.ColumnsContent>
                    </S.Columns>

                    <S.Columns columns={3}>
                        <S.ColumnsContent>
                            <span>Via de acesso</span>
                            <p>{detailList?.viaAcesso}</p>
                        </S.ColumnsContent>

                        <S.ColumnsContent>
                            <span>Fator de redução ou acréscimo</span>
                            <p>{detailList?.fatorReducaoAcrescimo}</p>
                        </S.ColumnsContent>
                    </S.Columns>

                    <S.Columns columns={3}>
                        <S.ColumnsContent>
                            <span>Quantidade apresentada</span>
                            <p>{detailList?.quantidadeApresentada}</p>
                        </S.ColumnsContent>

                        <S.ColumnsContent>
                            <span>Valor unitário apresentado</span>
                            <p>{currencyMaskBRL(detailList?.valorUnitarioApresentado || 0)}</p>
                        </S.ColumnsContent>

                        <S.ColumnsContent>
                            <span>Total apresentado</span>
                            <p>{currencyMaskBRL(detailList?.valorTotalApresentado || 0)}</p>
                        </S.ColumnsContent>
                    </S.Columns>

                    {/* MOTIVOS DE GLOSA */}

                    <S.TitleSectionDropdown>
                        <TitleSection>Motivos de glosa</TitleSection>
                        <ReactSVG
                            src={isOpen?.isOpenDropDown ? '/faturamento/assets/icons/chevron.svg' : '/faturamento/assets/icons/arrow-down.svg'}
                            onClick={() =>
                                setIsOpen({
                                    ...isOpen,
                                    isOpenDropDown: !isOpen?.isOpenDropDown
                                })
                            }
                            style={{
                                cursor: 'pointer',
                                width: '24px'
                            }}
                        />
                    </S.TitleSectionDropdown>

                    {isOpen?.isOpenDropDown && (
                        <>
                            {detailList?.motivosGlosa?.map((item, index) => (
                                <S.BoxMotivoGlosa key={index}>
                                    <S.TextContent>
                                        <p>{item?.codigo}</p> - <span>{item?.descricao}</span>
                                    </S.TextContent>

                                    {item?.justificativa && <span className="justificativa">{item?.justificativa}</span>}
                                </S.BoxMotivoGlosa>
                            ))}
                        </>
                    )}

                    {/* RECURSO DE GLOSA */}

                    <S.TitleSectionDropdown>
                        <TitleSection>Recurso de glosa</TitleSection>
                        <ReactSVG
                            src={
                                isOpen?.isOpenDropDownRecursoGlosa
                                    ? '/faturamento/assets/icons/chevron.svg'
                                    : '/faturamento/assets/icons/arrow-down.svg'
                            }
                            onClick={() =>
                                setIsOpen({
                                    ...isOpen,
                                    isOpenDropDownRecursoGlosa: !isOpen?.isOpenDropDownRecursoGlosa
                                })
                            }
                            style={{
                                cursor: 'pointer',
                                width: '24px'
                            }}
                        />
                    </S.TitleSectionDropdown>

                    {isOpen?.isOpenDropDownRecursoGlosa && (
                        <>
                            <S.Divisory>
                                <p>Valor recursado</p>
                                <span>{NumberUtils?.maskMoney(detailList?.valorRecursado) || '-'}</span>
                            </S.Divisory>
                            <S.Divisory>
                                <p>Justificativa do recurso</p>
                                <span>{detailList?.justificativaRecurso}</span>
                            </S.Divisory>

                            <S.Files>
                                <S.ArchiveDetail>
                                    {files?.map((archive, index) => (
                                        <S.File key={index} onClick={() => handleClickToDownloadArchive(archive)}>
                                            <ReactSVG src="/faturamento/assets/icons/file-doc.svg" wrapper="span" />
                                            <p>{capitalize(archive?.nome)}</p>
                                        </S.File>
                                    ))}
                                </S.ArchiveDetail>
                            </S.Files>
                        </>
                    )}

                    {/* MOTIVOS DE INDEFERIMENTO */}

                    <S.TitleSectionDropdown>
                        <TitleSection>Motivos de indeferimento</TitleSection>
                        <ReactSVG
                            src={
                                isOpen?.isOpenDropDownMotivoIndeferimento
                                    ? '/faturamento/assets/icons/chevron.svg'
                                    : '/faturamento/assets/icons/arrow-down.svg'
                            }
                            onClick={() =>
                                setIsOpen({
                                    ...isOpen,
                                    isOpenDropDownMotivoIndeferimento: !isOpen?.isOpenDropDownMotivoIndeferimento
                                })
                            }
                            style={{
                                cursor: 'pointer',
                                width: '24px'
                            }}
                        />
                    </S.TitleSectionDropdown>

                    {isOpen?.isOpenDropDownMotivoIndeferimento && (
                        <>
                            {detailList?.motivosIndeferimento.length > 0 ? (
                                detailList?.motivosIndeferimento?.map((item, index) => (
                                    <S.BoxMotivoIndeferimento key={index}>
                                        <S.TextContent>
                                            <p>{item?.codigo}</p> - <span>{item?.descricao}</span>
                                        </S.TextContent>

                                        {item?.justificativa && <span className="justificativa">{item?.justificativa}</span>}
                                    </S.BoxMotivoIndeferimento>
                                ))
                            ) : (
                                <S.BoxMotivoIndeferimento>
                                    <span>Nenhum motivo registrado.</span>
                                </S.BoxMotivoIndeferimento>
                            )}
                        </>
                    )}

                    {/* CALCULO */}

                    <S.TitleSectionDropdown>
                        <TitleSection>Cálculo</TitleSection>
                        <ReactSVG
                            src={isOpen?.isOpenDropDownCalculo ? '/faturamento/assets/icons/chevron.svg' : '/faturamento/assets/icons/arrow-down.svg'}
                            onClick={() =>
                                setIsOpen({
                                    ...isOpen,
                                    isOpenDropDownCalculo: !isOpen?.isOpenDropDownCalculo
                                })
                            }
                            style={{
                                cursor: 'pointer',
                                width: '24px'
                            }}
                        />
                    </S.TitleSectionDropdown>

                    {isOpen?.isOpenDropDownCalculo && (
                        <S.WrapperCalculo>
                            <S.ContentCalculo>
                                <S.FlexContent bold={true}>
                                    <p>Valor unitário apurado</p>
                                    <span>{currencyMaskBRL(detailList?.valorUnitarioApurado) || '--'}</span>
                                </S.FlexContent>

                                <S.FlexContent>
                                    <p>Valor do item</p>

                                    <span>{currencyMaskBRL(detailList?.valorItem) || '--'}</span>
                                </S.FlexContent>

                                <S.FlexContent>
                                    <p>Grau de participação</p>
                                    <span>{detailList?.grauParticipacao || '--'}</span>
                                </S.FlexContent>

                                <S.FlexContent>
                                    <p>Fator de redução ou acréscimo</p>
                                    <span>{detailList?.fatorReducaoAcrescimo || '--'}</span>
                                </S.FlexContent>
                            </S.ContentCalculo>

                            <S.ContentCalculo>
                                <S.FlexContent bold={true}>
                                    <p>Valor apurado</p>
                                    <span>{currencyMaskBRL(detailList?.valorApurado) || '--'}</span>
                                </S.FlexContent>
                                <S.FlexContent>
                                    <p>Quantidade apresentada</p>
                                    <span>{detailList?.quantidadeApresentada || '--'}</span>
                                </S.FlexContent>
                                <S.FlexContent>
                                    <p>Quantidade glosada</p>
                                    <span>{detailList?.quantidadeGlosada}</span>
                                </S.FlexContent>
                                <S.FlexContent>
                                    <p>Valor unitário apurado</p>
                                    <span>{currencyMaskBRL(detailList?.valorUnitarioApurado) || '--'}</span>
                                </S.FlexContent>
                            </S.ContentCalculo>

                            <S.ContentCalculo>
                                <S.FlexContent bold={true}>
                                    <p>Valor glosado</p>
                                    <span>{currencyMaskBRL(detailList?.valorGlosado) || '--'}</span>
                                </S.FlexContent>
                                <S.FlexContent>
                                    <p>Total apresentado</p>
                                    <span>{currencyMaskBRL(detailList?.valorTotalApresentado) || '--'}</span>
                                </S.FlexContent>
                                <S.FlexContent>
                                    <p>Total a pagar</p>
                                    <span>{currencyMaskBRL(detailList?.valorApurado)}</span>
                                </S.FlexContent>
                            </S.ContentCalculo>

                            <S.ContentCalculo>
                                <S.FlexContent bold={true}>
                                    <p>Valor indeferido</p>
                                    <span>{currencyMaskBRL(detailList?.valorIndeferido) || '--'}</span>
                                </S.FlexContent>
                                <S.FlexContent>
                                    <p>Valor recursado</p>
                                    <span>{currencyMaskBRL(detailList?.valorRecursado) || '--'}</span>
                                </S.FlexContent>
                                <S.FlexContent>
                                    <p>Valor deferido</p>
                                    <span>{currencyMaskBRL(detailList?.valorDeferido) || '--'}</span>
                                </S.FlexContent>
                            </S.ContentCalculo>
                        </S.WrapperCalculo>
                    )}
                </S.ModalContent>

                <S.BtnModal>
                    <Button
                        themeButton="gray"
                        typeButton="ghost"
                        onClick={() =>
                            setIsOpen({
                                ...isOpen,
                                isOpenShowDetails: false
                            })
                        }
                    >
                        Fechar
                    </Button>
                </S.BtnModal>
            </Modal>
        </>
    )
}

export default ListDetailContent
