import { AxiosResponse } from 'axios'

import { ILotNfForm } from 'types/cobrancaPrestador/loteCobranca'
import { TipoEnvioDoc } from 'types/common/enums'
import { ObjectUtils } from 'utils/objectUtils'
import { apiFaturamentoPrestador } from '../../apis/apiFaturamentoPrestador'
import {
    ILoteManualDTO,
    ILoteManualForm,
    ILoteManualGuiaProps,
    ILoteManualProps,
    IPageGuiaLoteManualDTO,
    IPageLoteManualDTO,
    IQuantidadeStatusCobranca
} from './types'

const baseUrl = '/lote-manual'

export class LoteManualService {
    static async get(props?: ILoteManualProps): Promise<AxiosResponse<IPageLoteManualDTO>> {
        const params = ObjectUtils.removeEmptyParams(props)
        const url = props ? `${baseUrl}?${params}` : baseUrl
        return apiFaturamentoPrestador.get(url)
    }

    static async getLotes(uuid?: string): Promise<AxiosResponse<ILoteManualDTO>> {
        // const params = ObjectUtils.removeEmptyParams(props)
        const url = `${baseUrl}/${uuid}`
        return apiFaturamentoPrestador.get(url)
    }

    static async getLoteEmConstrucao(props?: ILoteManualProps): Promise<AxiosResponse<IPageLoteManualDTO>> {
        const params = ObjectUtils.removeEmptyParams(props)
        const url = props ? `${baseUrl}?${params}` : baseUrl
        return apiFaturamentoPrestador.get(url)
    }

    static async getByID(uuid: string): Promise<AxiosResponse<ILoteManualDTO>> {
        return apiFaturamentoPrestador.get(`${baseUrl}/${uuid}`)
    }

    static async getGuias(uuid: string, props: ILoteManualGuiaProps): Promise<AxiosResponse<IPageGuiaLoteManualDTO>> {
        const params = ObjectUtils.propsToParams(props)
        const url = props ? `${baseUrl}/${uuid}/guia?${params}` : `${baseUrl}/${uuid}/guia`
        return apiFaturamentoPrestador.get(url)
    }

    static async getQuantidadeStatusCobranca(uuid: string): Promise<AxiosResponse<IQuantidadeStatusCobranca[]>> {
        return apiFaturamentoPrestador.get(`${baseUrl}/${uuid}/quantidade-guia-por-status-cobranca`)
    }

    static async post(form: ILoteManualForm): Promise<AxiosResponse<ILoteManualDTO>> {
        return apiFaturamentoPrestador.post(baseUrl, form)
    }

    static async patch(uuid: string, form: string[]): Promise<AxiosResponse<ILoteManualDTO>> {
        return apiFaturamentoPrestador.patch(`${baseUrl}/${uuid}/guia`, form)
    }

    static async deleteGuia(uuid: string, uuidGuia: string): Promise<AxiosResponse<ILoteManualDTO>> {
        return apiFaturamentoPrestador.delete(`${baseUrl}/${uuid}/guia/${uuidGuia}`)
    }

    static async enviarCobranca({
        uuid,
        notaFiscal,
        docComp
    }: {
        uuid: string
        notaFiscal?: ILotNfForm
        docComp: File[]
    }): Promise<AxiosResponse<ILoteManualDTO>> {
        const formData = new FormData()
        notaFiscal?.nfFiles?.forEach((file) => formData.append('arquivosNotaFiscal', file))
        docComp?.forEach((file) => formData.append('arqsDocsComplementares', file))

        return apiFaturamentoPrestador.post(`${baseUrl}/${uuid}/enviar-cobranca`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            },
            params: {
                ...(notaFiscal?.conciliarNotaFiscal
                    ? { conciliarNotaFiscal: true, numeroNotaFiscalConciliacao: notaFiscal?.numeroNota }
                    : {
                          numeroNota: notaFiscal?.numeroNota,
                          valor: notaFiscal?.valor,
                          servicoPrestado: notaFiscal?.servicoPrestado,
                          dataEmissao: notaFiscal?.dataEmissao
                      })
            }
        })
    }

    static async enviarDocComplementar({
        uuid,
        docs,
        tipoEnvioDoc
    }: {
        uuid: string | Array<string>
        docs: File[]
        tipoEnvioDoc: TipoEnvioDoc
    }): Promise<AxiosResponse<ILoteManualDTO>> {
        const formData = new FormData()
        docs?.forEach((file) => formData.append('arquivos', file))

        Array.isArray(uuid) ? uuid?.forEach((id) => formData.append('lote', id)) : formData.append('lote', uuid)

        formData.append('tipoEnvio', tipoEnvioDoc)

        return apiFaturamentoPrestador.post(`/cobranca/lote/documento-diversos`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        })
    }
}
