import { InfoOutlined } from '@mui/icons-material'
import { Box, ClickAwayListener, Tooltip, TooltipProps, Typography, tooltipClasses } from '@mui/material'
import { Theme, styled } from '@mui/material/styles'
import zIndex from '@mui/material/styles/zIndex'
import React from 'react'

export type CardToolTipComponentProps = {
    title?: string
    description?: string
    clicavel?: boolean
    // onClclick?: boolean
    // setOnClick?: any
    location?:
        | 'bottom'
        | 'left'
        | 'right'
        | 'top'
        | 'bottom-end'
        | 'bottom-start'
        | 'left-end'
        | 'left-start'
        | 'right-end'
        | 'right-start'
        | 'top-end'
        | 'top-start'
}

const CardToolTipComponent = ({ title, description, location = 'top-end', clicavel = false }: CardToolTipComponentProps) => {
    const [open, setOpen] = React.useState(false)
    const CardTooltip = styled(({ className, ...props }: TooltipProps) => <Tooltip {...props} classes={{ popper: className }} />)(({ theme }) => ({
        [`& .${tooltipClasses.tooltip}`]: {
            backgroundColor: theme.palette.common.white,
            boxShadow: theme.shadows[1],
            padding: '16px',
            borderRadius: '8px'
        }
    }))

    const handleTooltipClose = () => {
        setOpen(false)
    }

    const handleTooltipOpen = () => {
        setOpen(true)
    }

    return (
        <>
            {clicavel ? (
                <ClickAwayListener onClickAway={handleTooltipClose}>
                    <div>
                        <CardTooltip
                            onClose={handleTooltipClose}
                            open={open}
                            disableFocusListener
                            disableHoverListener
                            disableTouchListener
                            placement={location}
                            title={
                                open && (
                                    <React.Fragment>
                                        <Typography color="rgba(0, 0, 0, 0.88)" fontSize="12px" fontWeight="600">
                                            {title}
                                        </Typography>

                                        <Typography
                                            color="rgba(0, 0, 0, 0.56)"
                                            fontSize="12px"
                                            fontWeight="400"
                                            marginTop="8px"
                                            style={{ whiteSpace: 'break-spaces' }}
                                        >
                                            {description}
                                        </Typography>
                                    </React.Fragment>
                                )
                            }
                        >
                            <Box onClick={handleTooltipOpen} style={{ cursor: 'pointer', marginLeft: '6px' }} display="flex" alignItems="center">
                                <InfoOutlined style={{ color: '#2B45D4' }} />
                            </Box>
                        </CardTooltip>
                    </div>
                </ClickAwayListener>
            ) : (
                <div>
                    <CardTooltip
                        placement={location}
                        title={
                            <React.Fragment>
                                <Typography color="rgba(0, 0, 0, 0.88)" fontSize="12px" fontWeight="600">
                                    {title}
                                </Typography>

                                <Typography
                                    color="rgba(0, 0, 0, 0.56)"
                                    fontSize="12px"
                                    fontWeight="400"
                                    marginTop="8px"
                                    style={{ whiteSpace: 'break-spaces' }}
                                >
                                    {description}
                                </Typography>
                            </React.Fragment>
                        }
                    >
                        <Box style={{ marginLeft: '6px' }} display="flex" alignItems="center">
                            <InfoOutlined style={{ color: '#2B45D4' }} />
                        </Box>
                    </CardTooltip>
                </div>
            )}
        </>
    )
}
export default CardToolTipComponent
