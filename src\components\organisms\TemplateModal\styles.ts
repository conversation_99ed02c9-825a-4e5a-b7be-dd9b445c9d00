/* eslint-disable prettier/prettier */
import styled, {css} from 'styled-components';

export const TitleModal = styled.h1`
	font-weight: 400;
	font-size: 24px;
	line-height: 32px;
	color: rgba(0, 0, 0, 0.88);
`;
export const ModalContent = styled.div`
	${({theme}) => css`
		display: flex;
		max-height: 85vh;
		height: 100%;
		flex-direction: column;

		small {
			margin: 0.5rem 0;
			display: block;
			font-size: 1.2rem;
			font-weight: 400;
			color: ${theme.colors.black['56']};
		}
	`}
`;
export const ContainerTop = styled.div`
	${({theme}) => css`
		display: flex;
		align-items: center;
		justify-content: space-between;

		border-bottom: 1px solid #ccc;
		padding-bottom: 20px;

		img {
			cursor: pointer;
		}

		h1 {
			font-size: 2.4rem;
			line-height: 3.2rem;
			font-weight: 600;
			color: ${theme.colors.black['88']};
		}
	`}
`;
