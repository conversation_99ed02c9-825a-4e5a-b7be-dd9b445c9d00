import styled, { css } from 'styled-components'

type ContentActiveItemsProps = {
    disabled: boolean
}

export const ContentActiveItems = styled.div<ContentActiveItemsProps>`
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 4px;
    background: rgba(43, 69, 212, 0.04);

    display: grid;
    align-items: center;
    grid-template-columns: 5fr 1fr;

    span {
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        color: rgba(0, 0, 0, 0.56);
    }

    ${({ disabled }) =>
        disabled &&
        css`
            opacity: 0.6;
        `}
`

export const SwitchContent = styled.div`
    display: flex;
    justify-content: end;
`
