import axios from 'axios'
import { jwtAuthorizationHeaderInjector } from './jwtAuthorizationHeaderInjector'

export const apiCredenciamento = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_CREDENCIAMENTO,
    headers: {
        'Content-Type': 'application/json'
    }
})

apiCredenciamento.interceptors.request.use(
    (config) => {
        return jwtAuthorizationHeaderInjector(config)
    },
    (error) => Promise.reject(error)
)
