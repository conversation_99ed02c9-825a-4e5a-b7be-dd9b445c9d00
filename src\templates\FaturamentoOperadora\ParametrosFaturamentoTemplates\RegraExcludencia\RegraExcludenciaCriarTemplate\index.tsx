import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import { SingleValue } from 'react-select'
import Button from 'components/atoms/Button'
import Layout from 'components/molecules/Layout'
import Input from 'components/atoms/Input'
import InputDate from 'components/molecules/InputDate'
import Select, { Options } from 'components/molecules/Select'
import { TitleSection } from 'components/atoms/TitleSection/styles'
import AttachFileModal from 'components/organisms/AttachFileModal'
import { useNewProcedureContext } from 'src/context/ParametersContext/useNewProcedure'
import { Parameter, Procedures, useParameterContext } from 'src/context/ParametersContext/useParameter'
import * as S from './styles'
import FormAdicionarProcedimento from 'components/organisms/FormAdicionarProcedimento'
import TableProcedimentoAdicionado from 'components/molecules/TableProcedimentoAdicionado'
import { handleFieldsChange } from 'utils/form-utils'

interface props {
    data: Parameter
    setData: (data: Parameter) => void
}

const RegraExcludenciaCriarTemplate = ({ data, setData }: props) => {
    const router = useRouter()
    const [fieldsNewProcedure] = useNewProcedureContext()
    const [openFileModal, setOpenFileModal] = useState(false)
    const [openProcedure, setOpenProcedure] = useState(false)
    const [disabledButton, setDisabledButton] = useState(true)
    const [parameterForm, setParameterForm] = useParameterContext()

    useEffect(() => {
        setDisabledButton(!(fieldsNewProcedure?.secondaryProcedure && !undefined && !null))
    }, [fieldsNewProcedure])

    const removeProcedure = (index: number) => {
        let newProcedure = data.proceduresList
        console.log('newProcedure: ', newProcedure)
        if (newProcedure.length === 1) {
            newProcedure = [] as Procedures[]
        } else {
            newProcedure = newProcedure.splice(index, 1)
        }
        console.log('procedure remove', newProcedure)
        setData({
            ...data,
            ['proceduresList']: newProcedure
        })
    }

    const addProcedure = (newProcedure: Procedures[]) => {
        setData({
            ...data,
            ['proceduresList']: newProcedure
        })
    }

    return (
        <Layout isLoggedIn={true} title={'Nova regra'}>
            <S.Container>
                <TitleSection>Parâmetros</TitleSection>
                <S.FormInput
                    onSubmit={(event) => {
                        event.preventDefault()
                    }}
                >
                    <S.ContainerInputs>
                        <div className="row">
                            <Input
                                className="rule-name"
                                isDefault="default"
                                label="Nome da regra"
                                placeholder="----"
                                type="text"
                                initialValue={parameterForm?.ruleName}
                                handleOnChange={(value: string) => handleFieldsChange(setParameterForm, 'ruleName', value)}
                            />
                            <InputDate
                                value={
                                    parameterForm?.effectiveDate !== undefined && parameterForm?.effectiveDate !== null
                                        ? new Date(parameterForm?.effectiveDate)
                                        : null
                                }
                                label="Data de vigência"
                                placeholder="00/00/00"
                                onChange={(date: Date) => {
                                    handleFieldsChange(
                                        setParameterForm,
                                        'effectiveDate',
                                        date !== null && date !== undefined ? new Intl.DateTimeFormat('en-US').format(date) : null
                                    )
                                }}
                                key={'input-date'}
                            />
                            <Select
                                className="medical-bills"
                                label="Local de aplicação"
                                value={parameterForm?.localApplication}
                                defaultValue={
                                    [
                                        {
                                            value: 'Selecione',
                                            label: 'Selecione'
                                        },
                                        {
                                            value: 'Todos',
                                            label: 'Todos'
                                        },
                                        {
                                            value: 'Regulacao',
                                            label: 'Regulação'
                                        },
                                        {
                                            value: 'Contas Medicas',
                                            label: 'Contas Médicas'
                                        }
                                    ][0]
                                }
                                options={[
                                    {
                                        value: 'Selecione',
                                        label: 'Selecione'
                                    },
                                    {
                                        value: 'Todos',
                                        label: 'Todos'
                                    },
                                    {
                                        value: 'Regulacao',
                                        label: 'Regulação'
                                    },
                                    {
                                        value: 'Contas Medicas',
                                        label: 'Contas Médicas'
                                    }
                                ]}
                                onChange={(option: SingleValue<Options>) => {
                                    handleFieldsChange(setParameterForm, 'applicationLocal', option.value)
                                }}
                            />
                        </div>
                        <div className="row">
                            <Select
                                className="first-verification"
                                label="Primeira verificação"
                                value={parameterForm?.firstVerification}
                                defaultValue={
                                    [
                                        {
                                            value: 'Selecione',
                                            label: 'Selecione'
                                        },
                                        {
                                            value: 'No mesmo atendimento',
                                            label: 'No mesmo atendimento'
                                        },
                                        {
                                            value: 'Em atendimento do mesmo dia independente da guia ou conta médica',
                                            label: 'Em atendimento do mesmo dia independente da guia ou conta médica'
                                        },
                                        {
                                            value: 'Em atendimentos no prazo de X dias independente da guia ou conta médica, do mesmo prestador e local de atendimento',
                                            label: 'Em atendimentos no prazo de X dias independente da guia ou conta médica, do mesmo prestador e local de atendimento'
                                        }
                                    ][0]
                                }
                                options={[
                                    {
                                        value: 'Selecione',
                                        label: 'Selecione'
                                    },
                                    {
                                        value: 'No mesmo atendimento',
                                        label: 'No mesmo atendimento'
                                    },
                                    {
                                        value: 'Em atendimento do mesmo dia independente da guia ou conta médica',
                                        label: 'Em atendimento do mesmo dia independente da guia ou conta médica'
                                    },
                                    {
                                        value: 'Em atendimentos no prazo de X dias independente da guia ou conta médica, do mesmo prestador e local de atendimento',
                                        label: 'Em atendimentos no prazo de X dias independente da guia ou conta médica, do mesmo prestador e local de atendimento'
                                    }
                                ]}
                                onChange={(option: SingleValue<Options>) => {
                                    handleFieldsChange(setParameterForm, 'verificationFirst', option.value)
                                }}
                            />
                        </div>
                        <div className="row">
                            <Select
                                className="second-verification"
                                label="Segunda verificação"
                                value={parameterForm?.secondVerification}
                                defaultValue={
                                    [
                                        {
                                            value: 'Selecione',
                                            label: 'Selecione'
                                        },
                                        {
                                            value: 'É preciso que TODOS os procedimentos informados como cobrados estejam na guia ou conta',
                                            label: 'É preciso que TODOS os procedimentos informados como cobrados estejam na guia ou conta'
                                        },
                                        {
                                            value: 'Basta que um dos procedimentos informados como cobrados esteja na guia ou conta',
                                            label: 'Basta que um dos procedimentos informados como cobrados esteja na guia ou conta'
                                        }
                                    ][0]
                                }
                                options={[
                                    {
                                        value: 'Selecione',
                                        label: 'Selecione'
                                    },
                                    {
                                        value: 'É preciso que TODOS os procedimentos informados como cobrados estejam na guia ou conta',
                                        label: 'É preciso que TODOS os procedimentos informados como cobrados estejam na guia ou conta'
                                    },
                                    {
                                        value: 'Basta que um dos procedimentos informados como cobrados esteja na guia ou conta',
                                        label: 'Basta que um dos procedimentos informados como cobrados esteja na guia ou conta'
                                    }
                                ]}
                                onChange={(option: SingleValue<Options>) => {
                                    handleFieldsChange(setParameterForm, 'verificationSecond', option.value)
                                }}
                            />
                        </div>
                        <div className="row">
                            <Select
                                className="provider"
                                label="Prestador"
                                value={parameterForm?.providerType}
                                defaultValue={
                                    [
                                        {
                                            value: 'Selecione',
                                            label: 'Selecione'
                                        },
                                        {
                                            value: 'Todos',
                                            label: 'Todos'
                                        },
                                        {
                                            value: 'Específico',
                                            label: 'Específico'
                                        }
                                    ][0]
                                }
                                options={[
                                    {
                                        value: 'Selecione',
                                        label: 'Selecione'
                                    },
                                    {
                                        value: 'Todos',
                                        label: 'Todos'
                                    },
                                    {
                                        value: 'Específico',
                                        label: 'Específico'
                                    }
                                ]}
                                onChange={(option: SingleValue<Options>) => {
                                    handleFieldsChange(setParameterForm, 'provider', option.value)
                                }}
                            />
                            {parameterForm?.provider === 'Específico' ? (
                                <Input
                                    className="contract-type"
                                    isDefault="default"
                                    label="Contrato, CNPJ ou Razão Social"
                                    placeholder="----"
                                    type="text"
                                    maxLength={15}
                                    initialValue={parameterForm?.providerIdentity}
                                    handleOnChange={(value: number) => handleFieldsChange(setParameterForm, 'providerIdentity', value)}
                                />
                            ) : null}
                        </div>
                    </S.ContainerInputs>

                    <TitleSection>Procedimentos</TitleSection>

                    {data !== undefined && data?.proceduresList?.length > 0 && (
                        <TableProcedimentoAdicionado removeItem={removeProcedure} parameter={data?.proceduresList} />
                    )}
                    <S.ProceduresButtons>
                        <Button
                            typeButton={'ghost'}
                            themeButton={'primary'}
                            style={{ width: '31%' }}
                            iconLeft={'/faturamento/assets/icons/upload.svg'}
                            onClick={() => setOpenFileModal(true)}
                        >
                            Importar tabela de excludentes
                        </Button>
                        <Button
                            themeButton={'primary'}
                            style={{ width: '25%' }}
                            iconLeft={'/faturamento/assets/icons/plus-white.svg'}
                            onClick={() => setOpenProcedure(true)}
                        >
                            Inserir manualmente
                        </Button>
                    </S.ProceduresButtons>
                </S.FormInput>
                <FormAdicionarProcedimento
                    newProcedure={data !== undefined ? data?.proceduresList : ([] as Procedures[])}
                    setNewProcedure={addProcedure}
                    openProcedure={openProcedure}
                    setOpenProcedure={setOpenProcedure}
                />
            </S.Container>
            <S.ButtonContainer>
                <Button
                    themeButton={'warning'}
                    style={{ width: '20%', marginTop: '40px' }}
                    iconLeft={'/faturamento/assets/icons/plus.svg'}
                    onClick={() => router.push('/parametros/regras-de-excludencia')}
                >
                    Criar regra
                </Button>
            </S.ButtonContainer>
            <AttachFileModal openFileModal={openFileModal} setOpenFileModal={setOpenFileModal} />
        </Layout>
    )
}

export default RegraExcludenciaCriarTemplate
