/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState } from 'react'
import theme from 'styles/theme'
import Select, { components } from 'react-select'
import * as S from './styles'

type SimpleSelectProps = {
    defaultValue: any
    onChange: any
    options: any
    value?: any
    label?: string
    required?: boolean
    isClearable?: boolean
    disabled?: boolean
}

const SimpleSelect = ({ defaultValue, value, onChange, options, label, isClearable, required, disabled }: SimpleSelectProps) => {
    const [isFocused, setIsFocused] = useState(false)

    // const [isFill, setIsFill] = useState(false)

    const NoOptionsMessage = (props: any) => {
        return (
            <components.NoOptionsMessage {...props}>
                <span className="custom-css-class">Sem opções</span>
            </components.NoOptionsMessage>
        )
    }

    // useEffect(() => {
    //     console.log('passou,', value)
    //     console.log('passou,', !!value)
    // }, [])

    return (
        <S.Wrapper
            isFocused={isFocused || (defaultValue !== '' && defaultValue !== undefined && defaultValue !== null)}
            focus={isFocused}
            isFill={true}
        >
            <label>
                {label}
                {required && <span> *</span>}
            </label>
            <Select
                styles={{
                    control: (provider, state) => ({
                        display: 'flex',
                        flexDirection: 'row',
                        borderRadius: '0.4rem',
                        fontSize: '16px',
                        height: '5.6rem',
                        padding: '0 .6rem',
                        border: state.isFocused ? '2px solid' : '1px solid',
                        borderColor: state.isFocused ? `${theme.colors.primary['500']}` : `${theme.colors.black[16]}`
                    }),
                    menu: (provider: any, state) => ({
                        ...provider,
                        zIndex: 9999
                    })
                }}
                instanceId="postType"
                components={{ NoOptionsMessage }}
                placeholder={''}
                defaultValue={defaultValue}
                value={value}
                onChange={onChange}
                options={options}
                isDisabled={disabled}
                isClearable={isClearable}
                onFocus={() => {
                    setIsFocused(true)
                }}
                onBlur={() => {
                    setIsFocused(false)
                }}
            />
        </S.Wrapper>
    )
}

export default SimpleSelect
