import React, { ReactNode, useEffect, useState } from 'react'

import { useToast } from 'src/hooks/toast'
import { useRouter } from 'next/router'
import { ReactSVG } from 'react-svg'

import DividerSectionCard from 'components/atoms/DividerSectionCard'
import NoContent from 'components/molecules/NoContent'
import Button from 'components/atoms/Button'
import Badge from 'components/atoms/Badge'
import Modal from 'components/atoms/Modal'
import Input from 'components/atoms/Input'

import ButtonFilter from 'components/molecules/ButtonFilter'
import SearchBar from 'components/molecules/SearchBar'

import { IGuiaRecursoDTO, IQuantitativoGuiasPorEtapaAnaliseDto, IResumoLoteRecursoDTO } from 'types/analiseContas/recursoGlosa'
import { IPagination } from 'types/common/pagination'

import { PaginationHelper } from 'utils/helpers/paginationHelper'
import { EtapaAnaliseEnum, SituacaoLoteEnum } from 'utils/enum/lote-recurso.enum'
import { NumberUtils } from 'utils/numberUtils'
import { useDebounce } from 'utils/useDebounce'
import { capitalize } from 'utils/stringUtils'

import { RecursoGlosaService } from 'src/services/analiseContasApi/recursoGlosaServices'
import { TipoLoteEnum } from 'src/services/composicaoPagamentoApi/enum'

import TablePaginationRecursoOperadora from '../Components/TablePaginationRecurso'

import { statusBackgroundEnum, statusColorEnum, statusTextEnum } from './enuns'

import ModalIndeferirLote from '../Components/ModalIndeferirLote'

import * as S from './styles'
import InfoCard from 'components/molecules/InfoCard'
import { DateUtils } from 'utils/dateUtils'
interface GuiasLoteRecurso {
    acao: ReactNode
    numeroGuia: string
    tipoGuia: string
    beneficiario: string
    valorRecursado: string
    valorDeferido: string
    valorIndeferido: string
}

interface Titles {
    label: string
    value: keyof GuiasLoteRecurso
}

const ResumoLotesTemplate = ({ uuid }) => {
    const { addToast } = useToast()
    const router = useRouter()
    const loteRecursoId = router.query.id as string
    const [loadingCancelar, setLoadingCancelar] = useState<boolean>()
    const [loadingFecharAbrir, setLoadingFecharAbrir] = useState<boolean>()
    const [status, setStatus] = useState<EtapaAnaliseEnum>(EtapaAnaliseEnum.ANALISE_TECNICA)
    const [valuesGuias, setValuesGuias] = useState<GuiasLoteRecurso[]>([])
    const [resumoLoteGuia, setResumoLoteGuia] = useState<IResumoLoteRecursoDTO>()
    const [quantitativos, setQuantitativos] = useState<IQuantitativoGuiasPorEtapaAnaliseDto[]>([])
    const [numberPage, setNumberPage] = useState<number>(0)
    const [pagination, setPagination] = useState<IPagination>()
    const [openModalFecharLote, setOpenModalFecharLote] = useState<boolean>(false)
    const [openModalCancelarLote, setOpenModalCancelarLote] = useState<boolean>(false)
    const [openModalReabrirLote, setOpenModalReabrirLote] = useState<boolean>(false)
    const [openModalIndeferirLote, setOpenModalIndeferirLote] = useState<boolean>(false)
    const [refresh, setRefresh] = useState<boolean>(false)
    const [justificativaCancelamento, setJustificativaCancelamento] = useState<string>()
    const [filter, setFilter] = useState<string>()
    const [searchBarValue, setSarchBarValue] = useState<string>('')
    // const debouncedValue = useDebounce<string>(filter, 500)

    const titles: Titles[] = [
        { label: 'Nº Guia', value: 'numeroGuia' },
        { label: 'Tipo de guia', value: 'tipoGuia' },
        { label: 'Beneficiário', value: 'beneficiario' },
        { label: 'Recursado.', value: 'valorRecursado' },
        { label: 'Indeferido', value: 'valorIndeferido' },
        { label: 'Deferido', value: 'valorDeferido' },
        { label: '', value: 'acao' }
    ]

    useEffect(() => {
        if (loteRecursoId) {
            getResumoGuias()
            getQuantitativosPrestador()
        }
    }, [router.isReady, refresh])

    useEffect(() => {
        if (loteRecursoId) {
            getGuiasByLoteRecursoId()
        }
    }, [status, numberPage, refresh, searchBarValue])

    const parseValues = (guias: IGuiaRecursoDTO[]): GuiasLoteRecurso[] => {
        return guias?.map((item) => ({
            numeroGuia: item?.numeroGuia,
            tipoGuia: item?.tipoGuia,
            beneficiario: capitalize(item?.beneficiario),
            valorRecursado: NumberUtils.maskMoney(item?.valorRecursado),
            valorDeferido: NumberUtils.maskMoney(item?.valorDeferido),
            valorIndeferido: NumberUtils.maskMoney(item?.valorIndeferido),
            acao: (
                <S.WrapperAction
                    ativo={true}
                    style={item?.etapaAnalise ? { cursor: 'pointer' } : { cursor: 'not-allowed' }}
                    onClick={() => {
                        if (item?.etapaAnalise) {
                            router.push(`/processamento-contas/recurso-glosa/prestador/lotes/detalhes-guia/${item?.guiaRecursoId}`)
                        }
                    }}
                >
                    <ReactSVG wrapper="div" style={{ fill: 'red' }} src="/faturamento/assets/icons/ic-eye.svg" />
                </S.WrapperAction>
            )
        }))
    }

    const getResumoGuias = () => {
        RecursoGlosaService.getResumoLoteRecurso(loteRecursoId)
            .then(({ data }) => {
                setResumoLoteGuia(data)
            })
            .catch(({ response }) => {
                addToast({
                    title: response?.data?.message,
                    type: 'error',
                    duration: 3000
                })
            })
    }

    const getGuiasByLoteRecursoId = () => {
        RecursoGlosaService.listarGuiasLoteRecurso({
            loteRecursoId,
            etapaAnalise: status,
            page: numberPage,
            size: 5,
            numeroGuia: searchBarValue ? searchBarValue : null
        })
            .then(({ data }) => {
                setValuesGuias(parseValues(data?.content))
                setPagination(PaginationHelper.parserPagination(data, setNumberPage))
            })
            .catch(({ response }) => {
                addToast({
                    title: response?.data?.message,
                    type: 'error',
                    duration: 3000
                })
            })
    }

    const getQuantitativosPrestador = () => {
        RecursoGlosaService.getQuantificacoesGuias(loteRecursoId)
            .then(({ data }) => {
                setQuantitativos(data)
            })
            .catch(({ response }) => {
                addToast({
                    title: response?.data?.message,
                    type: 'error',
                    duration: 3000
                })
            })
    }

    const handleCancelarLote = () => {
        setLoadingCancelar(true)
        RecursoGlosaService.putCancelarLoteRecurso(loteRecursoId, { justificativa: justificativaCancelamento })
            .then(() => {
                addToast({ title: 'Lote cancelado com sucesso', type: 'success', duration: 3000 })
                setJustificativaCancelamento('')
                setOpenModalCancelarLote(false)
                getResumoGuias()
                getQuantitativosPrestador()
                setRefresh(!refresh)
            })
            .catch(({ response }) => {
                addToast({
                    title: response?.data?.message,
                    type: 'error',
                    duration: 3000
                })
            })
            .finally(() => setLoadingCancelar(false))
    }

    const handleFecharLote = () => {
        setLoadingFecharAbrir(true)
        RecursoGlosaService.putFecharLoteRecurso(loteRecursoId)
            .then(() => {
                addToast({ title: 'Lote fechado com sucesso', type: 'success', duration: 3000 })
                setOpenModalFecharLote(false)
                getResumoGuias()
                getQuantitativosPrestador()
                setRefresh(!refresh)
            })
            .catch(({ response }) => {
                addToast({
                    title: response?.data?.message,
                    type: 'error',
                    duration: 3000
                })
            })
            .finally(() => setLoadingFecharAbrir(false))
    }

    const handleReabrirLote = () => {
        setLoadingFecharAbrir(true)
        RecursoGlosaService.putReabrirLoteRecurso(loteRecursoId)
            .then(() => {
                addToast({ title: 'Lote reaberto com sucesso', type: 'success', duration: 3000 })
                setOpenModalReabrirLote(false)
                getResumoGuias()
                getQuantitativosPrestador()
                setRefresh(!refresh)
            })
            .catch(({ response }) => {
                addToast({
                    title: response?.data?.message,
                    type: 'error',
                    duration: 3000
                })
            })
            .finally(() => {
                setLoadingFecharAbrir(false), setOpenModalReabrirLote(false)
            })
    }

    const handlePesquisar = () => {
        setSarchBarValue(filter)
        setNumberPage(0)
    }

    return (
        <>
            <S.Header style={{ marginBottom: '24px' }}>
                <S.PrestadorInfoWrapper>
                    <S.TitleWrapper>
                        <h3>Lote {resumoLoteGuia?.identificadorLoteRecurso}</h3>
                        <Badge
                            background={statusBackgroundEnum[resumoLoteGuia?.situacaoLoteRecurso]}
                            color={statusColorEnum[resumoLoteGuia?.situacaoLoteRecurso]}
                            text={statusTextEnum[resumoLoteGuia?.situacaoLoteRecurso]}
                            style={{ padding: '0px 8px' }}
                        />
                    </S.TitleWrapper>

                    {resumoLoteGuia?.dataEnvioRecurso ? (
                        <p>Enviado em {new Date(resumoLoteGuia?.dataEnvioRecurso).toLocaleDateString('pt-BR')}</p>
                    ) : null}
                </S.PrestadorInfoWrapper>

                {resumoLoteGuia?.situacaoLoteRecurso === SituacaoLoteEnum.EM_ANALISE && (
                    <S.BtnHeaderWrapper>
                        <Button
                            iconLeft="/faturamento/assets/icons/ic-block.svg"
                            style={{ width: 'fit-content', backgroundColor: 'rgba(244, 67, 54, 0.04)', color: '#F44336' }}
                            onClick={() => setOpenModalCancelarLote(true)}
                        >
                            Cancelar
                        </Button>
                        <Button
                            iconLeft="/faturamento/assets/icons/ic-dislike.svg"
                            style={{ width: 'fit-content', backgroundColor: 'rgba(244, 67, 54, 0.04)', color: '#F44336' }}
                            onClick={() => setOpenModalIndeferirLote(true)}
                        >
                            Indeferir
                        </Button>
                        <Button
                            iconLeft="/faturamento/assets/icons/ic-cadeado.svg"
                            disabled={false}
                            themeButton="warning"
                            style={{ width: 'fit-content' }}
                            onClick={() => setOpenModalFecharLote(true)}
                        >
                            Fechar
                        </Button>
                    </S.BtnHeaderWrapper>
                )}

                {resumoLoteGuia?.situacaoLoteRecurso === SituacaoLoteEnum?.FECHADO && (
                    <S.BtnHeaderWrapper>
                        <Button
                            iconLeft="/faturamento/assets/icons/ic-open-cadeado.svg"
                            disabled={false}
                            style={{ width: 'fit-content', backgroundColor: 'rgba(43, 69, 212, 0.04)', color: '#2B45D4' }}
                            onClick={() => setOpenModalReabrirLote(true)}
                        >
                            Reabrir
                        </Button>
                    </S.BtnHeaderWrapper>
                )}
            </S.Header>

            <S.InfoContainer>
                {resumoLoteGuia?.dadosCancelamentoLoteRecurso && (
                    <InfoCard
                        type="danger"
                        subtitle={resumoLoteGuia?.dadosCancelamentoLoteRecurso?.justificativa}
                        title={`Cancelado por ${resumoLoteGuia?.dadosCancelamentoLoteRecurso?.usuario} em ${DateUtils?.parseDateDDMMYYYY(
                            resumoLoteGuia?.dadosCancelamentoLoteRecurso?.data,
                            true,
                            '/'
                        )} `}
                    />
                )}
            </S.InfoContainer>

            <DividerSectionCard>
                <S.Header>
                    <S.ContentData>
                        <S.TuplaDataInfo
                            label="Mês de competência"
                            value={
                                // new Date(resumoLoteGuia?.competencia).toLocaleDateString('pt-BR', {
                                //     month: 'long',
                                //     year: 'numeric'
                                // }) || '--'

                                DateUtils.getMonthFullName(resumoLoteGuia?.competencia) +
                                    ' ' +
                                    new Date(resumoLoteGuia?.competencia).toLocaleDateString('pt-BR', {
                                        year: 'numeric'
                                    }) || '--'
                            }
                            background={'grey'}
                            standart={'expanded'}
                        />
                        <S.TuplaDataInfo
                            label="Tipo de lote"
                            value={TipoLoteEnum[resumoLoteGuia?.tipoLote] || '--'}
                            background={'grey'}
                            standart={'expanded'}
                        />
                        {/* <S.TuplaDataInfo
                            label="Lote de origem"
                            value={resumoLoteGuia?.identificadorLoteOrigem || '--'}
                            background={'grey'}
                            standart={'expanded'}
                        /> */}

                        <S.BoxGrey background={'grey'} standart={'expanded'}>
                            <label>Lote de origem</label>
                            <a
                                href={`/faturamento/processamento-contas/analise-contas/resumo-lote/${resumoLoteGuia?.loteOrigemId}?competencia=${resumoLoteGuia?.competenciaLoteOrigem}`}
                                target="_blank"
                                rel="noreferrer"
                            >
                                {resumoLoteGuia?.identificadorLoteOrigem || '--'}
                            </a>
                        </S.BoxGrey>
                    </S.ContentData>
                </S.Header>

                <S.Header>
                    <S.ContentData>
                        <S.TuplaDataInfo
                            label="Apresentado"
                            value={NumberUtils.maskMoney(resumoLoteGuia?.valorTotalApresentadoOrigem)}
                            background={'grey'}
                            standart={'expanded'}
                        />
                        <S.TuplaDataInfo
                            label="Apurado"
                            value={NumberUtils.maskMoney(resumoLoteGuia?.valorTotalApuradoOrigem)}
                            background={'grey'}
                            standart={'expanded'}
                        />
                        <S.TuplaDataInfo
                            label="Glosado"
                            value={NumberUtils.maskMoney(resumoLoteGuia?.valorTotalGlosadoOrigem)}
                            background={'grey'}
                            standart={'expanded'}
                        />
                        <S.TuplaDataInfo
                            label="Recursado"
                            value={NumberUtils.maskMoney(resumoLoteGuia?.valortotalRecursado)}
                            background={'grey'}
                            standart={'expanded'}
                        />
                        <S.TuplaDataInfo
                            label="Deferido"
                            value={NumberUtils.maskMoney(resumoLoteGuia?.valorTotalDeferido)}
                            background={'grey'}
                            standart={'expanded'}
                        />
                        <S.TuplaDataInfo
                            label="Indeferido"
                            value={NumberUtils.maskMoney(resumoLoteGuia?.valortotalIndeferido)}
                            background={'grey'}
                            standart={'expanded'}
                        />
                    </S.ContentData>
                </S.Header>
            </DividerSectionCard>
            {/* <DividerSectionCard dividerContent={true}>
                <S.TitleSection>Dados do recurso de protocolo</S.TitleSection>
                <S.SubSection>
                    <p>Código da glosa do protocolo</p>
                    <span>1089 - Valores não condizem com a tabela cadastrada do prestador</span>
                </S.SubSection>
                <S.SubSection>
                    <p>Justificativa do prestador(No caso de recurso integral do protocolo) </p>
                    <span>
                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed augue neque, pretium et massa eget, viverra accumsan nulla.
                        Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Nunc hendrerit placerat libero
                        eu scelerisque. Praesent faucibus rhoncus metus, ac semper tortor iaculis a. Morbi quis dignissim sem. Etiam at felis lacus.
                        Donec lectus nulla, placerat ac nunc in, fermentum accumsan nibh. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
                    </span>
                </S.SubSection>
            </DividerSectionCard> */}
            <DividerSectionCard dividerContent={true}>
                <S.HeaderList>
                    {/* <SearchBar
                        value={filter}
                        placeholder="Procurar por guia"
                        handleOnSearch={() => {
                            setNumberPage(0)
                            getGuiasByLoteRecursoId()
                        }}
                        type="string"
                        handleOnClose={() => setFilter('')}
                        handleOnChange={(e) => {
                            if (e?.target?.value === '') {
                                setFilter('')
                            }
                            setFilter(e.target.value)
                            setNumberPage(0)
                        }}
                    /> */}

                    <SearchBar
                        value={filter}
                        placeholder="Procurar por lote"
                        handleOnSearch={() => handlePesquisar()}
                        type="text"
                        handleOnClose={() => {
                            setFilter(null), setSarchBarValue(null)
                        }}
                        handleOnChange={(e) => {
                            if (e?.target?.value === '' || e?.target?.value === null) {
                                setFilter('')
                            }
                            setFilter(e.target.value)
                            setNumberPage(0)
                        }}
                    />
                </S.HeaderList>
                <S.Header style={{ marginTop: '24px' }}>
                    <ButtonFilter
                        filter={true}
                        setNumberPage={setNumberPage}
                        setStatus={setStatus}
                        data={[
                            {
                                name: 'Análise técnica ',
                                value: `${
                                    quantitativos?.find((item) => item?.etapaGuiaRecursoAnalise === 'Análise Técnica')?.quantidadeGuia || 0
                                } - (${
                                    quantitativos?.find((item) => item?.etapaGuiaRecursoAnalise === 'Análise Técnica')?.percentual.toFixed(0) || 0
                                }%)`,
                                situacao: EtapaAnaliseEnum?.ANALISE_TECNICA
                            },
                            {
                                name: 'Análise administrativa',
                                value: `${
                                    quantitativos?.find((item) => item?.etapaGuiaRecursoAnalise === 'Análise Administrativa')?.quantidadeGuia || 0
                                } - (${
                                    quantitativos
                                        ?.find((item) => item?.etapaGuiaRecursoAnalise === 'Análise Administrativa')
                                        ?.percentual.toFixed(0) || 0
                                }%)`,
                                situacao: EtapaAnaliseEnum?.ANALISE_ADMINISTRATIVA
                            },
                            {
                                name: 'Analisadas',
                                value: `${quantitativos?.find((item) => item?.etapaGuiaRecursoAnalise === 'Fechamento')?.quantidadeGuia || 0} - (${
                                    quantitativos?.find((item) => item?.etapaGuiaRecursoAnalise === 'Fechamento')?.percentual.toFixed(0) || 0
                                }%)`,
                                situacao: EtapaAnaliseEnum?.FECHAMENTO
                            },
                            {
                                name: 'Canceladas',
                                value: `${quantitativos?.find((item) => item?.etapaGuiaRecursoAnalise === 'Cancelamento')?.quantidadeGuia || 0} - (${
                                    quantitativos?.find((item) => item?.etapaGuiaRecursoAnalise === 'Cancelamento')?.percentual.toFixed(0) || 0
                                }%)`,
                                situacao: EtapaAnaliseEnum?.CANCELAMENTO
                            }
                        ]}
                    />
                </S.Header>
                {valuesGuias?.length > 0 ? (
                    <TablePaginationRecursoOperadora
                        titles={titles}
                        values={valuesGuias}
                        pagination={pagination}
                        customGridStyles="1fr 1.5fr 2fr 1fr 1fr 1fr  0.1fr"
                        noSelectAll={true}
                        selectIdField={'id'}
                    />
                ) : (
                    <NoContent title="Nenhum lote encontrado" />
                )}
            </DividerSectionCard>

            {/* MODAL INDEFERIR LOTE */}
            <ModalIndeferirLote
                uuid={loteRecursoId}
                openModal={openModalIndeferirLote}
                setCloseModal={() => setOpenModalIndeferirLote(false)}
                handleIndeferir={() => {
                    getResumoGuias()
                    setRefresh(!refresh)
                    setOpenModalIndeferirLote(false)
                }}
                lote={resumoLoteGuia}
            />

            {/* MODAL FECHAR LOTE */}
            <Modal isOpen={openModalFecharLote} onClose={() => setOpenModalFecharLote(false)} style={{ padding: 0 }}>
                <S.WrapperModal>
                    <h1>Fechar o lote - {resumoLoteGuia?.identificadorLoteRecurso}?</h1>
                    <p>Ao fechar o lote, será possível reabrir o lote se a competência não estiver encerrada.</p>
                    <S.WrapperButtons>
                        <Button themeButton="gray" typeButton="text" onClick={() => setOpenModalFecharLote(false)}>
                            Cancelar
                        </Button>
                        <Button themeButton="secondary" onClick={handleFecharLote} disabled={loadingFecharAbrir}>
                            {loadingFecharAbrir ? 'Fechando...' : 'Fechar'}
                        </Button>
                    </S.WrapperButtons>
                </S.WrapperModal>
            </Modal>

            {/* MODAL CANCELAR LOTE */}
            <Modal isOpen={openModalCancelarLote} onClose={() => setOpenModalCancelarLote(false)} style={{ padding: 0 }}>
                <S.WrapperModal>
                    <h1>Cancelar o lote {resumoLoteGuia?.identificadorLoteRecurso}?</h1>
                    <p>Informe a justificativa do cancelamento para o Prestador</p>
                    <Input
                        isDefault="default"
                        label="Justificativa"
                        value={justificativaCancelamento}
                        handleOnChange={(value) => {
                            setJustificativaCancelamento(value)
                        }}
                    />
                    <S.WrapperButtons>
                        <Button themeButton="gray" typeButton="text" onClick={() => setOpenModalCancelarLote(false)}>
                            Não
                        </Button>
                        <Button
                            disabled={loadingCancelar || !justificativaCancelamento}
                            themeButton="secondary"
                            style={{ width: 'fit-content', backgroundColor: 'rgba(244, 67, 54, 0.04)', color: '#F44336' }}
                            onClick={handleCancelarLote}
                        >
                            {loadingCancelar ? 'Cancelando...' : 'Sim, cancelar'}
                        </Button>
                    </S.WrapperButtons>
                </S.WrapperModal>
            </Modal>

            {/* MODAL REABRIR LOTE */}
            <Modal isOpen={openModalReabrirLote} onClose={() => setOpenModalReabrirLote(false)} style={{ padding: 0 }}>
                <S.WrapperModal>
                    <h1>Reabrir o lote {resumoLoteGuia?.identificadorLoteRecurso}?</h1>
                    <p>Ao reabrir o lote, será possível alterar o lote e as guias novamente.</p>

                    <S.WrapperButtons>
                        <Button themeButton="gray" typeButton="text" onClick={() => setOpenModalReabrirLote(false)}>
                            Cancelar
                        </Button>
                        <Button themeButton="secondary" onClick={handleReabrirLote} disabled={loadingFecharAbrir}>
                            {loadingFecharAbrir ? 'Reabrindo..' : 'Reabrir'}
                        </Button>
                    </S.WrapperButtons>
                </S.WrapperModal>
            </Modal>
        </>
    )
}

export default ResumoLotesTemplate
