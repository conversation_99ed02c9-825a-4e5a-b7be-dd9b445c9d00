import styled from 'styled-components'

export const Wrapper = styled.div`
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgb(0, 0, 0, 0.5);
    position: fixed;
    z-index: 100;
    cursor: pointer;

    animation: mycolor 2s ease-in-out;

    @keyframes mycolor {
        from {
            background-color: rgb(0, 0, 0, 0);
        }
        to {
            background-color: rgb(0, 0, 0, 0.5);
        }
    }
`

export const Content = styled.div`
    cursor: default;
    position: fixed;
    display: flex;
    height: 100vh;
    top: 0;
    right: 0;
    flex-direction: column;
    gap: 24px;
    width: 376px;
    padding: 24px;
    padding-bottom: 160px;
    overflow-y: auto;
    background-color: rgba(255, 255, 255, 1);
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    z-index: 120;

    animation: mymove 1s ease-in-out;

    @keyframes mymove {
        from {
            right: -380px;
        }
        to {
            right: 0px;
        }
    }
`
