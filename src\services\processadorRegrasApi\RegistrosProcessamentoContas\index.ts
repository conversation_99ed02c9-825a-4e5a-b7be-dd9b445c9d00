import { apiProcessadorRegras } from 'src/services/apis/apiProcessadorRegras'
import {
    IGetDownloadXmlLoteProps,
    IGetItemProcessamentoPrestadorDto,
    IGetPermiteRedistribuirLotesAgendadosProps,
    IGetProcessamentoContasQuantitativoDto,
    IGetProcessamentoContasQuantitativosProps,
    IGetProcessamentoPrestadoresProps,
    IGetRedistribuirLotesAgendadosProps,
    IGetRegistroProcessamentoLoteDto,
    IGetRegistrosProcessamentoProps,
    IGetSincronizarAnaliseContasProps,
    IPatchReprocessarLotesComFalhaProps,
    IProcessamentoContasQuantitativoResumo
} from './types'
import { IPageResult } from 'types/common/pagination'
import { MockGetItemProcessamentoPrestadorPage, MockGetProcessamentoContasQuantitativoDto, MockGetRegistroProcessamentoLotePage } from './mocks'
import { SituacaoLote } from 'types/common/enums'
import { SituacaoProcessamentoLoteEnum } from './enums'

const baseUrl = '/lote/processamento/registros'

export class RegistrosProcessamentoContasService {
    static tempoBaseConsiderarLoteEmAberto = 30

    static getDadosQuantitativos({ competencia, tipoProcessamentoLote, codigoContratado, emAbertoPor }: IGetProcessamentoContasQuantitativosProps) {
        return apiProcessadorRegras
            .get<IGetProcessamentoContasQuantitativoDto>(`${baseUrl}/resumo-processamento`, {
                params: {
                    tipoProcessamentoLote,
                    competencia,
                    codigoContratado: codigoContratado || undefined,
                    emAbertoPor: emAbertoPor || undefined
                }
            })
            .then(({ data, ...rest }) => ({ data: this.mapDadosQuantitativos(data), ...rest }))
    }

    static getProcessamentoPrestadores({
        competencia,
        tipoProcessamentoLote,
        codigoContratado,
        situacaoLote,
        situacaoProcessamentoLote,
        emAbertoPor,
        page,
        size
    }: IGetProcessamentoPrestadoresProps) {
        return apiProcessadorRegras.get<IPageResult<IGetItemProcessamentoPrestadorDto>>(`${baseUrl}/resumo-processamento-prestador`, {
            params: {
                tipoProcessamentoLote,
                competencia,
                codigoContratado: codigoContratado || undefined,
                situacaoLote: situacaoLote || undefined,
                situacaoProcessamentoLote: situacaoProcessamentoLote || undefined,
                emAbertoPor: emAbertoPor || undefined,
                page,
                size
            }
        })
    }

    static getRegistrosProcessamento({
        competencia,
        prestadorId,
        situacaoLote,
        situacaoProcessamento,
        identificadorLote,
        page,
        size
    }: IGetRegistrosProcessamentoProps) {
        return apiProcessadorRegras.get<IPageResult<IGetRegistroProcessamentoLoteDto>>(`${baseUrl}`, {
            params: {
                tipoProcessamentoLote: 'NORMAL',
                situacaoRegistroLote: situacaoProcessamento === SituacaoProcessamentoLoteEnum.FALHA ? 'FINALIZADO' : undefined,
                competencia,
                prestadorId: prestadorId || undefined,
                identificadorLote: identificadorLote || undefined,
                situacaoLote: situacaoLote || undefined,
                situacaoProcessamento: situacaoProcessamento || undefined,
                page,
                size
            }
        })
    }

    static reprocessarLotesComFalha({ competencia, prestadorId, identificadorLotes }: IPatchReprocessarLotesComFalhaProps) {
        return apiProcessadorRegras.patch<any>(`${baseUrl}/reagendar-todos`, undefined, {
            params: {
                competencia
            }
        })
    }

    static sincronizarAnaliseContas({ competencia }: IGetSincronizarAnaliseContasProps) {
        return apiProcessadorRegras.get<any>(`${baseUrl}/sincronizar-analise-contas`, {
            params: {
                competencia
            }
        })
    }

    static getPermiteRedistribuirLotesAgendados({ competencia }: IGetPermiteRedistribuirLotesAgendadosProps) {
        return apiProcessadorRegras.get<boolean>(`${baseUrl}/balancear-agendamento`, {
            params: {
                competencia
            }
        })
    }

    static redistribuirLotesAgendados({ competencia }: IGetRedistribuirLotesAgendadosProps) {
        return apiProcessadorRegras.patch<any>(`${baseUrl}/balancear-agendamento`, undefined, {
            params: {
                competencia
            }
        })
    }

    static downloadXmlLote({ identificadorLote }: IGetDownloadXmlLoteProps) {
        return apiProcessadorRegras.get<any>(`/lote/${identificadorLote}/download/xml-lote`, { responseType: 'blob' })
    }

    static mapDadosQuantitativos(dados: IGetProcessamentoContasQuantitativoDto): IProcessamentoContasQuantitativoResumo {
        return {
            percentualFalha: dados?.percentualFalha,
            percentualRecusa: dados?.percentualRecusa,
            lotesEmAbertoAcimaTempoBase: dados?.lotesEmAbertoAcimaTempoBase || 0,
            quantidadeLotesNaoSicronizados: dados?.quantidadeLotesNaoSicronizados || 0,
            quantidadeLotesFalhaCompetencia: dados?.quantidadeLotesFalhaCompetencia || 0,
            quantidadeLotesAgendados:
                dados?.resumoProcessamentoLote?.find(({ situacaoProcessamento }) => situacaoProcessamento === SituacaoProcessamentoLoteEnum.AGENDADO)
                    ?.quantidadeLotes || 0,
            quantidadeLotesProcessados:
                dados?.resumoProcessamentoLote?.find(
                    ({ situacaoProcessamento }) => situacaoProcessamento === SituacaoProcessamentoLoteEnum.PROCESSADO
                )?.quantidadeLotes || 0,
            quantidadeLotesComFalha:
                dados?.resumoProcessamentoLote?.find(({ situacaoProcessamento }) => situacaoProcessamento === SituacaoProcessamentoLoteEnum.FALHA)
                    ?.quantidadeLotes || 0,
            quantidadeLotesProcessando:
                dados?.resumoProcessamentoLote?.find(
                    ({ situacaoProcessamento }) => situacaoProcessamento === SituacaoProcessamentoLoteEnum.EM_PROCESSAMENTO
                )?.quantidadeLotes || 0,
            quantidadeLotesRecusados: dados?.resumoLoteProcessado?.find(({ situacao }) => situacao === SituacaoLote.RECUSADO)?.quantidadeLotes || 0,
            quantidadeLotesEmAnalise: dados?.resumoLoteProcessado?.find(({ situacao }) => situacao === SituacaoLote.EM_ANALISE)?.quantidadeLotes || 0,
            quantidadeLotesCancelados: dados?.resumoLoteProcessado?.find(({ situacao }) => situacao === SituacaoLote.CANCELADO)?.quantidadeLotes || 0,
            resumoTempoProcessamento: {
                mediaTempoProcessamento: dados?.resumoTempoProcessamento?.mediaTempoProcessamento || 0,
                menor15: dados?.resumoTempoProcessamento?.menor15 || 0,
                entre15e60: dados?.resumoTempoProcessamento?.entre15e60 || 0,
                maior60: dados?.resumoTempoProcessamento?.maior60 || 0,
                totalRegistros: dados?.resumoTempoProcessamento?.totalRegistros || 0
            }
        }
    }
}
