import { GetServerSideProps } from 'next'
import React from 'react'
import CalendarioCriarEditarTemplate from 'src/templates/FaturamentoOperadora/CalendarioFaturamentoTemplates/CalendarioCriarEditarTemplate'

const ChangeCalendar = ({ id }) => {
    return <CalendarioCriarEditarTemplate id={id} />
}

export const getServerSideProps: GetServerSideProps = async (ctx) => {
    const id = ctx.query?.id

    return {
        props: {
            id
        }
    }
}

export default ChangeCalendar
