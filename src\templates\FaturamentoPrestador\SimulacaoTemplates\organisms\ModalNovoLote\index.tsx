/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-vars */
import * as S from './styles'
import React, { useCallback, useEffect, useState } from 'react'
import Button from 'components/atoms/Button'
import Input from 'components/atoms/Inputs/Input'
import Item from 'components/atoms/Item'
import DragAndDrop from 'components/molecules/DragAndDrop'
import Progress from 'components/molecules/Progress'
import Select from 'components/molecules/Select'
import SubmenuBox from 'components/molecules/SubmenuBox'
import TemplateModal from 'components/organisms/TemplateModal'
import { useRouter } from 'next/router'
import { useToast } from 'src/hooks/toast'
import { CobrancaServices } from 'src/services/faturamentoPrestadorApi/cobrancaServices'
import { RecursoGlosaServices } from 'src/services/faturamentoPrestadorApi/recursoGlosaServices'
import { IErrosXml, LocalData } from 'types/cobrancaPrestador/loteCobranca'
import { TipoArquivoModeloCSV, TipoEnvioNotaFiscal } from 'types/common/enums'
import { base64ToFile, fileToBase64 } from 'utils/fileUtils'
import { TipoSolicitacaoGuia } from 'types/common/enums'
import { ReactSVG } from 'react-svg'
import ErrorExpanded from 'components/molecules/ErrorExpanded'

export type TipoEnvioLote = 'cobranca' | 'recurso_glosa'
export type TipoArquivoLote = 'XML' | 'CSV'
export type TipoAcao = 'selecionar_xml' | 'selecionar_csv' | 'selecionar_nota_fiscal'
interface ModalNovoLoteProps {
    isOpen: boolean
    tipoLote: TipoEnvioLote
    setIsOpen: (isOpen: boolean) => void
    initialStep?: number
    onClickEnviarNotaFiscal: (idLote?: string, numeroNota?: string, valorNota?: string, dataEmissao?: string, file?: any) => void
    competencia?: string
    labelButton: string
    onClickConciliarNotaFiscal?: (idLote?: string) => void
    handleSetStepDocumentos?: any
    reset?: boolean
    setReset?: any
    setForceUpdateCompetencias?: any
}

const ModalNovoLote = ({
    isOpen,
    tipoLote,
    setIsOpen,
    onClickEnviarNotaFiscal,
    initialStep = 1,
    competencia,
    labelButton,
    onClickConciliarNotaFiscal,
    handleSetStepDocumentos,
    reset,
    setReset,
    setForceUpdateCompetencias
}: ModalNovoLoteProps) => {
    const router = useRouter()

    const { addToast } = useToast()

    const [localData, setLocalData] = useState<LocalData>(null)
    const [loadingFileLot, setLoadingFileLot] = useState(false)
    const [loadingFileNF, setLoadingFileNF] = useState(false)
    const [hasFileLot, setHasFileLot] = useState<boolean>(false)
    const [hasFileNF, setHasFileNF] = useState<boolean>(false)
    const [fileLotUploadModal, setFileLotUploadModal] = useState<File | null>(null)
    const [fileNFUploadModal, setFileNFUploadModal] = useState<File | null>(null)
    const [step, setStep] = useState(initialStep)
    const [idLoteCobranca, setIdLoteCobranca] = useState<string>()
    const [numeroNota, setNumeroNota] = useState<string>()
    const [valorNota, setValorNota] = useState<string>()
    const [dataEmissao, setDataEmissao] = useState<string>('')
    const [uploadedFile, setUploadedFile] = useState<File | null>(null)
    const [loading, setLoading] = useState<boolean>(false)
    const [action, setAction] = useState<TipoAcao>(tipoLote === 'recurso_glosa' ? 'selecionar_xml' : undefined)
    const [tipoArquivoLote, setTipoArquivoLote] = useState<TipoArquivoLote>(tipoLote === 'recurso_glosa' ? 'XML' : undefined)
    const [selecionandoTipoArquivoLote, setSelecionandoTipoArquivoLote] = useState(false)
    const [modeloCSVSelecionado, setModeloCSVSelecionado] = useState(false)
    const [baixandoModeloCSV, setBaixandoModeloCSV] = useState(false)
    const [errors, setErrors] = useState<IErrosXml[]>([])

    useEffect(() => {
        setSelecionandoTipoArquivoLote(tipoLote === 'cobranca' && tipoArquivoLote === undefined)
    }, [tipoLote, tipoArquivoLote])

    useEffect(() => {
        setBaixandoModeloCSV(tipoArquivoLote === 'CSV' && !modeloCSVSelecionado)
    }, [modeloCSVSelecionado, tipoArquivoLote])

    const resetState = () => {
        setLocalData(null)
        setLoadingFileLot(false)
        setLoadingFileNF(false)
        setHasFileLot(false)
        setHasFileNF(false)
        setFileLotUploadModal(null)
        setFileNFUploadModal(null)
        setStep(initialStep)
        setIdLoteCobranca(null)
        setNumeroNota(null)
        setValorNota(null)
        setHasFileLot(false)
        setHasFileNF(false)
        setUploadedFile(null)
        setLoading(false)
        setAction(tipoLote === 'recurso_glosa' ? 'selecionar_xml' : undefined)
        setTipoArquivoLote(tipoLote === 'recurso_glosa' ? 'XML' : undefined)
        setModeloCSVSelecionado(undefined)
        setReset(false)
        setErrors([])
    }

    function getTitle() {
        if (selecionandoTipoArquivoLote) return 'Como deseja enviar um novo lote?'

        if (baixandoModeloCSV) return 'Etapa 1 de 4 - Baixar modelo de arquivo CSV'

        if (step === 1) {
            return `${
                loadingFileLot ? 'Adicionando arquivo...' : `Adicionar arquivo ${tipoArquivoLote}`
            }`
        }

        if (step === 2) {
            return `Etapa ${tipoArquivoLote === 'XML' ? 2 : 3} de ${tipoArquivoLote === 'XML' ? 3 : 4} - ${
                loadingFileNF ? 'Adicionando nota fiscal...' : 'Adicione a nota fiscal'
            }`
        }

        return ''
    }

    const closeModal = () => {
        resetState()
        setIsOpen(false)
        setForceUpdateCompetencias('update')
        setErrors([])
    }

    const saveLocalData = useCallback(
        (data: LocalData) => {
            setLocalData({ ...localData, ...data })
        },
        [localData, setLocalData]
    )

    useEffect(() => {
        if (!localData || tipoLote !== 'cobranca') {
            resetState()
            return
        }

        const file = base64ToFile(localData?.fileBase64?.data, localData?.fileBase64?.name)
        const result = localData?.result
        const exigeNF = result?.tipoEnvioNotaFiscal === TipoEnvioNotaFiscal.PRE_PROCESSAMENTO
        const loteId = localData?.result?.loteCobrancaId
        const documentosObrigatorios = result?.documentosObrigatorios

        if (!exigeNF && isOpen) {
            if (!result?.documentosObrigatorios?.length) {
                addToast({ title: 'Lote enviados com sucesso.', type: 'success', duration: 5000 })
                onClickEnviarNotaFiscal()
                return closeModal()
            }

            return handleSetStepDocumentos({ idLoteCobranca: loteId, documentosObrigatorios, exigeNF })
        }

        console.log('> exigeNF: ', step, hasFileLot, !loadingFileLot)

        if (exigeNF) {
            setStep(2)
            setAction('selecionar_nota_fiscal')
        }

        setFileLotUploadModal(file)
        setIdLoteCobranca(loteId)
        setHasFileLot(true)
        setLoading(false)
        setUploadedFile(null)
    }, [isOpen, localData, setHasFileLot, setIdLoteCobranca, setStep])

    function uploadLotFile(file: File | null) {
        if (file !== null) {
            setFileLotUploadModal(file)

            if (tipoLote === 'cobranca') {
                setErrors([])
                CobrancaServices.postLoteEnvioEletronicoSimulacao({ competencia: competencia, arquivo: file })
                    .then(async ({ data: result }) => {
                        if (result?.erros?.length > 0) {
                            /* addToast({
                                title: 'O arquivo enviado pode está corrompido ou sua estrutura é inválida. Contate o fornecedor do software emissor do arquivo.',
                                type: 'error',
                                duration: 8000
                            }) */
                            setErrors(result?.erros)
                            setFileLotUploadModal(null)
                            setHasFileLot(false)
                            setLoading(false)
                            return
                        }

                        const fileBase64 = await fileToBase64(file)
                        saveLocalData({
                            result: result?.loteEnviado,
                            fileBase64
                        })
                        // setIdLoteCobranca(data.loteCobrancaId)
                        // setHasFileLot(true)
                    })
                    .catch((error) => {
                        const errorMessage = error?.response?.data?.message

                        console.log(errorMessage)

                        let messageToast = 'Ocorreu erro ao enviar o lote de cobrança. Tente novamente.'
                        if (errorMessage != null) {
                            messageToast = errorMessage
                        }
                        addToast({
                            title: messageToast,
                            type: 'error',
                            duration: 8000
                        })
                        setFileLotUploadModal(null)
                        setHasFileLot(false)
                        setErrors([])
                        closeModal()
                    })
            }

            if (tipoLote === 'recurso_glosa') {
                RecursoGlosaServices.postLoteEnvioEletronico({ arquivo: file })
                    .then(({ data }) => {
                        setIdLoteCobranca(data?.loteEnviado?.loteCobrancaId)
                        setHasFileLot(true)
                    })
                    .catch(() => {
                        addToast({
                            title: 'Ocorreu erro ao enviar o lote de recurso de glosa. Tente novamente.',
                            type: 'error'
                        })
                        setFileLotUploadModal(null)
                        setHasFileLot(false)
                    })
            }
        } else {
            setFileLotUploadModal(null)
            setHasFileLot(false)
            setUploadedFile(null)
        }
    }

    function uploadNFFile(file: File | null) {
        if (file !== null) {
            setFileNFUploadModal(file)

            setHasFileNF(true)
        } else {
            setFileNFUploadModal(null)
            setHasFileNF(false)
        }
    }

    const handleSetUploadedFile = (file: File) => {
        setUploadedFile(file)
        setFileLotUploadModal(file)
    }

    const handleSubmitUploadedFile = () => {
        setLoading(true)
        uploadLotFile(uploadedFile)
    }

    //   function SendResourceGloss() {
    //     setFileUpload(fileLotUploadModal);
    //   }
    // useEffect(() => {
    //     setStep(initialStep)
    // }, [isOpen])

    const handleSendNF = useCallback(() => {
        const result = localData?.result
        const exigeNF = result?.tipoEnvioNotaFiscal === TipoEnvioNotaFiscal.PRE_PROCESSAMENTO

        handleSetStepDocumentos({
            exigeNF,
            idLoteCobranca,
            numeroNota,
            valorNota,
            dataEmissao,
            fileNFUploadModal,
            documentosObrigatorios: localData?.result?.documentosObrigatorios
        })
    }, [handleSetStepDocumentos, idLoteCobranca, numeroNota, valorNota, fileNFUploadModal, localData])

    const handleClickConciliarNota = () => {
        onClickConciliarNotaFiscal(idLoteCobranca)
        closeModal()
    }

    useEffect(() => {
        if (!reset) return

        closeModal()
    }, [reset])

    const baixarModeloCSV = (tipoArquivo: TipoArquivoModeloCSV) => {
        CobrancaServices.getModeloCSVGeracaoManualLote(tipoArquivo).then((response) => {
            const fileName = response.headers['content-disposition'].split('filename=')[1] ?? 'modelo.csv'
            const url = window.URL.createObjectURL(response.data)
            const link = document.createElement('a')
            link.href = url
            link.setAttribute('download', fileName)
            document.body.appendChild(link)
            link.click()
        })
    }

    useEffect(() => {
        if (!hasFileLot) {
            setErrors([])
        }
    }, [hasFileLot])

    return (
        <TemplateModal
            title={getTitle()}
            isOpen={isOpen}
            setIsOpen={setIsOpen}
            showIconClosed={selecionandoTipoArquivoLote}
            closeOnTouchOverlay={selecionandoTipoArquivoLote}
        >
            <S.ContentModal>
                {/* {step === 1 && (
                    <>
                        <p>Escolha uma das opções.</p>
                        <S.RowBoxs>
                            <SubmenuBox image="/faturamento/assets/icons/icon1.svg" message="Enviando arquivo xml" onClick={() => setStep(2)} />
                            <SubmenuBox
                                image="/faturamento/assets/icons/icon2.svg"
                                message="Selecionando as guias"
                                onClick={() => {
                                    route.push('cobranca/novo-lote')
                                }}
                            />
                        </S.RowBoxs>
                    </>
                )} */}
                {/* {step === 1 && ( */}
                {selecionandoTipoArquivoLote ? (
                    <>
                        <p className="description">Selecione o tipo de arquivo você quer enviar</p>
                        <S.TipoArquivoLoteList>
                            <S.TipoArquivoLoteOption
                                onClick={() => {
                                    setTipoArquivoLote('XML')
                                    setAction('selecionar_xml')
                                }}
                            >
                                <span>Arquivo XML</span>
                                <p>Enviar um arquivo gerado automaticamente por sistema no padrão TISS.</p>
                            </S.TipoArquivoLoteOption>
                        </S.TipoArquivoLoteList>
                    </>
                ) : (
                    <>
                        <p className="description">
                            {hasFileLot ? (
                                loadingFileLot ? (
                                    <span>Por favor, aguarde um momento</span>
                                ) : (
                                    <span>
                                        Preencha os campos abaixo e adicione também o arquivo da nota fiscal, em <b>XML</b> ou <b>PDF</b>, referente
                                        ao lote
                                    </span>
                                )
                            ) : baixandoModeloCSV ? (
                                <span>
                                    Baixe o <b>modelo de arquivo</b> correspondente ao tipo de lote
                                </span>
                            ) : tipoArquivoLote === 'XML' ? (
                                <span>
                                    Adicione o arquivo em <b>XML</b> com as informações das guias que serão vinculadas ao lote.
                                    {errors?.length > 0 && (
                                        <div>
                                            <ErrorExpanded
                                                initialOpen={true}
                                                text="O arquivo enviado pode está corrompido ou sua estrutura é inválida. Contate o fornecedor do software emissor do arquivo."
                                            >
                                                <S.ErrorsWrapper>
                                                    <S.PresentationErrors>{JSON.stringify(errors)}</S.PresentationErrors>
                                                    <div
                                                        onClick={() => {
                                                            addToast({
                                                                title: 'Erros copiados para área de transferência',
                                                                type: 'success'
                                                            })
                                                            navigator.clipboard.writeText(JSON.stringify(errors))
                                                        }}
                                                    >
                                                        <ReactSVG
                                                            style={{ cursor: 'pointer' }}
                                                            src="/faturamento/assets/icons/copy-clipboard.svg"
                                                            wrapper="div"
                                                            className="icon"
                                                        />
                                                    </div>
                                                </S.ErrorsWrapper>
                                            </ErrorExpanded>
                                        </div>
                                    )}
                                </span>
                            ) : (
                                <span>
                                    Agora, adicione o <b>modelo de arquivo</b> da etapa anterior preenchido com as informações das guias
                                </span>
                            )}
                        </p>
                        {hasFileLot && !loadingFileLot && (
                            <S.ContainerInputs>
                                <Item>
                                    <p
                                        style={{
                                            width: '390px',
                                            cursor: 'pointer',
                                            borderRadius: '24px',
                                            padding: '10px 20px',
                                            border: '2px solid rgba(0, 0, 0, 0.56)'
                                        }}
                                        onClick={handleClickConciliarNota}
                                    >
                                        Conciliar uma nota fiscal já enviada
                                    </p>
                                </Item>
                                {/* <Select
                                required
                                label="Tipo de Documento"
                                options={[{ value: 'NOTA_FISCAL', label: 'NOTA FISCAL' }]}
                                loadOptions={(inputValue: string, callback: any) => {
                                    // loadOptions(callback, 'tipo-consulta');
                                }}
                                value={{ value: 'NOTA_FISCAL', label: 'NOTA FISCAL' }}
                                // handleFieldsChange(setFieldsOncological, 'cid_10_main', option);
                            /> */}
                                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
                                    <Input
                                        isDefault="default"
                                        label="Número da nota"
                                        value={numeroNota}
                                        type="text"
                                        required
                                        handleOnChange={(value: string) => {
                                            // handleFieldsChange(setFieldsOncological, 'therapeutic_plan', value)
                                            setNumeroNota(value)
                                        }}
                                    />
                                    <Input
                                        isDefault="default"
                                        label="Valor da nota"
                                        mask="monetary"
                                        value={valorNota}
                                        type="text"
                                        required
                                        handleOnChange={(value: string) => {
                                            // handleFieldsChange(setFieldsOncological, 'therapeutic_plan', value)
                                            setValorNota(value)
                                        }}
                                    />
                                </div>
                                <Input
                                    required
                                    mask="date"
                                    type="text"
                                    isDefault="default"
                                    label="Data de emissão"
                                    value={dataEmissao}
                                    handleOnChange={(value: string) => setDataEmissao(value)}
                                />
                            </S.ContainerInputs>
                        )}

                        {baixandoModeloCSV ? (
                            <div style={{ marginTop: 16 }}>
                                <S.ModeloCSVList>
                                    <S.ModeloCSVOption onClick={() => baixarModeloCSV(TipoArquivoModeloCSV.CONSULTA)}>
                                        <ReactSVG src="/faturamento/assets/icons/form.svg" wrapper="div" className="icon" />
                                        <span className="label">Consulta</span>
                                    </S.ModeloCSVOption>
                                    <S.ModeloCSVOption onClick={() => baixarModeloCSV(TipoArquivoModeloCSV.SPSADT)}>
                                        <ReactSVG src="/faturamento/assets/icons/form.svg" wrapper="div" className="icon" />
                                        <span className="label">SP/SADT</span>
                                    </S.ModeloCSVOption>
                                    <S.ModeloCSVOption onClick={() => baixarModeloCSV(TipoArquivoModeloCSV.RESUMO_INTERNACAO)}>
                                        <ReactSVG src="/faturamento/assets/icons/form.svg" wrapper="div" className="icon" />
                                        <span className="label">Internação</span>
                                    </S.ModeloCSVOption>
                                    <S.ModeloCSVOption onClick={() => baixarModeloCSV(TipoArquivoModeloCSV.RESUMO_INTERNACAO)}>
                                        <ReactSVG src="/faturamento/assets/icons/form.svg" wrapper="div" className="icon" />
                                        <span className="label">Prorrogação de internação</span>
                                    </S.ModeloCSVOption>
                                    <S.ModeloCSVOption onClick={() => baixarModeloCSV(TipoArquivoModeloCSV.ANEXO_DE_OUTRAS_DESPESAS)}>
                                        <ReactSVG src="/faturamento/assets/icons/form.svg" wrapper="div" className="icon" />
                                        <span className="label">OPME</span>
                                    </S.ModeloCSVOption>
                                    <S.ModeloCSVOption onClick={() => baixarModeloCSV(TipoArquivoModeloCSV.ANEXO_DE_OUTRAS_DESPESAS)}>
                                        <ReactSVG src="/faturamento/assets/icons/form.svg" wrapper="div" className="icon" />
                                        <span className="label">Quimioterapia</span>
                                    </S.ModeloCSVOption>
                                    <S.ModeloCSVOption onClick={() => baixarModeloCSV(TipoArquivoModeloCSV.ANEXO_DE_OUTRAS_DESPESAS)}>
                                        <ReactSVG src="/faturamento/assets/icons/form.svg" wrapper="div" className="icon" />
                                        <span className="label">Radioterapia</span>
                                    </S.ModeloCSVOption>
                                </S.ModeloCSVList>
                            </div>
                        ) : (
                            <div style={{ marginTop: 16 }}>
                                {['selecionar_xml', 'selecionar_csv'].includes(action) && (
                                    <>
                                        {fileLotUploadModal !== null && (
                                            <Progress
                                                name={fileLotUploadModal?.name}
                                                onClick={() => {
                                                    uploadLotFile(null)
                                                }}
                                                setLoadingFile={setLoadingFileLot}
                                                permiteRemover={!hasFileLot}
                                            />
                                        )}

                                        {fileLotUploadModal === null && (
                                            <DragAndDrop
                                                file={fileLotUploadModal}
                                                acceptExtensions={tipoArquivoLote === 'XML' ? '.xml' : '.csv'}
                                                uploadFile={handleSetUploadedFile}
                                                setLoadingFile={fileLotUploadModal}
                                            />
                                        )}
                                    </>
                                )}

                                {action === 'selecionar_nota_fiscal' && fileLotUploadModal !== null && fileNFUploadModal !== null && (
                                    <Progress
                                        name={fileNFUploadModal?.name}
                                        onClick={() => {
                                            uploadNFFile(null)
                                        }}
                                        setLoadingFile={setLoadingFileNF}
                                    />
                                )}

                                {action === 'selecionar_nota_fiscal' && fileNFUploadModal === null && (
                                    <DragAndDrop
                                        uploadFile={uploadNFFile}
                                        file={fileNFUploadModal}
                                        setLoadingFile={fileNFUploadModal}
                                        acceptExtensions=".xml, .pdf"
                                    />
                                )}

                                {!hasFileLot ? (
                                    <>
                                        {tipoArquivoLote === 'XML' ? (
                                            <small>
                                                Adicione apenas um arquivo em XML, com tamanho máximo de <b>2MB</b>
                                            </small>
                                        ) : (
                                            <small>
                                                Adicione o arquivo com até <b>100 guias</b>
                                            </small>
                                        )}
                                    </>
                                ) : (
                                    <small>
                                        Adicione apenas arquivos em XML ou PDF, com tamanho máximo de <b>2MB</b>
                                    </small>
                                )}
                            </div>
                        )}

                        <S.ContainerBottom>
                            {/* {!hasFileLot && ( */}
                            {baixandoModeloCSV || (step === 1 && action === 'selecionar_csv') ? (
                                <Button
                                    typeButton="text"
                                    themeButton="octopus"
                                    onClick={() => {
                                        if (baixandoModeloCSV) {
                                            setTipoArquivoLote(undefined)
                                            setAction(undefined)
                                        }
                                        if (step === 1 && action === 'selecionar_csv') {
                                            setModeloCSVSelecionado(false)
                                        }
                                    }}
                                >
                                    Voltar
                                </Button>
                            ) : (
                                <Button typeButton="text" themeButton="octopus" onClick={() => closeModal()}>
                                    Cancelar
                                </Button>
                            )}

                            {/* )} */}

                            {['selecionar_xml', 'selecionar_csv'].includes(action) && (
                                <>
                                    {action === 'selecionar_csv' && !modeloCSVSelecionado ? (
                                        <Button themeButton="secondary" onClick={() => setModeloCSVSelecionado(true)}>
                                            Próximo
                                        </Button>
                                    ) : (
                                        <Button
                                            themeButton="secondary"
                                            onClick={handleSubmitUploadedFile}
                                            disabled={!(!loadingFileLot && uploadedFile) || loading}
                                        >
                                            {loading ? (
                                                <S.Spinner>
                                                    <div className="loader" />
                                                </S.Spinner>
                                            ) : (
                                                labelButton
                                            )}
                                        </Button>
                                    )}
                                </>
                            )}

                            {action === 'selecionar_nota_fiscal' && (
                                <Button
                                    themeButton="secondary"
                                    disabled={
                                        !(
                                            hasFileLot &&
                                            hasFileNF &&
                                            numeroNota &&
                                            valorNota &&
                                            dataEmissao.length === 10 &&
                                            valorNota !== 'R$ 0,00'
                                        ) || loadingFileNF
                                    }
                                    onClick={handleSendNF}
                                >
                                    {labelButton}
                                </Button>
                            )}
                        </S.ContainerBottom>
                    </>
                )}
                {/* )} */}
            </S.ContentModal>
        </TemplateModal>
    )
}

export default ModalNovoLote
