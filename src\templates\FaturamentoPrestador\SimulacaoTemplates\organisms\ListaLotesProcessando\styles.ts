/* eslint-disable prettier/prettier */
import styled, { css } from 'styled-components'
import { shade } from 'polished'

export const Wrapper = styled.div`
    background: #fff;

    .contentInput {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin-bottom: 32px;

        div {
            max-width: 592px;
        }

        .contentNumber {
            display: flex;
            .pageNumber {
                font-weight: 400;
                font-size: 16px;
                line-height: 24px;
                margin-right: 20px;
            }

            svg {
                cursor: pointer;
            }
        }
    }
`

export const Table = styled.table`
    width: 100%;
    border-collapse: separate;
    border-spacing: 0 5px;
    margin: 2rem 0;
    background: white;

    text-align: center;
    .checkLotLabel {
        /* background: yellow; */
        display: flex;
        justify-content: start;
        align-items: center;

        height: 40px !important;

        input {
            margin-right: 24px;
            height: 18px;
            width: 18px;
        }
    }
    tr {
        height: 30px;
    }

    th {
        font-size: 1.2rem;
        font-weight: 600;
        text-align: left;
        padding-left: 10px;
        text-align: left;
    }

    .last-column {
        text-align: right;
        padding-right: 40px;
    }
`
interface TabContentProps {
    checked: boolean
}
export const TabContent = styled.tr<TabContentProps>`
    ${({ theme, checked }) => css`
        background-color: ${checked ? '#E8EBFD' : '#f7f8fd'};
        color: ${theme.colors.black['56']};

        font-size: 1.4rem;
        font-weight: 400;
        height: 40px !important;

        .first-column {
            color: ${theme.colors.primary['500']};
            font-weight: 600;
        }
        .checkLot {
            /* background: yellow; */
            display: flex;
            justify-content: start;
            align-items: center;

            height: 40px !important;

            input {
                width: 18px;
                height: 18px;
                margin-right: 19px;
                background: rgba(0, 0, 0, 0.56);
            }
        }

        .last-column {
            text-align: right;
            padding-right: 10px;
        }

        td {
            text-align: left;
            padding-left: 10px;
        }

        :hover {
            background: ${!checked && shade(0.01, '#f7f8fd')};
            cursor: pointer;
        }

        @media screen and (max-width: 1024px) {
            /* display: table; */
        }
    `}
`

export const ContentCancelLotes = styled.div`
    display: flex;
    align-items: center;
    gap: 19px;
    cursor: pointer;

    p {
        font-weight: 600;
        font-size: 12px;
        line-height: 16px;
        color: rgba(0, 0, 0, 0.88);
    }

    svg path {
        height: 18px;
        width: 18px;
        fill: rgba(0, 0, 0, 0.56);
    }
`

export const ButtonWrapper = styled.div`
    display: block;

`

