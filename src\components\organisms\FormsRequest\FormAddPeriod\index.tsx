/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { Dispatch, SetStateAction, useEffect, useState } from 'react'
import Input from 'components/atoms/Input'
import InputDay from 'components/molecules/InputDay'
// import {handleFieldsChange} from 'src/utils/form-utils';
// import {useNewPeriodContext} from 'src/context/CalendarContext/useNewPeriod';
import Button from 'components/atoms/Button'

import { v4 } from 'uuid'
import * as S from './styles'

export type typefieldsNewPeriod = {
    id: string
    descricao: string
    diaInicio: number
    diaFim: number
}

type FormAddPeriodProps = {
    visible: (value: boolean) => void
    onCreatPeriod: (data: typefieldsNewPeriod) => void
    contador: number
}

export const FormAddPeriod = ({ onCreatPeriod, visible, contador }: FormAddPeriodProps) => {
    const [disabledButton, setDisabledButton] = useState<boolean>(true)
    const [fieldsNewPeriod, setFieldsNewPeriod] = useState<typefieldsNewPeriod>()
    const [dataInicio, setDataInicio] = useState<Date>()
    const [dataFim, setDataFim] = useState<Date>()

    function handleCancel() {
        visible(false)
    }

    function handleAdd() {
        onCreatPeriod(fieldsNewPeriod)
        visible(false)
    }

    React.useEffect(() => {
        setFieldsNewPeriod((prevState) => ({
            ...prevState,
            descricao: `Periodo ${contador}`,
            id: v4()
        }))
    }, [])

    React.useEffect(() => {
        if ((!dataInicio && !dataFim) || dataInicio?.getDate() >= dataFim?.getDate()) {
            setDisabledButton(true)
            return
        }
        setDisabledButton(false)
    }, [dataInicio, dataFim])

    return (
        <S.TabAddMedicine>
            <S.FieldsMoreMedication>
                <S.Content>
                    <Input
                        disabled
                        isDefault="default"
                        label="Descrição"
                        placeholder=""
                        type="text"
                        initialValue={fieldsNewPeriod?.descricao}
                        value={fieldsNewPeriod?.descricao}
                        maxLength={500}
                    />
                    <InputDay
                        value={dataInicio}
                        label="Dia Inicial"
                        onChange={(date: Date) => {
                            setDataInicio(date)
                            setFieldsNewPeriod({ ...fieldsNewPeriod, diaInicio: date?.getDate() })
                        }}
                        key={Math.random()}
                    />
                    <InputDay
                        value={dataFim}
                        label="Dia Final"
                        onChange={(date: Date) => {
                            setDataFim(date)
                            setFieldsNewPeriod({ ...fieldsNewPeriod, diaFim: date?.getDate() })
                        }}
                        key={Math.random()}
                    />
                </S.Content>
            </S.FieldsMoreMedication>
            <S.buttons>
                <Button className={'button-back'} typeButton={'text'} onClick={() => handleCancel()}>
                    Cancelar
                </Button>
                <Button
                    className={'button-add'}
                    themeButton={'primary'}
                    disabled={disabledButton}
                    onClick={() => handleAdd()}
                    iconLeft={'/faturamento/assets/icons/plus.svg'}
                >
                    Adicionar
                </Button>
            </S.buttons>
        </S.TabAddMedicine>
    )
}
