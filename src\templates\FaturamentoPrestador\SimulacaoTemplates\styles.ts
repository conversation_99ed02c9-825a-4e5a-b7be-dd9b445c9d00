/* eslint-disable @typescript-eslint/no-unused-vars */

import styled, { css } from 'styled-components'

export const Container = styled.div`
    display: flex;

    flex-direction: column;

    gap: 24px;

    max-width: 1400px;

    margin: 0 auto;

    width: 95%;

    padding-bottom: 60px;
`

export const ContainerMenuTabs = styled.div`
    background: #ffffff;

    border-radius: 8px;

    margin: 32px 0px;
`

export const Title = styled.h1`
    ${({ theme }) => css`
        font-style: normal;

        font-weight: ${theme.font.semiBold};

        font-size: 28px;

        line-height: 40px;

        color: ${theme.colors.black[88]};
    `};
`

export const AlertWrapper = styled.div`
    display: flex;

    flex-direction: column;

    gap: 16px;
`

export const DescriptionAlert = styled.p`
    margin-top: 4px;
`

export const Link = styled.a`
    color: blue;

    cursor: pointer;
`

export const SubTitle = styled.span`
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    color: rgba(0, 0, 0, 0.56);
`
