import Button from 'components/atoms/Button'
import DividerSectionCard from 'components/atoms/DividerSectionCard'
import Item from 'components/atoms/Item'
import Layout from 'components/molecules/Layout'
import NoContent from 'components/molecules/NoContent'
import Pagination from 'components/molecules/Pagination'
import { useRouter } from 'next/router'
import { useEffect, useState } from 'react'
import { ReactSVG } from 'react-svg'
import { Regras } from 'src/services/processadorRegrasApi/regras'
import { IGetRegrasProps, IRegraDTO } from 'src/services/processadorRegrasApi/regras/types'
import { IPagination, ISortPage } from 'types/pagination'
import { PaginationHelper } from 'utils/helpers/paginationHelper'
import * as S from './styles'
import { Filter } from 'components/molecules/Filter'
import { InputSearch } from 'components/molecules/InputSearch'
import Filtro from 'components/molecules/Filtro'
import { useToast } from 'src/hooks/toast'
import { parserLoadOption } from 'components/atoms/AsyncPaginateSelect/functions'
import { PrestadorService } from 'src/services/faturamentoOperadoraApi/prestadorServices'
import { regraAssociadaOptions } from '../RegraProcessadorCriarTemplate/mock'
import dynamic from 'next/dynamic'
import { useQuery } from 'react-query'
const AnimatedLoadingLottie = dynamic(() => import('components/atoms/AnimatedLoadingLottie'), { ssr: false })

const RegraExcludenciaTemplate = () => {
    const route = useRouter()
    const { addToast } = useToast()
    const [filtro, setFiltro] = useState<IGetRegrasProps & ISortPage>()
    const [showFilter, setShowFilter] = useState(false)
    const [pagination, setPagination] = useState<IPagination>()
    const [numberPage, setNumberPage] = useState<number>(0)

    const { data: dataRegra, isLoading: loading } = useQuery({
        queryKey: ['regrasList', filtro, numberPage],
        queryFn: () => Regras.get({ ...filtro, page: numberPage, size: 10 }),
        onError: (err: any) =>
            addToast({
                title: err?.data?.message,
                type: 'error',
                duration: 4000
            })
    })

    async function loadPrestador(str: string, prev, { page }) {
        // TODO: VOLTAR PARA O PROXY
        return PrestadorService.getPrestador({ nomeCompletoNomeFantasiaRazaoSocialCpfCnpj: str, size: 10, page }).then(({ data }) => {
            return parserLoadOption(data, 'nomeFantasia', 'id')
        })
    }

    const loadRegras = async (text, prev, { page }) => {
        // TODO: FILTRO DE PESQUISA
        return Regras.getRegrasDisponiveis({ identificadorOuDescricao: text, size: 10, page }).then(({ data }) => {
            return parserLoadOption(data, 'descricao', 'identificador')
        })
    }

    return (
        <Layout isLoggedIn={true} title={'Regras de processador'}>
            <S.ResultHeader>
                <p></p>

                <Button
                    themeButton={'warning'}
                    style={{ width: '18%' }}
                    iconLeft={'/faturamento/assets/icons/plus.svg'}
                    onClick={() => route.push('/parametros/processador-de-regras/nova-regra')}
                >
                    Nova regra
                </Button>
            </S.ResultHeader>
            <DividerSectionCard>
                <Filter.Root>
                    <InputSearch.Root style={{ minWidth: '350px' }}>
                        <InputSearch.Button
                            onClick={(text) => {
                                setFiltro((prev) => ({
                                    ...prev,
                                    descricao: text?.trim(),
                                    size: 10
                                }))
                            }}
                        />
                        <InputSearch.Input placeholder="Busque por uma regra" />
                    </InputSearch.Root>
                    <Filter.Button onClick={() => setShowFilter(!showFilter)} style={{ width: 'fit-content', marginLeft: '8px' }}>
                        Filtro
                    </Filter.Button>
                </Filter.Root>
                {loading ? (
                    <S.ContainerLottie>
                        <AnimatedLoadingLottie />
                    </S.ContainerLottie>
                ) : (
                    <>
                        {dataRegra?.data?.content?.length === 0 ? (
                            <S.Content>
                                <NoContent title={'No momento não existe nenhuma regra.'} />
                            </S.Content>
                        ) : (
                            <>
                                <S.ContentItem>
                                    <Item>
                                        <p>Descrição</p>
                                    </Item>
                                    <Item>
                                        <p>Regra associada</p>
                                    </Item>
                                    <Item>
                                        <p>Situação</p>
                                    </Item>
                                    <Item>
                                        <p style={{ color: 'white' }}>.</p>
                                    </Item>
                                </S.ContentItem>
                                {dataRegra?.data?.content?.map((item, index) => {
                                    return (
                                        <S.ContentItemResult key={index}>
                                            <Item>
                                                <span>{item.descricao}</span>
                                            </Item>
                                            <Item>
                                                <span>{item?.regraAssociada}</span>
                                            </Item>
                                            <Item>
                                                <span style={{ paddingLeft: '18px' }}>{item.situacao ? 'Ativo' : 'Inativo'}</span>
                                            </Item>
                                            <Item>
                                                <S.WrapperIcons>
                                                    <S.Icon
                                                        onClick={() => route.push(`/parametros/processador-de-regras/alterar-regra/${item.uuid}`)}
                                                    >
                                                        <ReactSVG src="/faturamento/assets/icons/pencil-blue.svg" />
                                                    </S.Icon>
                                                </S.WrapperIcons>
                                            </Item>
                                        </S.ContentItemResult>
                                    )
                                })}

                                <Pagination
                                    totalPage={pagination?.totalPaginas}
                                    totalRegister={pagination?.totalRegistros}
                                    actualPage={pagination?.paginaAtual}
                                    setNumberPage={pagination?.setNumberPage}
                                />
                            </>
                        )}
                    </>
                )}
            </DividerSectionCard>

            <Filtro.Root
                show={showFilter}
                setShow={setShowFilter}
                filter={filtro}
                onSubmitFilter={(e) => {
                    setNumberPage(0)
                    setFiltro({
                        ...e,
                        descricao: filtro?.descricao,
                        page: 0,
                        size: 5
                    })
                }}
            >
                <Filtro.Content.Lateral style={{ gap: '32px' }}>
                    <Filtro.Item.Title>Filtros</Filtro.Item.Title>

                    <Filtro.Item.Select placeholder="Prestador" loadOptions={loadPrestador} field="prestadorId" />
                    <Filtro.Item.Select placeholder="Regra associada" loadOptions={loadRegras} field="regraAssociada" />
                    <Filtro.Item.SubTitle>Situação</Filtro.Item.SubTitle>
                    <Filtro.Item.CheckBox field="situacao" placeholder="Ativo" />
                    <Filtro.Item.Footer />
                </Filtro.Content.Lateral>
            </Filtro.Root>
        </Layout>
    )
}

export default RegraExcludenciaTemplate
