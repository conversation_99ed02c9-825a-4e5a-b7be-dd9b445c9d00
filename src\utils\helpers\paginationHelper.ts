import { IPageResult, IPagination } from 'types/common/pagination'

export class PaginationHelper {
    static parserPagination<T>(page: IPageResult<T>, setNumberPageCallback: any): IPagination {
        return {
            totalRegistros: page.totalElements,
            totalPaginas: page.totalPages,
            linhasPorPagina: page.size,
            paginaAtual: page.pageable.pageNumber,
            setNumberPage: setNumberPageCallback
        }
    }
}
