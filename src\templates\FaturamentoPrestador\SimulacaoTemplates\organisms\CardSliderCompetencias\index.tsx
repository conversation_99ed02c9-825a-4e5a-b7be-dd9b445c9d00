/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-explicit-any */
import * as S from './styles'
import React, { useCallback, useEffect, useState } from 'react'
import { SituacaoPrestadorAnalise } from 'types/common/enums'
import { DateUtils } from 'utils/dateUtils'
import { capitalize } from 'utils/stringUtils'
import { Slider } from 'components/molecules/Slider'
import Select, { Options } from 'components/molecules/Select'
import { Competencia } from 'types/common/competencia'

type CardSliderCompetenciasProps = {
    data: { month: string; status: string }[]
    dateFilter: Options[]
    valueSelect?: Options
    setCompetenciaSelecionada: (value: Competencia) => void
    competenciaSelecionada: Competencia
    hasDefaultValueInSelect?: boolean
    onChangeCompetencia?: (mesSelecionado: string) => void
    onChangeAnoExercicio?: (exercicio: number) => void
    loading?: boolean
}

type CardSlideTypes = {
    competencia?: string
    month?: string
    status?: string
}

const CardSliderCompetencias = ({
    data = [],
    dateFilter,
    valueSelect,
    setCompetenciaSelecionada,
    competenciaSelecionada,
    onChangeCompetencia,
    onChangeAnoExercicio,
    hasDefaultValueInSelect = false,
    loading = false
}: CardSliderCompetenciasProps) => {
    const [itemSelecionado, setItemSelecionado] = useState<string>()
    const [initialValue, setInitialValue] = useState<Options>()

    const handleSelecionarCompetencia = useCallback(
        (item: CardSlideTypes) => {
            const competenciaSelecionada = {
                competencia: item?.competencia,
                situacao: item?.status
            }
            setItemSelecionado(item?.month)

            if (competenciaSelecionada) setCompetenciaSelecionada({ ...item, situacao: item?.status })
        },
        [setCompetenciaSelecionada]
    )

    const onChangeYear = useCallback(
        (option) => {
            onChangeAnoExercicio(option?.label)
        },
        [onChangeAnoExercicio]
    )

    useEffect(() => {
        if (!competenciaSelecionada) return
        setItemSelecionado(DateUtils.getMonthName(competenciaSelecionada?.competencia))
    }, [competenciaSelecionada])

    useEffect(() => {
        setInitialValue(dateFilter[dateFilter?.length - 1])
    }, [dateFilter])

    return (
        <S.Wrapper>
            <Select
                async={false}
                className="select"
                options={dateFilter}
                label=""
                value={valueSelect}
                onChange={onChangeYear}
                defaultValue={hasDefaultValueInSelect ? [initialValue] : undefined}
                key={hasDefaultValueInSelect ? initialValue?.value : undefined}
            />

            {loading && (
                <S.Spinner>
                    <div className="loader" />
                </S.Spinner>
            )}
            {data?.length > 0 && (
                <Slider initialActiveIndex={Number(competenciaSelecionada?.competencia?.split('-')[1]) - 1}>
                    {data?.map((content: CardSlideTypes, index: number) => (
                        <S.StatusBox
                            style={{ cursor: 'pointer' }}
                            key={index}
                            onClick={() => handleSelecionarCompetencia(content)}
                            className={content?.month === itemSelecionado ? 'status-blue-box' : ''}
                        >
                            <h3>{content?.month}</h3>
                            {/* <span className={content.status === SituacaoPrestadorAnalise.PAGO ? 'green' : ''}>{content.status !== SituacaoPrestadorAnalise.EMPTY ? capitalize(content.status) : ''}</span> */}
                        </S.StatusBox>
                    ))}
                </Slider>
            )}
        </S.Wrapper>
    )
}

export default CardSliderCompetencias
