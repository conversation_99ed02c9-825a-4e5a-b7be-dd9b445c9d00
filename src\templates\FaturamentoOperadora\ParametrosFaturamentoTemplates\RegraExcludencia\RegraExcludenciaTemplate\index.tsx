import React, { useState } from 'react'
import { useRouter } from 'next/router'
import Item from 'components/atoms/Item'
import Switch from 'components/atoms/Switch'
import Button from 'components/atoms/Button'
import Layout from 'components/molecules/Layout'
import NoContent from 'components/molecules/NoContent'
import { historyData } from 'src/utils/Mocks/parameters'
import SearchBar from 'components/molecules/SearchBar'
import HistoryContent from 'components/molecules/HistoryContent'
import DividerSectionCard from 'components/atoms/DividerSectionCard'
import { useParameterContext } from 'src/context/ParametersContext/useParameter'
import * as S from './styles'
import { DateUtils } from 'utils/dateUtils'

const RegraExcludenciaTemplate = () => {
    const router = useRouter()
    const [value, setValue] = useState(true)
    const [parameterForm] = useParameterContext()

    return (
        <Layout isLoggedIn={true} title={'Regras por excludências'}>
            {parameterForm === null ? (
                <S.Content>
                    <NoContent
                        title={'No momento não existe nenhuma regra.'}
                        textButton={'Nova regra'}
                        path="/parametros/regras-de-excludencia/nova-regra"
                    />
                </S.Content>
            ) : (
                <>
                    <DividerSectionCard>
                        <S.ResultHeader>
                            <SearchBar placeholder={'Procurar'} />
                            <Button
                                themeButton={'warning'}
                                style={{ width: '18%' }}
                                iconLeft={'/faturamento/assets/icons/plus.svg'}
                                onClick={() => router.push('/parametros/regras-de-excludencia/nova-regra')}
                            >
                                Nova regra
                            </Button>
                        </S.ResultHeader>
                        <S.ContentItem>
                            <Item>
                                <p>Nome da Regra</p>
                            </Item>
                            <Item>
                                <p>Data Vigência</p>
                            </Item>
                            <Item>
                                <p>Validação</p>
                            </Item>
                            <Item>
                                <p>Ativo</p>
                            </Item>
                            <Item>
                                <p style={{ color: 'white' }}>.</p>
                            </Item>
                        </S.ContentItem>
                        <S.ContentItemResult>
                            <Item>
                                <span onClick={() => router.push('/parametros/regras-de-excludencia/detalhes')}>{parameterForm?.ruleName}</span>
                            </Item>
                            <Item>
                                <span>{DateUtils.formatDateDataPicker(parameterForm?.effectiveDate?.toString(), 'dd/mm/yyyy')}</span>
                            </Item>
                            <Item>
                                <span>{parameterForm?.firstVerification}</span>
                            </Item>
                            <Item>
                                <span>
                                    <Switch isOn={value} handleToggle={() => setValue(!value)} />
                                </span>
                            </Item>
                            <Item>
                                <img src="/faturamento/assets/icons/trash.svg" width={25} height={25} style={{ marginLeft: 'auto' }} />
                            </Item>
                        </S.ContentItemResult>
                    </DividerSectionCard>
                    <HistoryContent data={historyData} title={'Histórico'} />
                </>
            )}
        </Layout>
    )
}

export default RegraExcludenciaTemplate
