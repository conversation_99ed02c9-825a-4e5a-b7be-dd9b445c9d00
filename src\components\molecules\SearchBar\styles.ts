import styled, { css } from 'styled-components'

type WrapperProps = {
    isFocus: boolean
}

export const Wrapper = styled.div<WrapperProps>`
    ${({ theme, isFocus }) => css`
        width: min(40rem, 95%);
        position: relative;
        background-color: #fff;
        padding: 0.8rem 1.6rem;

        display: flex;
        flex-direction: row !important;
        align-items: center;

        /* background: transparent; */
        border: 1px solid rgba(0, 0, 0, 0.16);
        border-radius: 40px;

        transition: box-shadow 0.2s ease-in-out;

        :focus-within {
            border-color: transparent;
            box-shadow: 0 0 0 2px ${theme.colors.primary.default};
        }
    `}
`

export const WrapperLeft = styled.div`
    width: 100%;
    height: 100%;
    background-color: #fff;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
`

export const IconRight = styled.div`
    ${({ theme }) => css`
        padding: 8px;
        width: 32px;
        height: 32px;
        margin-left: 16px;
        border-radius: 50%;
        background: ${theme.colors.secondary[500]};

        cursor: pointer;

        transition: filter 0.2s;

        display: flex;
        justify-content: center;
        align-items: center;

        :hover {
            filter: brightness(0.9);
        }

        svg {
            width: 16px;
            height: 16px;

            path {
                fill: ${theme.colors.black['88']};
            }
        }
    `}
`

export const CloseButton = styled.div`
    ${({ theme }) => css`
        cursor: pointer;

        span {
            line-height: 0;
            display: block;
        }
        svg {
            width: 16px;
            height: 16px;
            path {
                fill: ${theme.colors.black['56']};
            }
        }
    `}
`

export const Input = styled.input`
    ${({ theme }) => css`
        position: relative;
        border: none;
        /* background: transparent; */
        transition: all 0.2s ease;

        padding: 5px 75px 5px 0;

        min-height: 2.4rem;
        height: 100%;
        width: 100%;

        outline: none;

        font-size: 1.6rem;
        line-height: 2.4rem;

        &[type='number']::-webkit-inner-spin-button {
            -webkit-appearance: none;
        }
        &[type='number'] {
            -moz-appearance: textfield;
            appearance: textfield;
        }

        &::placeholder {
            font-size: 1.5rem;
            line-height: 2.4rem;
            color: ${theme.colors.black[40]};
        }
    `}
`
