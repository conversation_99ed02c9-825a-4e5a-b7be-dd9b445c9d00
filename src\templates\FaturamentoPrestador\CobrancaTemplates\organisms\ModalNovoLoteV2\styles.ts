import styled from 'styled-components'
import theme from 'styles/theme'

export const ModalHeader = styled.div`
    h1 {
        font-size: 2.4rem;
        line-height: 3.2rem;
        font-weight: 600;
        color: ${theme.colors.black['88']};
    }
`

export const ContentModal = styled.div`
    display: flex;
    flex-direction: column;
    margin: 1.6rem 0 0;

    p {
        font-size: 1.4rem;
        line-height: 2rem;
        font-weight: 400;
        color: ${theme.colors.black['56']};

        &.description {
            margin-bottom: 2.4rem;
        }
    }
`
export const TipoArquivoLoteList = styled.div`
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
`

export const TipoArquivoLoteOption = styled.div`
    width: 100%;
    padding: 1.2rem 1.6rem;
    border: 1px solid rgba(0, 0, 0, 0.16);
    border-radius: 8px;

    display: flex;
    align-items: center;
    gap: 2.4rem;

    cursor: pointer;

    img {
        width: 8.4rem;
        height: 5.6rem;
        object-fit: cover;
    }

    span {
        font-weight: 600;
        font-size: 1.6rem;
        line-height: 2.4rem;
        color: ${theme.colors.black[56]};
    }

    transition: background-color 0.2s;

    :hover {
        background-color: rgba(0, 0, 0, 0.04);
    }
`
export const ButtonWrapper = styled.div`
    margin: 24px 0;
    display: flex;
    gap: 8px;
    align-self: flex-end;
`
export const ContainerNF = styled.div`
    display: flex;
    flex-direction: column;
    margin-bottom: 24px;
    gap: 24px;
`
export const ErrorsWrapper = styled.div`
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
    margin-bottom: 1.6rem;
`

export const TitleErrors = styled.p`
    font-weight: bold;
    color: rgba(219, 60, 49, 1);
`

export const PresentationErrors = styled.code`
    max-height: 90px;
    height: 90px;
    background-color: rgba(0, 0, 0, 0.1);
    overflow-x: hidden;
    padding: 6px;
`
