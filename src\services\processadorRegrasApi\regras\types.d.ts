import { regraAssociadaInasEnum, regraAssociadaPVEnum } from './enuns'

export interface IGetRegrasProps {
    regraAssociada?: regraAssociadaPVEnum | regraAssociadaInasEnum
    prestadorId?: string
    situacao?: boolean
    descricao?: string
}

export interface IRegraDTO {
    descricao: string
    id: number
    regraAssociada: regraAssociadaPVEnum | regraAssociadaInasEnum
    situacao: boolean
    uuid: string
}

export interface IRegraForm {
    descricao: string
    prestadoresDispensados?: PrestadoresDispensado[]
    regraAssociada: regraAssociadaPVEnum | regraAssociadaInasEnum
    situacao?: boolean
}

export interface IExclusaoRegraPrestadorForm {
    prestadorId?: string
    nomeFantasia: string
    cnpj: string
}

export interface IExclusaoRegraPrestadorDTO {
    cnpj: string
    dataCriacao: string
    id: number
    nomeFantasia: string
    prestadorId: string
    userId: string
    uuid: string
}

export interface IRegrasImplementadasDTO {
    descricao: string
    identificador: regraAssociadaInasEnum
}
