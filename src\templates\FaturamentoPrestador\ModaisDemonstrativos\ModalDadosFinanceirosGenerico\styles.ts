import styled from 'styled-components'

export const ModalContainer = styled.div`
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 40px;
`

export const TitleModal = styled.h3`
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 400;
    font-size: 24px;
    line-height: 32px;
    color: rgba(0, 0, 0, 0.88);
`

export const ListCard = styled.div`
    display: flex;
    width: 100%;
    flex-direction: column;
    gap: 8px;
`

export const Card = styled.div`
    height: 52px;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 8px 16px;
    justify-content: space-between;
    background: rgba(43, 69, 212, 0.04);
    border-radius: 8px;

    p {
        font-family: 'Open Sans';
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
        color: rgba(0, 0, 0, 0.56);
    }

    span {
        font-family: 'Open Sans';
        font-style: normal;
        font-weight: 600;
        font-size: 16px;
        line-height: 16px;
        text-align: right;
        color: rgba(0, 0, 0, 0.88);
    }
`

export const CardDestaque = styled.div`
    height: 52px;
    margin-top: 8px;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 8px 16px;
    justify-content: space-between;
    background: #e8ebfd;
    border-radius: 8px;

    p {
        font-family: 'Open Sans';
        font-style: normal;
        font-weight: 600;
        font-size: 12px;
        line-height: 16px;
        color: rgba(0, 0, 0, 0.88);
    }

    span {
        font-family: 'Open Sans';
        font-style: normal;
        font-weight: 700;
        font-size: 16px;
        line-height: 16px;
        text-align: right;
        color: rgba(0, 0, 0, 0.88);
    }
`

export const CardAction = styled.div`
    margin-left: auto;

    button {
        width: 100px;
    }
`

export const InfoContainer = styled.div`
    display: flex;
    flex-direction: column;
    gap: 16px;
`

export const InfoText = styled.p`
    font-family: 'Open Sans';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: rgba(0, 0, 0, 0.72);
    margin: 0;
`
