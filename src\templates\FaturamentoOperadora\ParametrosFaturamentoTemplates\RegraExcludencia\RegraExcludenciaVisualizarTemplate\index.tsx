import React from 'react'
import { useRouter } from 'next/router'
import Button from 'components/atoms/Button'
import ReturnHeader from 'components/molecules/ReturnHeader'
import TitleSection from 'components/atoms/TitleSection'
import FooterButtons from 'components/atoms/FooterButtons'
import DividerSectionCard from 'components/atoms/DividerSectionCard'
import { useParameterContext } from 'src/context/ParametersContext/useParameter'
import { useNewProcedureContext } from 'src/context/ParametersContext/useNewProcedure'
import * as S from './styles'
import { DateUtils } from 'utils/dateUtils'

const RegraExcludenciaVisualizarTemplate = () => {
    const router = useRouter()
    const [parameterForm] = useParameterContext()
    const [fieldsNewProcedure] = useNewProcedureContext()

    return (
        <>
            <ReturnHeader title={parameterForm?.ruleName} link="/parametros/regras-de-excludencia" />
            <>
                <DividerSectionCard>
                    <S.ContentItem>
                        <S.Item>
                            <p>Nome da regra</p>
                            <span>{parameterForm?.ruleName}</span>
                        </S.Item>
                        <S.Item>
                            <p>Data de vigência</p>
                            <span>{DateUtils.formatDateDataPicker(parameterForm?.effectiveDate?.toString(), 'dd/mm/yyyy')}</span>
                        </S.Item>
                        <S.Item>
                            <p>Local de aplicação</p>
                            <span>{parameterForm?.localApplication}</span>
                        </S.Item>
                    </S.ContentItem>
                    <S.ContentItem>
                        <S.Item>
                            <p>Primeira verificação</p>
                            <span>{parameterForm?.firstVerification}</span>
                        </S.Item>
                    </S.ContentItem>
                    <S.ContentItem>
                        <S.Item>
                            <p>Segunda verificação</p>
                            <span>{parameterForm?.secondVerification}</span>
                        </S.Item>
                    </S.ContentItem>
                    <S.ContentItem>
                        <S.Item style={{ flex: 'none', flexBasis: '31%', marginBottom: '40px' }}>
                            <p>Prestador</p>
                            <span>{parameterForm?.provider}</span>
                        </S.Item>
                    </S.ContentItem>

                    <TitleSection>Procedimentos</TitleSection>
                    <S.ContentItemProcedure>
                        <S.ItemResult>
                            <p>Procedimento Principal</p>
                        </S.ItemResult>
                        <S.ItemResult>
                            <p>Procedimentos Excludentes</p>
                        </S.ItemResult>
                    </S.ContentItemProcedure>
                    <S.ContentItemResultProcedure>
                        <S.ItemResult>
                            <span>{fieldsNewProcedure.mainProcedure}</span>
                        </S.ItemResult>
                        <S.ItemResult>
                            <span>{fieldsNewProcedure.secondaryProcedure}</span>
                        </S.ItemResult>
                    </S.ContentItemResultProcedure>
                </DividerSectionCard>
            </>
            <FooterButtons>
                <Button
                    typeButton={'ghost'}
                    themeButton={'primary'}
                    style={{ width: '18%', marginRight: '15px' }}
                    iconLeft={'/faturamento/assets/icons/pencil-blue.svg'}
                    onClick={() => router.push('/parametros/regras-de-excludencia/alterar-regra')}
                >
                    Editar regra
                </Button>
                <Button
                    themeButton={'warning'}
                    style={{ width: '18%' }}
                    iconLeft={'/faturamento/assets/icons/export.svg'}
                    onClick={() => alert('Xls exportado')}
                >
                    Exportar xls
                </Button>
            </FooterButtons>
        </>
    )
}

export default RegraExcludenciaVisualizarTemplate
