/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react'
import * as S from './styles'
export type CardFilter = {
    name: string
    situacao: string
}

interface props {
    cardsFilter: CardFilter[]
    filterSelected: string
    setFilterSelected: (index: string) => void
}
const CardsFilter = ({ cardsFilter, filterSelected, setFilterSelected }: props) => {
    return (
        <S.Filter>
            {cardsFilter?.map((itens: CardFilter, index: number) => (
                <S.FilterBox
                    key={index}
                    onClick={() => {
                        setFilterSelected(itens.situacao)
                    }}
                    className={itens.situacao === filterSelected ? 'actived' : ''}
                >
                    <div>
                        <h3>{itens.name}</h3>
                    </div>
                </S.FilterBox>
            ))}
        </S.Filter>
    )
}

export default CardsFilter
