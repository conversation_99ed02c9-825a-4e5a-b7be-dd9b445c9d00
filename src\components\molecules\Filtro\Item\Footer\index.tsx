import React from 'react'

import { Wrapper, Text, Buttons } from './styles'
import Button from 'components/atoms/Button'
import { useFiltroContext } from '../../Root'

interface IFoot {
    textFooter?: string
    clearButtonText?: string
    submitButonText?: string
}

const Footer = ({
    textFooter = 'Os filtros só serão aplicados em tela quando a ação for concluída por meio do botão abaixo',
    clearButtonText = 'Limpar filtros',
    submitButonText = 'Aplicar filtros'
}: IFoot) => {
    const { onSubmit, onClear, filter } = useFiltroContext()

    return (
        <Wrapper>
            <Text>{textFooter}</Text>

            <Buttons>
                <Button onClick={onClear} typeButton="text">
                    {clearButtonText}
                </Button>

                <Button onClick={onSubmit} disabled={!filter} themeButton="secondary">
                    {submitButonText}
                </Button>
            </Buttons>
        </Wrapper>
    )
}

export default Footer
