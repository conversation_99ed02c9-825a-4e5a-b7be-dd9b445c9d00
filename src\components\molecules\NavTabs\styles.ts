import styled, { css } from 'styled-components'

type ContentProps = {
    variant?: 'type_1' | 'type_2' | 'type_3'
}
export const Content = styled.div<ContentProps>`
    ${({ theme, variant }) => css`
        display: flex;
        /* width: 80%; */
        align-items: center;
        overflow-x: auto;
        border-bottom: 1px solid ${theme.colors.black['16']};

        ${variant === 'type_3' &&
        css`
            border-bottom: none;
        `}

        @media screen and (max-width: 1024px) {
            overflow-x: scroll;
        }
    `}
`
export const GuidePanel = styled.div``

type TabsProps = {
    actived?: boolean
    type?: 'default' | 'compact'
    variant?: 'type_1' | 'type_2' | 'type_3'
}

export const Tabs = styled.div<TabsProps>`
    ${({ actived, theme, type, variant }) => css`
        display: flex;
        flex-direction: row;
        background-color: ${actived ? (variant === 'type_2' ? theme?.colors.white[100] : '') : variant === 'type_2' ? '#F2F2F5' : ''};
        color: ${actived ? (variant === 'type_2' ? theme.colors.black[88] : theme.colors.primary[500]) : theme.colors.black['40']};
        border-bottom: ${actived && variant === 'type_1'
            ? `${{ compact: '2px' }[type] || '4px'} solid ${theme.colors.primary.default}`
            : `${{ compact: '2px' }[type] || '4px'} solid transparent`};

        width: auto;
        border-radius: 8px 8px 0px 0px;
        min-width: ${variant === 'type_2' ? '192px' : ''};
        padding: ${{
            compact: '6px'
        }[type] || variant === 'type_2'
            ? '8px'
            : '16px'};
        margin-right: ${variant === 'type_2' ? '' : '5px'};
        font-size: ${{
            compact: '14px'
        }[type] || '16px'};
        cursor: pointer;
        user-select: none;

        align-items: center;
        > .icon {
            padding-right: 5px;
        }

        svg path {
            fill: ${actived ? (variant === 'type_2' ? theme.colors.black[88] : theme.colors.primary[500]) : theme.colors.black['56']};
            fill-opacity: 0.88;
        }

        span {
            margin-right: 3px;
            position: relative;
            display: inline-block;
            top: 2px;
        }

        p {
            font-weight: 600;
            white-space: nowrap;
        }

        ${variant === 'type_3' &&
        css`
            margin: 0;
            margin-right: 0.4rem;
            padding: 0.8rem;
            padding-bottom: 1.6rem;
            border-bottom: ${actived ? `2px solid #0044CC` : `2px solid transparent`};

            p {
                font-size: 1.6rem;
                line-height: 2.4rem;
                font-weight: 400;
                white-space: nowrap;
                color: #3e4e65;
            }
        `}

        actived {
            border-bottom: 2px solid ${theme.colors.pink.primary.default};
            a {
                color: ${theme.colors.primary[500]};
            }
        }

        @media screen and (max-width: 1024px) {
            min-width: 180px;
        }
    `}
`

export const QtdBox = styled.div`
    margin-left: 10px;
    background: #f7f8fd;
    color: #2b45d4;
    padding: 5px 15px;
    border-radius: 4px;
`

type CounterBadgeProps = {
    actived?: boolean
}
export const CounterBadge = styled.div<CounterBadgeProps>`
    width: fit-content;
    height: 2.4rem;
    padding: 0.4rem 0.8rem;
    margin-left: 0.8rem;
    border-radius: 100px;
    background-color: rgba(0, 80, 229, 0.08);

    font-size: 1.2rem;
    line-height: 1.6rem;
    font-weight: 400;
    color: #3e4e65;

    ${({ actived }) =>
        actived &&
        css`
            background-color: #940a00;
            color: #ffffff;
        `}
`
