// import { useRouter } from 'next/router';
import React, { useCallback, useEffect, useState } from 'react'
import * as S from './styles'
import DropLote from 'components/molecules/DropLote'
import InfoWithIconDropLote from 'components/atoms/InfoWithIconDropLote'
import { ILoteDevolucaoQuery, IPageLoteDevolucao } from 'types/cobrancaPrestador/visaoDevolucao'
import { DateUtils } from 'utils/dateUtils'
import { CobrancaServices, IGetCobrancaProps, situacaoLoteEnum } from 'src/services/faturamentoPrestadorApi/cobrancaServices'
import { PaginationHelper } from 'utils/helpers/paginationHelper'
import { IPagination } from 'types/common/pagination'
import Pagination from 'components/molecules/Pagination'
import { IPrestadorAnaliseQuery } from 'types/cobrancaPrestador/prestadorAnalise'
import NoContent from 'components/molecules/NoContent'
import { useAuth } from 'src/hooks/auth'
import DropLoteCard from '../DropLoteCards'
import { ReactSVG } from 'react-svg'
import { capitalize } from 'utils/stringUtils'
import { useToast } from 'src/hooks/toast'
import moment from 'moment'
import { NumberUtils } from 'utils/numberUtils'
import { useRouter } from 'next/router'
import { ILoteCobrancaDTO } from 'types/cobrancaPrestador/loteCobranca'

type ListaLotesDevolvidosProps = {
    competenciaSelecionada: IPrestadorAnaliseQuery
    searchLote: string
    forceUpdate: boolean
}

const ListaLotesDevolvidos = ({ competenciaSelecionada, searchLote, forceUpdate }: ListaLotesDevolvidosProps) => {
    const { prestadorVinculado } = useAuth()
    const { addToast } = useToast()
    const route = useRouter()

    const [pageLotes, setPageLotes] = useState<IPageLoteDevolucao>()
    const [numberPage, setNumberPage] = useState<number>(0)
    const [pagination, setPagination] = useState<IPagination>()

    const labels: string[] = ['Lote', 'Valor apresentado', 'Valor glosado', 'Valor apurado', 'Envio']

    function createDropLot(item: ILoteCobrancaDTO) {
        return [
            {
                component: (
                    <div>
                        <p>{item?.numeroLote}</p>
                    </div>
                )
            },
            {
                component: (
                    <div>
                        <p>{NumberUtils.maskMoney(item?.valorApresentado)}</p>
                    </div>
                )
            },
            {
                component: (
                    <div>
                        <p>valor_glosado</p>
                    </div>
                )
            },
            {
                component: (
                    <div>
                        <p>valor_apurado</p>
                    </div>
                )
            },
            {
                component: (
                    <div>
                        <p>{item?.tipoEnvio === 'ELETRONICO' ? 'Eletrônico' : capitalize(item?.tipoEnvio)}</p>
                    </div>
                )
            }
        ]
    }

    const carregarLotes = useCallback(
        (filter?: string, page?: number) => {
            const getProps: IGetCobrancaProps =
                filter === ''
                    ? {
                          size: 10,
                          page: page || 0
                      }
                    : {
                          size: 10,
                          page: page || 0,
                          filtroNumeroLote: filter
                      }

            if (competenciaSelecionada?.competencia && prestadorVinculado?.uuid) {
                //TODO: ALTERAR ENDPOINT situacaoLoteEnum.FECHADO
                CobrancaServices.getLotes(competenciaSelecionada?.competencia, prestadorVinculado?.uuid, 'DEVOLVIDO', getProps).then(({ data }) => {
                    // if (data.totalElements > 0) {
                    setPageLotes(data)
                    // }
                    // initLotesChecks(data.content)

                    const objectPagination = PaginationHelper.parserPagination<ILoteCobrancaDTO>(data, setNumberPage)
                    setPagination(objectPagination)
                })
            }
        },
        [competenciaSelecionada, prestadorVinculado]
    )

    const handleDownloadRelatorioErrosPdf = (idLote) => {
        CobrancaServices.getRelatorioErrosPdf(idLote).then((response) => {
            console.log(response.headers)
            const fileName = response.headers['content-disposition'].split('filename=')[1] ?? 'download.pdf'
            const url = window.URL.createObjectURL(response.data)
            const link = document.createElement('a')
            link.href = url
            link.setAttribute('download', fileName) //or any other extension
            document.body.appendChild(link)
            link.click()
        })
    }

    const handleDownloadXML = (idLote: string) => {
        CobrancaServices.getProtocoloRecebimentoXml(idLote)
            .then((response) => {
                const fileName = response.headers['content-disposition'].split('filename=')[1] ?? 'download.xml'
                const url = window.URL.createObjectURL(new Blob([response.data]))
                const link = document.createElement('a')
                link.href = url
                link.setAttribute('download', fileName) //or any other extension
                document.body.appendChild(link)
                link.click()
            })
            .catch(() => {
                addToast({ title: 'Erro ao fazer download', type: 'error', duration: 3000 })
            })
    }
    const handleDownloadPDF = (idLote: string) => {
        CobrancaServices.getProtocoloRecebimentoPdf(idLote)
            .then((response) => {
                const fileName = response.headers['content-disposition'].split('filename=')[1] ?? 'download.pdf'
                const url = window.URL.createObjectURL(response.data)
                const link = document.createElement('a')
                link.href = url
                link.setAttribute('download', fileName) //or any other extension
                document.body.appendChild(link)
                link.click()
            })
            .catch(() => {
                addToast({ title: 'Erro ao fazer download', type: 'error', duration: 3000 })
            })
    }

    useEffect(() => {
        if (forceUpdate === false) {
            carregarLotes()
        }
    }, [forceUpdate])

    useEffect(() => {
        carregarLotes(searchLote, numberPage)
    }, [numberPage, competenciaSelecionada])

    return (
        <S.Wrapper>
            {!pageLotes || pageLotes?.content.length === 0 ? (
                <NoContent title="No momento não existe nenhum lote devolvido" path="/parametros/regras-de-excludencia/nova-regra" />
            ) : (
                <>
                    <S.HeaderLabel>
                        {labels.map((item, index) => (
                            <div key={index}>
                                <p>{item}</p>
                            </div>
                        ))}
                    </S.HeaderLabel>
                    {pageLotes?.content.map((lote, index) => {
                        return (
                            <DropLote items={createDropLot(lote)} key={index}>
                                <S.ContentDropLot>
                                    <div className="row1">
                                        <DropLoteCard
                                            iconSrc="/faturamento/assets/icons/blueHuman.svg"
                                            title="Lote enviado por"
                                            value="Leslie Alexander"
                                        />
                                        <DropLoteCard
                                            iconSrc="/faturamento/assets/icons/calender.svg"
                                            title="Data de envio"
                                            value={moment(lote?.dataEnvio).format('DD/MM/YYYY [-] HH:mm')}
                                        />
                                        <DropLoteCard
                                            iconSrc="/faturamento/assets/icons/calender.svg"
                                            title="Data de processamento"
                                            value="08/12/2022 - 00:00"
                                        />
                                        <DropLoteCard
                                            iconSrc="/faturamento/assets/icons/calender.svg"
                                            title="Período para recursar"
                                            value="Aguardando demonstrativo"
                                        />
                                    </div>
                                    <div className="row1_1">
                                        <DropLoteCard
                                            iconSrc="/faturamento/assets/icons/calender.svg"
                                            title="Data de envio do recurso"
                                            value="08/12/2022 - 00:00"
                                        />
                                        <DropLoteCard
                                            blueValue
                                            iconSrc="/faturamento/assets/icons/listMany.svg"
                                            title="Lote de recurso de glosa"
                                            value="5001"
                                        />
                                    </div>
                                    <div className="row2">
                                        <S.ProtocolWrapper>
                                            <p>Protocolo de recebimento</p>
                                            <S.FileWrapper>
                                                {lote?.tipoEnvio !== 'MANUAL' && (
                                                    <S.FileCard>
                                                        <ReactSVG src="/faturamento/assets/icons/download_blue.svg" />
                                                        <p onClick={() => handleDownloadXML(lote?.loteCobrancaId)}>Baixar XML</p>
                                                    </S.FileCard>
                                                )}

                                                <S.FileCard>
                                                    <ReactSVG src="/faturamento/assets/icons/download_blue.svg" />
                                                    <p onClick={() => handleDownloadPDF(lote?.loteCobrancaId)}>Baixar PDF</p>
                                                </S.FileCard>
                                            </S.FileWrapper>
                                        </S.ProtocolWrapper>
                                        <S.ResourceWrapper onClick={() => route.push(`/prestador/medico/cobranca/lotes/${lote?.loteCobrancaId}`)}>
                                            <ReactSVG src="/faturamento/assets/icons/glosa_icon.svg" />
                                            <p>Recursar glosa</p>
                                        </S.ResourceWrapper>
                                    </div>
                                </S.ContentDropLot>
                                {/* <S.ContentDropLot>
                                    <InfoWithIconDropLote
                                        icon="/faturamento/assets/icons/calender.svg"
                                        title="Data da devolução"
                                        description={lot.dataDevolucao ? DateUtils.formatDateTimePTBR(lot.dataDevolucao) : '---'}
                                    />
                                    <InfoWithIconDropLote
                                        icon="/faturamento/assets/icons/ic_alert.svg"
                                        title="Motivo da devolução"
                                        description={lot.motivoDevolucao}
                                    />
                                </S.ContentDropLot> */}
                            </DropLote>
                        )
                    })}
                    <div style={{ marginTop: '40px' }}>
                        <Pagination
                            totalPage={pagination?.totalPaginas}
                            totalRegister={pagination?.totalRegistros}
                            actualPage={pagination?.paginaAtual}
                            setNumberPage={(page) => setNumberPage(page)}
                        />
                    </div>
                </>
            )}
        </S.Wrapper>
    )
}

export default ListaLotesDevolvidos
