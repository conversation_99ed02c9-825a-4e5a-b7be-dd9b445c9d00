import { regraAssociadaPVEnum, regraAssociadaInasEnum } from 'src/services/processadorRegrasApi/regras/enuns'

export const regraAssociadaOptions =
    process.env.NEXT_PUBLIC_CLIENT === 'planserv'
        ? [
              regraAssociadaPVEnum.PLANAUD_0054,
              regraAssociadaPVEnum.PLANAUD_0055,
              regraAssociadaPVEnum.PLANAUD_0062,
              regraAssociadaPVEnum.VAL_GERAL_0001,
              regraAssociadaPVEnum.VAL_GERAL_0002,
              regraAssociadaPVEnum.VAL_GERAL_0003,
              regraAssociadaPVEnum.VAL_GDF_0001,
              regraAssociadaPVEnum.VAL_GDF_0002,
              regraAssociadaPVEnum.PLANVAL_0003,
              regraAssociadaPVEnum.PLANVAL_0004_1,
              regraAssociadaPVEnum.PLANVAL_0004_3,
              regraAssociadaPVEnum.PLANVAL_0005_5,
              regraAssociadaPVEnum.PLANVAL_0015,
              regraAssociadaPVEnum.PLANVAL_0024,
              regraAssociadaPVEnum.PLANVAL_0050_1,
              regraAssociadaPVEnum.PLANVAL_0053,
              regraAssociadaPVEnum.PLANVAL_0054,
              regraAssociadaPVEnum.PLANVAL_0056,
              regraAssociadaPVEnum.PLANVAL_0058,
              regraAssociadaPVEnum.PLANVAL_0064,
              regraAssociadaPVEnum.PLANVAL_0078,
              regraAssociadaPVEnum.PLANVAL_0079,
              regraAssociadaPVEnum.PLANVAL_0081_1,
              regraAssociadaPVEnum.PLANVAL_0086,
              regraAssociadaPVEnum.PLANVAL_0093,
              regraAssociadaPVEnum.PLANVAL_0098_2,
              regraAssociadaPVEnum.PLANVAL_0098_7,
              regraAssociadaPVEnum.PLANVAL_0100,
              regraAssociadaPVEnum.PLANVAL_0100_2,
              regraAssociadaPVEnum.PLANVAL_0100_3,
              regraAssociadaPVEnum.PLANVAL_0101,
              regraAssociadaPVEnum.PLANVAL_0102,
              regraAssociadaPVEnum.PLANVAL_0109,
              regraAssociadaPVEnum.PLANVAL_0109_3_1,
              regraAssociadaPVEnum.PLANVAL_0110_1,
              regraAssociadaPVEnum.PLANVAL_0116,
              regraAssociadaPVEnum.PLANVAL_0117_1,
              regraAssociadaPVEnum.PLANVAL_0121,
              regraAssociadaPVEnum.PLANVAL_0125,
              regraAssociadaPVEnum.PLANVAL_0128
          ]
        : [
              regraAssociadaInasEnum.PLANAUD_0054,
              regraAssociadaInasEnum.PLANAUD_0055,
              regraAssociadaInasEnum.PLANVAL_0003,
              regraAssociadaInasEnum.PLANVAL_0004_1,
              regraAssociadaInasEnum.PLANVAL_0004_3,
              regraAssociadaInasEnum.PLANVAL_0005_5,
              regraAssociadaInasEnum.PLANVAL_0015,
              regraAssociadaInasEnum.PLANVAL_0024,
              regraAssociadaInasEnum.PLANVAL_0053,
              regraAssociadaInasEnum.PLANVAL_0054,
              regraAssociadaInasEnum.PLANVAL_0056,
              regraAssociadaInasEnum.PLANVAL_0058,
              regraAssociadaInasEnum.PLANVAL_0078,
              regraAssociadaInasEnum.PLANVAL_0079,
              regraAssociadaInasEnum.PLANVAL_0081_1,
              regraAssociadaInasEnum.PLANVAL_0086,
              regraAssociadaInasEnum.PLANVAL_0093,
              regraAssociadaInasEnum.PLANVAL_0098_2,
              regraAssociadaInasEnum.PLANVAL_0098_7,
              regraAssociadaInasEnum.PLANVAL_0100,
              regraAssociadaInasEnum.PLANVAL_0100_2,
              regraAssociadaInasEnum.PLANVAL_0100_3,
              regraAssociadaInasEnum.PLANVAL_0101,
              regraAssociadaInasEnum.PLANVAL_0102,
              regraAssociadaInasEnum.PLANVAL_0109,
              regraAssociadaInasEnum.PLANVAL_0109_3_1,
              regraAssociadaInasEnum.PLANVAL_0110_1,
              regraAssociadaInasEnum.PLANVAL_0116,
              regraAssociadaInasEnum.PLANVAL_0117_1,
              regraAssociadaInasEnum.PLANVAL_0125,
              regraAssociadaInasEnum.PLANVAL_0128,
              regraAssociadaInasEnum.VAL_GERAL_0001,
              regraAssociadaInasEnum.VAL_GERAL_0002,
              regraAssociadaInasEnum.VAL_GERAL_0003,
              regraAssociadaInasEnum.VAL_GERAL_0004,
              regraAssociadaInasEnum.VAL_GERAL_0005,
              regraAssociadaInasEnum.VAL_GERAL_0006,
              regraAssociadaInasEnum.VAL_GERAL_0007,
              regraAssociadaInasEnum.VAL_GERAL_0008,
              regraAssociadaInasEnum.VAL_GERAL_0009,
              regraAssociadaInasEnum.VAL_GERAL_0010,
              regraAssociadaInasEnum.VAL_GERAL_0011,
              regraAssociadaInasEnum.VAL_GERAL_0012,
              regraAssociadaInasEnum.VAL_GERAL_0013,
              regraAssociadaInasEnum.VAL_GERAL_0014,
              regraAssociadaInasEnum.VAL_GERAL_0015,
              regraAssociadaInasEnum.VAL_GERAL_0016,
              regraAssociadaInasEnum.VAL_GERAL_0017,
              regraAssociadaInasEnum.VAL_GERAL_0018,
              regraAssociadaInasEnum.VAL_GERAL_0019,
              regraAssociadaInasEnum.VAL_GERAL_0021,
              regraAssociadaInasEnum.VAL_GERAL_0024,
              regraAssociadaInasEnum.VAL_GERAL_0025,
              regraAssociadaInasEnum.VAL_INAS_0001,
              regraAssociadaInasEnum.VAL_INAS_0002,
              regraAssociadaInasEnum.VAL_INAS_0003,
              regraAssociadaInasEnum.VAL_INAS_0004,
              regraAssociadaInasEnum.VAL_INAS_0006,
              regraAssociadaInasEnum.VAL_INAS_0007,
              regraAssociadaInasEnum.VAL_INAS_0008,
              regraAssociadaInasEnum.VAL_INAS_0010
          ]
