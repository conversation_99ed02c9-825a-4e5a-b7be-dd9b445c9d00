/* eslint-disable prettier/prettier */
import styled, { css } from 'styled-components'

export const Filter = styled.div`
    display: flex;
    overflow: auto;
    width: 100%;
`

export const FilterBox = styled.div`
    ${({ theme }) => css`
        width: 100%;
        padding: 8px 16px;
        border-radius: 8px;

        border: 1px solid ${theme.colors.black['16']};

        cursor: pointer;

        & + & {
            margin-left: 16px;
        }

        h3 {
            font-weight: 600;
            font-size: 14px;
            line-height: 24px;
            color: ${theme.colors.black['88']};
            transition: color 0.2s;
        }

        span {
            font-weight: 400;
            font-size: 12px;
            line-height: 16px;
            color: ${theme.colors.black['56']};
            transition: color 0.2s;
        }

        transition: background-color 0.2s;

        &.actived {
            background-color: ${theme.colors.primary['500']};
            border-color: ${theme.colors.primary['500']};

            h3 {
                color: #FFFFFF;
            }

            span {
                color: #FFFFFF;
            }
        }
    `}
`
