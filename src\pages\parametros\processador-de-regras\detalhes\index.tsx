import React from 'react'
import Layout from 'components/molecules/Layout'
import RegraProcessadorVisualizarTemplate from 'src/templates/FaturamentoOperadora/ParametrosFaturamentoTemplates/RegraProcessador/RegraProcessadorVisualizarTemplate'

const Detalhes = () => {
    // return <RegraProcessadorVisualizarTemplate />;
    return <div>teste</div>
}

// Obtendo o Layout para a página
// Detalhes.getLayout = function getLayout(page: JSX.Element) {
// 	return <Layout isLoggedIn={true}>{page}</Layout>;
// };

export default Detalhes
