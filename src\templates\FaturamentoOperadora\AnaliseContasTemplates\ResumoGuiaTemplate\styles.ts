import styled from 'styled-components'
import theme from 'styles/theme'

export const Content = styled.div`
    padding-bottom: 32px;
`

export const Header = styled.div`
    display: flex;
    align-content: center;
    justify-content: space-between;

    p {
        cursor: default;
        font-size: 12px;
        margin-top: 6px;
        font-weight: normal;
        color: ${theme.colors.black[56]};
    }
`

export const SubHeader = styled.div`
    display: flex;
    margin-bottom: 24px;
    flex-direction: row;
    justify-content: space-between;
`

export const SubHeaderTitle = styled.div`
    margin: 0;
    font-size: 28px;
    font-weight: 600;

    display: flex;
    align-items: center;
    gap: 20px;
`

export const SubHeaderSubTitle = styled.div`
    font-size: 16px;
    margin-top: 4px;
    color: #00000075;
`

export const ActionsContainer = styled.div`
    gap: 16px;
    display: flex;
    align-items: center;
`

export const Action = styled.div`
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    line-height: 24px;
    white-space: nowrap;
    color: ${theme.colors.primary[500]};
`

export const ContentData = styled.div`
    gap: 16px;
    width: 100%;
    display: flex;
    padding-top: 8px;
    @media screen and (max-width: 1024px) {
        flex-direction: column;
    }
`

export const HeaderList = styled(Header)`
    padding-top: 24px;
`

export const Page = styled.div`
    display: flex;
    align-items: center;

    span {
        font-size: 1.6rem;
    }

    svg {
        cursor: pointer;
        margin-left: 20px;
    }
`

export const ContentItemResult = styled.div`
    display: grid;
    padding: 24px 16px 0 16px;
    grid-template-columns: repeat(2, 1fr) 2fr 1.5fr;
`

export const ContentItemResultGlosa = styled.div`
    padding: 16px;
    cursor: pointer;
    border-radius: 8px;
    margin-bottom: 4px;
    background: #f6f6f9;

    display: grid;
    align-items: center;
    grid-template-columns: repeat(2, 1fr) 2fr 1.5fr;
`

export const ModalContainer = styled.div``

export const ModalTitle = styled.h2`
    font-weight: 600;
    font-size: 24px;
    line-height: 40px;
    color: rgba(0, 0, 0, 0.88);
    padding-bottom: 20px;
    border-bottom: 1px solid ${theme.colors.black[16]};
`

export const ModalSubtitle = styled.h3`
    font-weight: 400;
    font-size: 16px;
    padding: 24px 0;
    line-height: 24px;
    color: ${theme.colors.black[88]};
`

export const ModalText = styled.div`
    font-size: 12px;
    line-height: 16px;
    color: ${theme.colors.black[56]};
`

export const ContainerButtons = styled.div`
    display: flex;
    margin: 24px 0 10px 0;
    justify-content: flex-end;
`
export const FileCardsContainer = styled.div`
    gap: 16px;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    padding-top: 8px;

    @media screen and (max-width: 1024px) {
        flex-direction: column;
    }
`
