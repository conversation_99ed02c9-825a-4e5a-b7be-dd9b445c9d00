/* eslint-disable prefer-const */
import * as S from './styles'

import Button from 'components/atoms/Button'
import { CardInvoicing } from 'components/atoms/Cards/CardValueInvoicing'
import moment from 'moment'
import { useRouter } from 'next/router'
import { useEffect, useMemo, useState } from 'react'
import { useAuth } from 'src/hooks/auth'
import { useToast } from 'src/hooks/toast'
import { Demonstrativo } from 'src/services/contasAPagarApi/demonstrativo-controller'
import { IGetDadosGerais } from 'src/services/contasAPagarApi/demonstrativo-controller/types'
import { CobrancaServices } from 'src/services/faturamentoPrestadorApi/cobrancaServices'
import { IPrestadorAnaliseQuery } from 'types/cobrancaPrestador/prestadorAnalise'
import { IResumoCobrancaQuery } from 'types/cobrancaPrestador/resumoCobranca'
import { SituacaoPrestadorAnalise, TipoFaturamento } from 'types/common/enums'
import { DateUtils } from 'utils/dateUtils'
import { NumberUtils } from 'utils/numberUtils'
import SectionDadosCobranca from '../SectionDadosCobranca'
import { DemonstrativoPrestador } from 'src/services/faturamentoPrestadorApi/contas-apagar-controller/demonstrativo-controller-v-2'
import {
    IGetListPageableDTO,
    IGetListResponse
} from 'src/services/faturamentoPrestadorApi/contas-apagar-controller/demonstrativo-controller-v-2/types'
import Pagination from 'components/molecules/Pagination'
import { IPage, IPagination } from 'types/pagination'
import { EnumSituacaoColor, EnumSituacaoPrettyName } from 'src/services/faturamentoPrestadorApi/contas-apagar-controller/enuns'
import { Grid } from '@mui/material'
import { useQuery } from 'react-query'

const initialValue = {
    abertura: '-',
    apresentado: '-',
    apurado: '-',
    fechamento: '-',
    glosado: '-',
    limiteEnvio: '-',
    pagamento: '-',
    horarioLimiteEnvioLote: '-'
}

const TabResumoMedico = ({ competenciaSelecionada, exercicio }: { competenciaSelecionada: IPrestadorAnaliseQuery; exercicio: any }) => {
    const { push } = useRouter()
    const { addToast } = useToast()
    const { prestadorVinculado } = useAuth()

    const [resumo, setResumo] = useState<IResumoCobrancaQuery>()
    const [cardDates, setCardDates] = useState<CardInvoicing[]>([])
    const [pagination, setPagination] = useState<IPagination>()
    const [cardValues, setCardValues] = useState<CardInvoicing[]>([])
    const [demonstrativeList, setDemonstrativeList] = useState<IGetListResponse[]>([])
    // const [demonstrativo, setDemonstrativo] = useState<IGetDadosGerais>()
    const [isLoading, setIsLoading] = useState(false)
    const [numberPage, setNumberPage] = useState<number>(0)
    const [isDownloading, setIsDownloading] = useState({
        xml: false,
        xls: false
    })
    const [extratoDisponivel, setExtratoDisponivel] = useState(false)
    const [demonstrativoContasXmlDisponivel, setDemonstrativoContasXMLDisponivel] = useState(false)
    // const [extratosDisponiveis, setExtratosDisponiveis] = useState([])

    const parserPagination = (page: IPage): IPagination => {
        return {
            totalRegistros: page?.totalElements,
            totalPaginas: page?.totalPages,
            linhasPorPagina: page?.size,
            paginaAtual: page?.pageable?.pageNumber,
            setNumberPage: setNumberPage
        }
    }

    const { data: extratosDisponiveis, isLoading: loading } = useQuery({
        queryKey: ['extratosDisponiveis', exercicio, prestadorVinculado, competenciaSelecionada],
        refetchOnWindowFocus: true,
        refetchOnMount: true,
        refetchOnReconnect: true,
        // refetchInterval: 1 * 60 * 1000,
        queryFn: async () => {
            const res = await CobrancaServices.getExtratoDisponivel(exercicio, prestadorVinculado.uuid, TipoFaturamento.NORMAL)

            // if (res.status === 200) {
            //     setExtratosDisponiveis(res.data)
            // }

            return res?.data
        },
        onError: (err: any) =>
            addToast({
                title: err?.data?.message,
                type: 'error',
                duration: 4000
            })
    })

    // useEffect(() => {
    //     async function getExtrato() {
    //         const res = await CobrancaServices.getExtratoDisponivel(exercicio, prestadorVinculado.uuid, TipoFaturamento.NORMAL)
    //         console.log('extratosDisponiveis', res.data)

    //         if (res.status === 200) {
    //             setExtratosDisponiveis(res.data)
    //         }
    //     }

    //     if (exercicio && prestadorVinculado.uuid) {
    //         getExtrato()
    //     }
    // }, [exercicio, prestadorVinculado, competenciaSelecionada])

    useEffect(() => {
        const findExtratoDisponivel = extratosDisponiveis?.find((ext) => ext?.competencia === competenciaSelecionada?.competencia)
        if (findExtratoDisponivel && findExtratoDisponivel?.demonstrativoContasXmlDisponivel) {
            setDemonstrativoContasXMLDisponivel(true)
        } else {
            setDemonstrativoContasXMLDisponivel(false)
        }

        if (findExtratoDisponivel && findExtratoDisponivel?.extratoDisponivel) {
            setExtratoDisponivel(true)
        } else {
            setExtratoDisponivel(false)
        }
    }, [competenciaSelecionada, extratosDisponiveis])

    useEffect(() => {
        if (!competenciaSelecionada) return

        setResumo(initialValue)

        async function getResumo() {
            setIsLoading(true)

            try {
                const responseResumoMedico = await CobrancaServices.getResumoMedico(competenciaSelecionada.competencia, prestadorVinculado.uuid)

                if (responseResumoMedico.status === 200) {
                    setResumo(responseResumoMedico.data)
                }
            } catch (err) {
                addToast({
                    type: 'error',
                    duration: 5000,
                    title: 'ocorreu um erro ao buscar as informações de resumo'
                })
            } finally {
                setIsLoading(false)
            }
        }

        getResumo()
    }, [competenciaSelecionada, extratosDisponiveis, demonstrativoContasXmlDisponivel])

    const isInitial = useMemo(() => JSON.stringify(resumo) === JSON.stringify(initialValue), [resumo, initialValue])

    // FIXME: Campo glosado deve ser alterado para a versão comm a lógica caso necessário.

    useEffect(() => {
        if (resumo && resumo?.fechamento !== '-') {
            // let data = new Date(resumo?.fechamento)

            // Adiciona 3 horas para corrigir o fuso
            // const dataFechamento = data?.setHours(data?.getHours() + 3)

            setCardValues([
                {
                    title: 'Apresentado',
                    description: isInitial ? '-' : NumberUtils.maskMoney(resumo?.apresentado)
                },
                {
                    title: 'Apurado',
                    description: isInitial ? '-' : NumberUtils.maskMoney(resumo?.apurado)
                },
                {
                    title: 'Glosado',
                    // description: isInitial || new Date() < new Date(dataFechamento) ? '-' : NumberUtils.maskMoney(resumo?.glosado)
                    description: '-'
                }
            ])
        }

        const cardDates = [
            {
                title: 'Abertura',
                description: moment(resumo?.abertura).isValid() ? DateUtils.formatDatePTBR(resumo?.abertura) : '-'
            },
            {
                title: 'Limite de envio',
                description: moment(resumo?.limiteEnvio).isValid()
                    ? `${DateUtils.formatDatePTBR(resumo?.limiteEnvio)} às ${resumo?.horarioLimiteEnvioLote?.slice(0, -3)}`
                    : '-'
            }
        ]

        setCardDates(cardDates)
    }, [resumo])

    // useEffect(() => {
    //     setDemonstrativo(null)

    //     if (competenciaSelecionada?.competencia) {
    //         Demonstrativo.getDadoGeraisByCompetencia(competenciaSelecionada.competencia)
    //             .then(({ data }) => {
    //                 setDemonstrativo(data)
    //             })
    //             .catch((err) => console.log(err))
    //     }
    // }, [competenciaSelecionada])

    const handleDownloadExtratoProducaoXlsx = () => {
        setIsDownloading(() => ({ xml: false, xls: true }))

        const params = {
            tipoArquivo: 'XLSX',
            idPrestador: prestadorVinculado.uuid,
            competencia: competenciaSelecionada.competencia
        }

        CobrancaServices.getDemonstrativoPagamentoXlsx(params)
            .then((response) => {
                const contentDisposition = response.headers['content-disposition']
                let fileName = 'default_filename'

                if (contentDisposition) {
                    const fileNameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
                    if (fileNameMatch && fileNameMatch.length > 1) {
                        fileName = fileNameMatch[1].replace(/['"]/g, '')
                    }
                }

                const blob = new Blob([response.data])
                const url = window.URL.createObjectURL(blob)
                const link = document.createElement('a')

                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link)
            })
            .catch((err) => {
                addToast({ title: err?.message || 'Erro ao fazer download', type: 'error', duration: 3000 })
            })
            .finally(() => {
                setIsDownloading(() => ({ xml: false, xls: false }))
            })
    }

    const handleDownloadExtratoProducaoXML = () => {
        setIsDownloading(() => ({ xml: true, xls: false }))

        const params = {
            tipoArquivo: 'XML',
            idPrestador: prestadorVinculado.uuid,
            competencia: competenciaSelecionada.competencia
        }

        CobrancaServices.getDemonstrativoPagamentoXML(params)
            .then((response) => {
                const contentDisposition = response.headers['content-disposition']
                let fileName = 'default_filename'

                if (contentDisposition) {
                    const fileNameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
                    if (fileNameMatch && fileNameMatch.length > 1) {
                        fileName = fileNameMatch[1].replace(/['"]/g, '')
                    }
                }

                const blob = new Blob([response.data])
                const url = window.URL.createObjectURL(blob)
                const link = document.createElement('a')

                link.href = url
                link.setAttribute('download', fileName)
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link)
            })
            .catch((err) => {
                addToast({ title: err?.message || 'Erro ao fazer download', type: 'error', duration: 3000 })
            })
            .finally(() => {
                setIsDownloading(() => ({ xml: false, xls: false }))
            })
    }

    function listDemonstrative() {
        DemonstrativoPrestador?.getDemonstrativeList({
            competencia: competenciaSelecionada?.competencia,
            page: numberPage,
            size: 10
        })
            .then(({ data }) => {
                setDemonstrativeList(data?.content)
                setPagination(parserPagination(data))
            })
            .catch((err) => {
                console.log(err)
            })
    }

    useEffect(() => {
        if (competenciaSelecionada) {
            listDemonstrative()
        }
    }, [competenciaSelecionada])

    return (
        <S.Container>
            <SectionDadosCobranca title="Datas" cards={cardDates} />

            {SituacaoPrestadorAnalise[competenciaSelecionada?.situacao] !== SituacaoPrestadorAnalise.ABERTO ? (
                <SectionDadosCobranca title="Valores da produção" cards={cardValues} />
            ) : null}

            {demonstrativeList?.length > 0 && (
                <SectionDadosCobranca title="Demonstrativos de pagamento">
                    <S.TableContent>
                        <S.Top container spacing={2}>
                            <Grid item xs={2}>
                                <p>Identificador da cobrança</p>
                            </Grid>
                            <Grid item xs={2}>
                                <p>Total aprovado para pagamento</p>
                            </Grid>
                            <Grid item xs={2}>
                                <p>Data do pagamento</p>
                            </Grid>
                            <Grid item xs={1}>
                                <p>Tipo de faturamento</p>
                            </Grid>
                            <Grid item xs={1}>
                                <p>Exercícios</p>
                            </Grid>
                            <Grid item xs={2}>
                                <p>Situação</p>
                            </Grid>
                            <Grid item xs={1}></Grid> {/* Espaço para o botão "Detalhes" */}
                        </S.Top>

                        {/* TODO: MAP DO CONTEUDO DO GET DE LISTAGEM */}
                        {demonstrativeList?.map((item, index) => (
                            <S.List key={index} container spacing={2}>
                                <Grid item xs={2}>
                                    <p>{item?.identificador}</p>
                                </Grid>
                                <Grid item xs={2}>
                                    <p>{NumberUtils?.maskMoney(item?.valorBruto)}</p>
                                </Grid>
                                <Grid item xs={2}>
                                    <p>{item?.dataPagamento?.split('-').reverse().join('/')}</p>
                                </Grid>
                                <Grid item xs={1}>
                                    <p>{item?.tipoFaturamento}</p>
                                </Grid>
                                <Grid item xs={1}>
                                    <p>{item?.exercicios}</p>
                                </Grid>
                                <Grid item xs={2}>
                                    <p className={`box-${EnumSituacaoColor[item?.statusDemostrativo]} badge`}>
                                        {EnumSituacaoPrettyName[item?.statusDemostrativo]}
                                    </p>
                                </Grid>
                                <Grid item xs={1}>
                                    <S.Buttons>
                                        <Button
                                            themeButton="primary"
                                            typeButton="text"
                                            iconLeft={'/faturamento/assets/icons/lupe-v2.svg'}
                                            onClick={() =>
                                                push(
                                                    `cobranca/demonstrativo?id=${item?.demonstrativoId}&competencia=${competenciaSelecionada?.competencia}`
                                                )
                                            }
                                        >
                                            Detalhes
                                        </Button>
                                    </S.Buttons>
                                </Grid>
                            </S.List>
                        ))}
                    </S.TableContent>
                    {/* TODO: SERÁ IMPLEMENTADA NO FUTURO */}

                    {/* <Pagination
                        totalPage={pagination?.totalPaginas}
                        totalRegister={pagination?.totalRegistros}
                        actualPage={pagination?.paginaAtual}
                        setNumberPage={pagination?.setNumberPage}
                    /> */}
                </SectionDadosCobranca>
            )}

            {(extratoDisponivel || demonstrativoContasXmlDisponivel) && (
                <SectionDadosCobranca title="Extrato de Produção" isLoading={loading}>
                    <S.Buttons>
                        {extratoDisponivel && (
                            <S.CustomButton
                                typeButton="ghost"
                                themeButton="primary"
                                disabled={isDownloading?.xls}
                                onClick={handleDownloadExtratoProducaoXlsx}
                                iconLeft="/faturamento/assets/icons/download.svg"
                            >
                                {isDownloading?.xls ? 'Baixando' : 'Baixar'} XLSX
                            </S.CustomButton>
                        )}

                        {demonstrativoContasXmlDisponivel && (
                            <S.CustomButton
                                typeButton="ghost"
                                themeButton="primary"
                                disabled={isDownloading?.xml}
                                onClick={handleDownloadExtratoProducaoXML}
                                iconLeft="/faturamento/assets/icons/download.svg"
                            >
                                {isDownloading?.xml ? 'Baixando' : 'Baixar'} XML
                            </S.CustomButton>
                        )}
                    </S.Buttons>
                </SectionDadosCobranca>
            )}
        </S.Container>
    )
}

export default TabResumoMedico
