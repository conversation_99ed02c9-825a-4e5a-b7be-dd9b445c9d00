import styled from 'styled-components'
import theme from 'styles/theme'

export const Container = styled.div`
    padding: 2.4rem;
    border-radius: 0.8rem;
    display: flex;
    flex-direction: column;
    gap: 2.4rem;
    background-color: #ffffff;
`

export const Header = styled.div`
    display: flex;
    align-items: center;
    gap: 2.4rem;
    justify-content: space-between;
`

export const Title = styled.p`
    font-size: 2rem;
    line-height: 2.8rem;
    font-weight: 600;
    color: ${theme.colors.primary[500]};
`

export const WrapperPagination = styled.div`
    display: flex;
    justify-content: space-between;
    align-items: flex-end;

    > p {
        font-size: 1.6rem;
        line-height: 2.4rem;
        color: rgba(0, 0, 0, 0.56);
    }
`

export const EmptyListMessage = styled.p`
    font-size: 1.6rem;
    line-height: 2.4rem;
    font-weight: 400;
    color: ${theme.colors.black[56]};
    text-align: center;
`

export const FlexRow = styled.div`
    display: flex;
    align-items: center;
    justify-content: space-between;
`
