import { useEffect, useState } from 'react'
import Layout from 'components/molecules/Layout'
import CardSubMenu from 'components/molecules/CardSubMenu'
import * as S from './styles'
import TitleSection2 from 'components/atoms/TitleSection2'
import { Me } from 'src/services/gestaoUsuarios/me'
import { ScopoEnum } from 'src/enum/scopes.enum'
import { getCurrentClient } from 'utils/parseAssets'
import { ClientEnum } from 'utils/parseAssets/client-enum'
import { ClientsResourcesMapMock, ProfileResourcesMapMock, ResourceSectionsEnum, ResourcesEnum, ResourcesSectionsMap } from './resources-configs'
import Alert from 'components/atoms/Alert'
import { ContasAPagar } from 'src/services/faturamentoPrestadorApi/contas-apagar-controller'
import { IGetPendencias } from 'src/services/faturamentoPrestadorApi/contas-apagar-controller/types'
import { TipoPendenciaEnum } from 'src/services/faturamentoPrestadorApi/contas-apagar-controller/enuns'
import { pendenciasMock } from './mock'
import { DateUtils } from 'utils/dateUtils'
import { useRouter } from 'next/router'
import { useAuth } from 'src/hooks/auth'
import { ProfileResponseDTO } from 'types/me'

const HomeTemplate = () => {
    const { prestadorVinculado } = useAuth()
    const route = useRouter()
    const [profile, setProfile] = useState(null)
    const [resourcesName, setResourcesName] = useState([])
    const [contextsName, setContextsName] = useState([])
    const [loadingPage, setLoadingPage] = useState(true)
    const [prestadorId, setPrestadorId] = useState<string>()
    const [pendencias, setPendencias] = useState<IGetPendencias>()
    const [competenciaSelecionada, setCompetenciaSelecionada] = useState<string>()

    const getContexts = async () => {
        setLoadingPage(true)

        const { data: profile } = await Me.getProfile()
        setProfile(profile)
        const { data } = await Me.get()
        setPrestadorId(data?.id)

        const { data: context } = await Me.getContext()
        const contextMapping = context.map((context) => context.name)
        setContextsName(contextMapping)

        const { data: resources } = await Me.getResourcesByScopeName(ScopoEnum.FATURAMENTO)
        // const resourceMapping = resources.map((resource) => resource?.name)
        const resourceMapping = getResourcesNameByProfileMock(profile)
        setResourcesName(resourceMapping)

        setLoadingPage(false)
    }

    const getResourcesNameByProfileMock = (profile: ProfileResponseDTO) => {
        let profileResources = []
        const clientResources = ClientsResourcesMapMock[getCurrentClient()] || []

        profile?.profiles?.forEach((p) => {
            profileResources = [...profileResources, ...(ProfileResourcesMapMock[p] || [])]
        })

        return profile?.isAdmin ? clientResources : clientResources.filter((r) => profileResources.includes(r))
    }

    const showCardMenu = (resourceName: ResourcesEnum) => {
        return resourcesName?.includes(resourceName)
    }

    const showSection = (sectionName: ResourceSectionsEnum) => {
        return ResourcesSectionsMap[sectionName].some((resource) => showCardMenu(resource))
    }

    function parseDatePendence(date: string[]) {
        const datas = date?.map((item, index) => {
            return (
                <>
                    <span key={index} className="link" onClick={() => route.push(`/prestador/medico/cobranca?competencia=${item}`)}>
                        {`${DateUtils.getMonthFullName(item)} de ${new Date(item).getFullYear()}`}
                    </span>
                    <span>{index < date?.length - 2 ? ', ' : index === date?.length - 2 ? ' e ' : ''}</span>
                </>
            )
        })
        return datas
    }

    useEffect(() => {
        if (prestadorVinculado) {
            ContasAPagar.getPendencias(prestadorVinculado?.uuid)
                .then(({ data }) => {
                    setPendencias(data)
                })
                .catch((error) => console.log(error))
        }
    }, [prestadorVinculado])

    useEffect(() => {
        getContexts()
    }, [])
    // useEffect(() => {
    //     const profiles: any[] = profile?.profiles?.filter((item) => {
    //         return item === 'PRESTADOR'
    //     })
    // }, [profile])

    return (
        <S.Container>
            {!loadingPage && (
                <>
                    <div style={{ marginBottom: '24px' }}>
                        {pendencias?.pendencias?.length > 0 &&
                            profile?.profiles?.filter((item) => {
                                return item === 'PRESTADOR'
                            })?.length > 0 && (
                                <Alert
                                    iconSrc="/faturamento/assets/icons/monotimer-ic.svg"
                                    color="secondary"
                                    title="Pendências"
                                    description={pendencias?.pendencias?.map((item, index) => {
                                        return item?.tipo === TipoPendenciaEnum.AGUARDANDO_NOTA_FISCAL ? (
                                            <S.DescriptionAlert key={index}>
                                                <span className="strong">{item?.descricao}</span>
                                                {item?.competencias?.length > 1 ? ': As competências de ' : ': A competência de '}

                                                {parseDatePendence(item?.competencias)}

                                                {item?.competencias?.length > 1
                                                    ? ' possuem demonstrativos aguardando o envio de notas fiscais.'
                                                    : ' possui demonstrativos aguardando o envio de notas fiscais.'}
                                            </S.DescriptionAlert>
                                        ) : (
                                            <S.DescriptionAlert key={index}>
                                                <span className="strong">{item?.descricao}</span>
                                                {item?.competencias?.length > 1 ? ': As competências de ' : ': A competência de '}

                                                {parseDatePendence(item?.competencias)}

                                                {item?.competencias?.length > 1
                                                    ? ' possuem demonstrativos com notas fiscais reprovadas.'
                                                    : ' possui demonstrativos com notas fiscais reprovadas.'}
                                            </S.DescriptionAlert>
                                        )
                                    })}
                                />
                            )}
                    </div>
                    {showSection(ResourceSectionsEnum.PROCESSAMENTO_CONTAS) && (
                        <>
                            <S.Title>Processamento de contas</S.Title>
                            <S.CardFlex>
                                {showCardMenu(ResourcesEnum.CALENDARIO_DE_CONTAS) && (
                                    <CardSubMenu
                                        title="Calendário de Faturamento"
                                        image="/faturamento/assets/icons/calendar-day.svg"
                                        description="Crie, edite ou delete calendários de faturamento"
                                        link="calendario-faturamento"
                                    />
                                )}
                                {/* {showCardMenu(ResourcesEnum.AUDITORIA_DE_BANCADA) && (
                                    <CardSubMenu
                                        title="Auditoria de bancada"
                                        image="/faturamento/assets/icons/calendar-day.svg"
                                        description="Analise e audite lotes e guias"
                                        link="processamento-contas/auditoria-bancada/resumo-auditoria"
                                    />
                                )} */}
                                {showCardMenu(ResourcesEnum.ANALISE_DE_CONTAS) && (
                                    <CardSubMenu
                                        title="Análise de contas"
                                        image="/faturamento/assets/icons/money.svg"
                                        description="Analise e audite todas as contas recebidas"
                                        link="processamento-contas/analise-contas"
                                    />
                                )}
                                {showCardMenu(ResourcesEnum.RECURSOS_DE_GLOSA) && (
                                    <CardSubMenu
                                        title="Recursos de glosa"
                                        image="/faturamento/assets/icons/form.svg"
                                        description="Analise e audite itens glosados"
                                        link="processamento-contas/recurso-glosa"
                                    />
                                )}
                                {/* {showCardMenu(ResourcesEnum.ASSOCIACAO_DE_CAIXA) && (
                                    <CardSubMenu
                                        title="Associação de caixa"
                                        image="/faturamento/assets/icons/mai-ic-shippingbox.mono.svg"
                                        description="Associe o número de caixa aos lotes"
                                        link="processamento-contas/numero-caixa"
                                    />
                                )}
                                {showCardMenu(ResourcesEnum.CONCILIACAO_DE_NOTAS) && (
                                    <CardSubMenu
                                        title="Conciliação de notas"
                                        image="/faturamento/assets/icons/mai-ic-shippingbox.mono.svg"
                                        description="Associe notas fiscais aos lotes"
                                        link="processamento-contas/conciliacao-operadora"
                                    />
                                )} */}
                                {/* {showCardMenu(ResourcesEnum.CALCULAR_GLOSA_BIOMETRIA) && (
                                    <CardSubMenu
                                        title="Calcular glosa de biometria"
                                        image="/faturamento/assets/icons/mai-ic-shippingbox.mono.svg"
                                        description="Analise as justificativas biométricas e glose valores excedentes"
                                        disabled={true}
                                        link=""
                                    />
                                )} */}
                                {profile?.isAdmin && (
                                    <CardSubMenu
                                        title="Composição de pagamento"
                                        image="/faturamento/assets/icons/mai-ic-export-file.mono.svg"
                                        description="Aprove e gerencie a composição de pagamento individual e geral"
                                        link="/composicao-pagamento"
                                    />
                                )}

                                {profile?.isAdmin && (
                                    <CardSubMenu
                                        title="Processamento de contas"
                                        image="/faturamento/assets/icons/reload-file.svg"
                                        description="Acompanhe o processamento de contas e reprocesse lotes com falhas"
                                        link="/painel-processamento-contas"
                                    />
                                )}
                            </S.CardFlex>
                        </>
                    )}

                    {showSection(ResourceSectionsEnum.RELATORIOS) && (
                        <>
                            <S.Title>Relatórios</S.Title>
                            <S.CardFlex>
                                {showCardMenu(ResourcesEnum.RELATORIOS) && (
                                    <CardSubMenu
                                        title="Relatórios de faturamento"
                                        image="/faturamento/assets/icons/money.svg"
                                        description="Visualize os relatórios online de faturamento"
                                        link="relatorio-faturamento"
                                    />
                                )}
                            </S.CardFlex>
                        </>
                    )}

                    {/* {showSection(ResourceSectionsEnum.CONTAS) ||
                        (profile?.isAdmin && (
                            <>
                                <S.Title>Contas</S.Title>
                                <S.CardFlex>
                                    {showCardMenu(ResourcesEnum.CONTAS_A_PAGAR) ||
                                        (profile?.isAdmin && (
                                            <CardSubMenu
                                                title="Contas a pagar"
                                                image="/faturamento/assets/icons/list.svg"
                                                description="Consulte e gerencie suas contas"
                                                onClick={() => (window.location.href = `${process.env.NEXT_PUBLIC_CONTAS}`)}
                                            />
                                        ))}
                                    {showCardMenu(ResourcesEnum.CONTAS_A_RECEBER) ||
                                        (process.env.NEXT_PUBLIC_ENVIRONMENT === 'staging' && profile?.isAdmin && (
                                            <CardSubMenu
                                                title="Contas a receber"
                                                image="/faturamento/assets/icons/list.svg"
                                                description="Consulte, processe e gere cobranças"
                                                onClick={() => (window.location.href = `${process.env.NEXT_PUBLIC_CONTAS_A_RECEBER}`)}
                                            />
                                        ))}

                                    {showCardMenu(ResourcesEnum.NEGOCIACOES_DE_DIVIDAS) && (
                                        <CardSubMenu
                                            title="Negociação de dividas"
                                            image="/faturamento/assets/icons/list.svg"
                                            description="Consulte e negocie dívidas atrasadas"
                                            onClick={() => (window.location.href = `${process.env.NEXT_PUBLIC_CONTAS}/negociacao`)}
                                            disabled
                                        />
                                    )}
                                </S.CardFlex>
                            </>
                        ))} */}

                    {/* {showSection(ResourceSectionsEnum.RELATORIOS) && (
                        <>
                            <S.Title>Relatórios</S.Title>
                            <S.CardFlex>
                                {showCardMenu(ResourcesEnum.RELATORIOS) && (
                                    <CardSubMenu
                                        title="Relatórios de faturamento"
                                        image="/faturamento/assets/icons/money.svg"
                                        description="Emita relatórios de lotes, extratos detalhados de glosas e outros"
                                        link="relatorios/faturamento"
                                    />
                                )}
                            </S.CardFlex>
                        </>
                    )} */}

                    {/* {showSection(ResourceSectionsEnum.PARAMETROS) && (
                        <>
                            <S.Title>Parâmetros</S.Title>
                            <S.CardFlex>
                                {showCardMenu(ResourcesEnum.REGRAS_DE_EXCLUDENCIA) && (
                                    <CardSubMenu
                                        title="Regras de excludência"
                                        image="/faturamento/assets/icons/list.svg"
                                        description="Crie e gerencie regras de excludência"
                                        link="parametros/regras-de-excludencia"
                                    />
                                )}
                            </S.CardFlex>
                        </>
                    )} */}

                    {showSection(ResourceSectionsEnum.MEDICO) && (
                        <>
                            <S.Title>Produção médico hospitalar</S.Title>
                            <S.CardFlex>
                                {showCardMenu(ResourcesEnum.COBRANCA_MEDICO) && (
                                    <CardSubMenu
                                        title="Cobrança"
                                        image="/faturamento/assets/icons/doctor.svg"
                                        // description="Confira as datas de envio de lotes, veja o demonstrativo de produção."
                                        description="Confira as datas de envio de lotes."
                                        link="/prestador/medico/cobranca"
                                        disabled={false}
                                    />
                                )}
                                {/* {showCardMenu(ResourcesEnum.RECURSO_DE_GLOSA_MEDICO) && (
                                    <CardSubMenu
                                        title="Recurso de Glosa"
                                        image="/faturamento/assets/icons/recurso.svg"
                                        description="Reivindique valores que foram glosados"
                                        link="/prestador/medico/recurso-glosa"
                                        disabled={false}
                                    />
                                )} */}
                            </S.CardFlex>
                        </>
                    )}

                    {showSection(ResourceSectionsEnum.ODONTO) && (
                        <>
                            <S.Title>Odonto</S.Title>
                            <S.CardFlex>
                                {showCardMenu(ResourcesEnum.COBRANCA_ODONTO) && (
                                    <CardSubMenu
                                        title="Cobrança"
                                        image="/faturamento/assets/icons/dente.svg"
                                        // description="Confira as datas de envio de lotes, veja o demonstrativo de produção."
                                        description="Confira as datas de envio de lotes."
                                        link="/prestador/odonto/cobranca"
                                        disabled={false}
                                    />
                                )}
                                {showCardMenu(ResourcesEnum.RECURSO_DE_GLOSA_ODONTO) && (
                                    <CardSubMenu
                                        title="Recurso de Glosa"
                                        image="/faturamento/assets/icons/recurso.svg"
                                        description="Reivindique valores que foram glosados"
                                        link="/prestador/odonto/recurso-glosa"
                                        disabled={true}
                                    />
                                )}
                            </S.CardFlex>
                        </>
                    )}

                    {showSection(ResourceSectionsEnum.SIMULACAO) && (
                        <>
                            <S.Title>Simulação</S.Title>
                            <S.CardFlex>
                                {showCardMenu(ResourcesEnum.SIMULADOR_DE_ENVIO_DE_XML) && (
                                    <CardSubMenu
                                        title="Simular envio de XML"
                                        image="/faturamento/assets/icons/mai-ic-send.svg"
                                        description="Faça uma simulação para encontrar possíveis erros na cobrança."
                                        link="/prestador/simulacao"
                                        disabled={false}
                                    />
                                )}
                            </S.CardFlex>{' '}
                        </>
                    )}

                    {profile?.isAdmin && (
                        <>
                            <S.Title>Parametros</S.Title>
                            <S.CardFlex>
                                <CardSubMenu
                                    title="Parâmetros"
                                    image="/faturamento/assets/icons/mai-ic-settings.svg"
                                    description="Configure parâmetros e regras de faturamento"
                                    link="/parametros"
                                />
                            </S.CardFlex>
                        </>
                    )}
                    {profile?.profiles?.filter((item) => {
                        return item === 'PRESTADOR'
                    })?.length > 0 && (
                        <>
                            <S.Title>Declarações</S.Title>
                            <S.CardFlex>
                                <CardSubMenu
                                    title="DIRF"
                                    image="/faturamento/assets/icons/mai-ic-attachment.mono.svg"
                                    description="Declaração do Imposto sobre a Renda Retido na Fonte."
                                    link="/DIRF"
                                    disabled={false}
                                />
                            </S.CardFlex>
                        </>
                    )}
                </>
            )}
        </S.Container>
    )
}

export default HomeTemplate
