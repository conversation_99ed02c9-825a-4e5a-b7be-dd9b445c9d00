/* eslint-disable prettier/prettier */
import styled, { css } from 'styled-components'

export const Wrapper = styled.section`
    background: white;
    padding: 16px;
    width: 100%;
    border-radius: 8px;

    display: flex;
    align-items: center;

    .select {
        min-width: 200px;
        margin-right: 80px;
    }

    @media screen and (max-width: 1024px) {
        flex-direction: column;

        .select {
            margin-bottom: 20px;
            min-width: 100%;
            margin-right: 0;
        }

        height: auto;
    }
`

export const StatusBox = styled.div`
    ${({ theme }) => css`
        border: 1px solid #d0d0d0;
        border-radius: 0.4rem;
        width: 100%;
        min-height: 55px;

        margin: 0 8px;

        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        gap: 0.2rem;

        h3 {
            font-size: 1.4rem;
            line-height: 2rem;
            font-weight: 600;
            color: #000000;
        }

        span {
            color: ${theme.colors.black[88]};
            font-size: 1.2rem;
            line-height: 1.4rem;
            font-weight: 400;
        }

        .green {
            color: ${theme.colors.success['medium']};
        }

        &.status-blue-box {
            background-color: ${theme.colors.maida.primary.dark};
            border: 1px solid ${theme.colors.maida.primary.dark};

            h3 {
                color: white;
            }

            span {
                color: white;
            }
        }
    `}
`

export const Spinner = styled.div`
    display: flex;
    align-items: center;

    .loader {
        width: 28px;
        height: 28px;
        border: 4px solid #000;
        border-bottom-color: transparent;
        border-radius: 50%;
        display: inline-block;
        box-sizing: border-box;
        animation: rotation 1s linear infinite;
    }

    @keyframes rotation {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }
`
