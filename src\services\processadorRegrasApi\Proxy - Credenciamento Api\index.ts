import { ObjectUtils } from 'utils/objectUtils'
import { apiProcessadorRegras } from 'src/services/apis/apiProcessadorRegras'
import { IPageResult, ISortPage } from 'types/pagination'
import { IPrestadorResponse } from './types'

const baseUrl = '/proxy'

export class ProxyCredenciamento {
    static async getPrestadores(props?: { cpfCnpj?: string; nomeCompletoNomeFantasia?: string } & ISortPage) {
        const params = ObjectUtils.propsToParams(props)
        return apiProcessadorRegras.get<IPageResult<IPrestadorResponse>>(`${baseUrl}/credenciamento/prestador?${params}`)
    }
}
