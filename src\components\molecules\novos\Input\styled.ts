import styled from 'styled-components'
import theme from 'styles/theme'

export const Wrapper = styled.div`
    position: relative;
    /* width: inherit; */
`

export const InputStyled = styled.input`
    height: 56px;
    border-radius: 4px;
    width: 100%;
    border: 1px solid rgba(0, 0, 0, 0.16);
    padding: 16px;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: rgba(62, 78, 101, 1);

    ::-webkit-calendar-picker-indicator {
        background-image: url('/credenciamento/assets/icons/calendar-mai-ic.svg');
        background-size: 24px 20px;
        margin: 1px;
        padding: 8px;
    }

    &:focus {
        outline: none;
        border: 2px solid #2b45d4;
        padding: 15px;
    }
`

export const Label = styled.div`
    position: absolute;
    top: -10px;
    left: 8px;
    font-size: 1.4rem;
    padding: 0 0.4rem;
    color: ${theme.colors.black[56]};
    background-color: rgba(255, 255, 255, 1);
    background-clip: content-box;
    z-index: 0;

    &:focus {
        color: #2b45d4 !important;
    }

    span {
        color: red;
    }
`
