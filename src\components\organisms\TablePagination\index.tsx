import { Checkbox } from '@mui/material'
import React, { CSSProperties, HTMLAttributes, useEffect } from 'react'
import * as S from './styles'
import Pagination from 'components/molecules/Pagination'

interface obj {
    label: string
    value: string
}

interface pagination {
    totalPaginas: number
    setNumberPage: React.Dispatch<React.SetStateAction<number>>
    totalRegistros: number
    paginaAtual: number
    linhasPorPagina: number
}

export type PaginationProps = {
    titles: obj[]
    values: Array<any>
    pagination?: pagination
    customGridStyles?: string
    enableSelect?: boolean
    handleSelect?: (value: any[]) => void
    selectIdField?: string
    onClickAction?: (value: any) => void
    onClickElement?: string
    padding?: CSSProperties['padding']
    // ALTERAÇÃO
    //=========================
    checkSelected?: any[]
    noSelectAll?: boolean
    //=========================
} & HTMLAttributes<HTMLDivElement>

function TablePagination({
    pagination,
    titles: headerTitles,
    values,
    customGridStyles: grid,
    enableSelect,
    selectIdField,
    handleSelect,
    onClickAction,
    onClickElement,
    // ALTERAÇÃO
    //=========================
    checkSelected,
    noSelectAll = false,
    //=========================
    ...props
}: PaginationProps) {
    const titles = enableSelect ? [{ label: '', value: 'select-checkbox' }, ...headerTitles] : headerTitles
    const customGridStyles = enableSelect ? `0.1fr ${grid} ` : grid
    const [onlyErrors, setOnlyErrors] = React.useState(false)
    const [selected, setSelected] = React.useState<any[]>([])

    const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.checked) {
            const newSelecteds = values.map((n) => n)
            setSelected(newSelecteds)
            return
        }
        setSelected([])
    }

    const handleSelectItem = (event: React.ChangeEvent<HTMLInputElement>, value: any) => {
        const filter = selected.filter((item) => item[selectIdField] === value[selectIdField])
        if (filter.length > 0) {
            setSelected(selected.filter((item) => item[selectIdField] !== value[selectIdField]))
        } else {
            setSelected([...selected, value])
        }
    }
    // ALTERAÇÃO: MANTER SELECIONADOS OS SELECIONADOS AO MUDAR DE PAGINA
    //=========================
    useEffect(() => {
        if (handleSelect) {
            if (selected?.length >= 0) {
                handleSelect(selected)
            }
        }
    }, [selected])
    //=========================

    // ALTERAÇÃO
    //=========================
    useEffect(() => {
        if (handleSelect) {
            if (checkSelected?.length >= 0) {
                handleSelect(checkSelected)
                setSelected(checkSelected)
            }
        }
    }, [checkSelected])
    //=========================

    // ALTERAÇÃO: MANTER SELECIONADOS OS SELECIONADOS AO MUDAR DE PAGINA
    //=========================
    useEffect(() => {
        if (checkSelected) {
            setSelected(checkSelected)
        } else {
            setSelected([])
        }
    }, [])
    //=========================
    const withErrors = (obj) => ('status' in obj && obj.status === 'CONTEM_ERROS' ? true : false)

    useEffect(() => {
        console.log(titles)
        console.log(values)
    }, [values, titles])

    return (
        <S.Wrapper {...props}>
            {pagination ? (
                <S.headerContentDemands>
                    <div className="tabs-demands-right">
                        <p>
                            {pagination?.paginaAtual * pagination?.linhasPorPagina + 1} -{' '}
                            {values.length + pagination?.paginaAtual * pagination?.linhasPorPagina}
                            {' de '}
                            {pagination?.totalRegistros}
                        </p>
                    </div>
                </S.headerContentDemands>
            ) : null}

            <S.TableTitle className="table-header" customGridStyles={customGridStyles}>
                {titles.map((element, index) =>
                    // ALTERAÇÃO
                    //=========================
                    element?.value === 'select-checkbox' ? (
                        <>
                            {noSelectAll ? (
                                <div style={{ marginLeft: '38px' }}></div>
                            ) : (
                                //===========================
                                <Checkbox defaultChecked={false} key={index} onChange={handleSelectAll} />
                            )}
                        </>
                    ) : (
                        <div key={element.value}>{element.label}</div>
                    )
                )}
            </S.TableTitle>

            <S.TableBody className="table-body" customGridStyles={customGridStyles}>
                {!onlyErrors
                    ? values?.map((element, index) => (
                          <S.ListItem
                              onClick={() => {
                                  if (onClickAction) {
                                      onClickAction(element[onClickElement])
                                  }
                              }}
                              clickAction={onClickAction ? true : false}
                              key={index}
                              customGridStyles={customGridStyles}
                              selected={selected.filter((item) => item[selectIdField] === element[selectIdField]).length > 0}
                          >
                              {titles.map((titulo, index) =>
                                  titulo?.value === 'select-checkbox' ? (
                                      <Checkbox
                                          key={index}
                                          checked={selected?.filter((item) => item[selectIdField] === element[selectIdField]).length > 0}
                                          onChange={(event) => handleSelectItem(event, element)}
                                      />
                                  ) : (
                                      <div key={index}>
                                          <div>{element[titulo.value]}</div>
                                      </div>
                                  )
                              )}
                          </S.ListItem>
                      ))
                    : values.filter(withErrors).map((element, index) => (
                          <S.ListItem key={index}>
                              {titles.map((titulo, idx) => (
                                  <div key={idx}>{element[titulo.value]}</div>
                              ))}
                          </S.ListItem>
                      ))}
            </S.TableBody>

            {pagination ? (
                <S.TableFooter>
                    <Pagination
                        totalPage={pagination?.totalPaginas}
                        totalRegister={pagination?.totalRegistros}
                        actualPage={pagination?.paginaAtual}
                        setNumberPage={pagination?.setNumberPage}
                    />
                </S.TableFooter>
            ) : null}
        </S.Wrapper>
    )
}

export default TablePagination
