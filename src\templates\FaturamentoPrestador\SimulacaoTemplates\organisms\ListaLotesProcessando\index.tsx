import * as S from './styles'
import moment from 'moment'
import React, { useCallback, useEffect, useState } from 'react'
import { IPagination } from 'types/common/pagination'
import { IPrestadorAnaliseQuery } from 'types/cobrancaPrestador/prestadorAnalise'
import { ILoteCobrancaDTO, IPageLote } from 'types/cobrancaPrestador/loteCobranca'
import { useAuth } from 'src/hooks/auth'
import { useToast } from 'src/hooks/toast'
import { CobrancaServices, IGetCobrancaProps } from 'src/services/faturamentoPrestadorApi/cobrancaServices'
import { capitalize } from 'utils/stringUtils'
import { PaginationHelper } from 'utils/helpers/paginationHelper'
import Button from 'components/atoms/Button'
import Pagination from 'components/molecules/Pagination'
import NoContent from 'components/molecules/NoContent'
import { Box, CircularProgress } from '@mui/material'

export type lotChecked = {
    index: number
    checked: boolean
    numberLote: string
}
type ListaLotesProcessandoProps = {
    competenciaSelecionada: IPrestadorAnaliseQuery
    carregarSituacoes: any
    refresh: boolean
    refreshList: boolean
    setRefresh: any
    filtroNumeroLote: string
    lotes: IPageLote
    setLotes?: any
    loadingLotes: boolean
    situacao: string
    setLoadingLotes: React.Dispatch<React.SetStateAction<boolean>>
}

export type itemsProps = {
    component: React.ReactNode
}

export enum TipoLoteEnum {
    CONSULTA = 'Consulta',
    HONORARIO = 'Honorário',
    SPSADT = 'SP-SADT',
    TRATAMENTO_ODONTOLOGICO = 'Tratamento Odontológico',
    RECURSO_GLOSA = 'Recurso Glosa',
    RESUMO_INTERNACAO = 'Resumo Internação',
    VALIDADO = 'Validado'
}

const ListaLotesProcessando = ({
    competenciaSelecionada,
    carregarSituacoes,
    refresh,
    setRefresh,
    refreshList,
    filtroNumeroLote,
    lotes,
    setLotes,
    loadingLotes,
    setLoadingLotes,
    situacao
}: ListaLotesProcessandoProps) => {
    const { addToast } = useToast()
    const { prestadorVinculado } = useAuth()

    const [checkedAll, setCheckedAll] = useState(false)
    const [lotsCheckeds, setLotsCheckeds] = useState<lotChecked[]>([])
    // const [pageLotes, setPageLotes] = useState<IPageLote>()
    const [pagination, setPagination] = useState<IPagination>()
    const [numberPage, setNumberPage] = useState(0)

    //   const route = useRouter();

    useEffect(() => {
        initLotsCheckeds(lotes?.content)
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [lotes])

    const initLotsCheckeds = (data: ILoteCobrancaDTO[]) => {
        const lotsCheckeds: lotChecked[] = []
        data?.forEach((item, index) => {
            lotsCheckeds.push({
                index,
                checked: false,
                numberLote: item?.loteCobrancaId?.toString()
            })
        })
        setLotsCheckeds(lotsCheckeds)
    }

    function checkAll() {
        setLotsCheckeds(lotsCheckeds.map((item) => ({ ...item, checked: !checkedAll })))
    }

    const carregarLotes = useCallback(
        (page?: number, filtroNumeroLote?: string, competencia?: IPrestadorAnaliseQuery, situacao?: string) => {
            const getProps: IGetCobrancaProps = {
                size: 5,
                page: page || 0
            }
            setLoadingLotes(true)
            CobrancaServices.getLotesSimulacao(
                competencia?.competencia,
                prestadorVinculado?.uuid,
                situacao,
                filtroNumeroLote?.length > 0 ? { ...getProps, filtroNumeroLote } : getProps
            )
                .then(({ data }) => {
                    setLotes(data)

                    const objectPagination = PaginationHelper.parserPagination<ILoteCobrancaDTO>(data, setNumberPage)
                    setPagination(objectPagination)
                })
                .finally(() => setLoadingLotes(false))
        },
        [competenciaSelecionada, situacao, prestadorVinculado]
    )

    // useEffect(() => {
    //     carregarLotes()
    //     console.log(filtroNumeroLote)
    // }, [filtroNumeroLote])

    useEffect(() => {
        prestadorVinculado && carregarLotes(numberPage, filtroNumeroLote, competenciaSelecionada, situacao)
    }, [numberPage, refreshList, competenciaSelecionada, situacao, prestadorVinculado])

    useEffect(() => {
        if (!lotes) return

        setCheckedAll(lotsCheckeds.filter((i) => i.checked).length === lotes?.content.length)
    }, [lotsCheckeds, lotes])

    useEffect(() => {
        if (!refresh) return

        carregarLotes()
        carregarSituacoes()
        setRefresh(false)
    }, [refresh])

    const handleClickCancelarLotes = () => {
        const cancelarLotes = async () => {
            let errosOcorridos = false
            await Promise.all(
                lotsCheckeds
                    ?.filter((i) => i.checked)
                    .map(async (lote) => {
                        try {
                            await CobrancaServices.patchCancelamento(lote.numberLote)
                        } catch {
                            errosOcorridos = true
                        }
                    })
            )

            if (!errosOcorridos) {
                addToast({ title: 'Lotes cancelados com sucesso', type: 'success' })
                carregarLotes(numberPage)
            } else {
                addToast({ title: 'Ocorreu erro ao cancelar o lote. Tente novamente.', type: 'error' })
            }
        }

        cancelarLotes()

        setTimeout(() => {
            if (carregarSituacoes) carregarSituacoes()
        }, 1000)
    }

    return (
        <>
            {loadingLotes ? (
                <Box sx={{ display: 'flex', minHeight: 218, alignItems: 'center', justifyContent: 'center' }}>
                    <CircularProgress sx={{ color: '#2B45D4' }} />
                </Box>
            ) : (
                <S.Wrapper>
                    {!lotes || lotes?.content.length === 0 ? (
                        <NoContent title="No momento não existe nenhum lote em processamento" path="/parametros/regras-de-excludencia/nova-regra" />
                    ) : (
                        <>
                            <div className="contentInput">
                                <div className="contentNumber">
                                    {/* <span className="pageNumber">1-10 de 248</span> */}
                                    <span className="pageNumber">
                                        {pagination?.paginaAtual * pagination?.linhasPorPagina + 1} -{' '}
                                        {lotes?.content?.length + pagination?.paginaAtual * pagination?.linhasPorPagina}
                                        {' de '}
                                        {pagination?.totalRegistros}
                                    </span>
                                    {/* <ReactSVG src="/faturamento/assets/icons/dot.svg" /> */}
                                </div>
                            </div>

                            <S.Table>
                                <tr>
                                    <th>Lote</th>
                                    <th>Tipo</th>
                                    <th>Data de envio</th>
                                    <th>Data do Processamento</th>
                                    <th>Envio</th>
                                </tr>

                                {lotes?.content?.map((lote, index: number) => (
                                    <S.TabContent
                                        key={index}
                                        checked={lotsCheckeds[index]?.checked}
                                        onClick={() => {
                                            //   route.push('/faturamento/guia');
                                        }}
                                    >
                                        <td>{lote?.numeroLote}</td>
                                        <td>{TipoLoteEnum[lote?.tipoLote]}</td>
                                        <td>{moment(lote?.dataEnvio).format('DD/MM/YYYY [-] HH:mm')}</td>
                                        <td>{lote?.dataProcessamento ? moment(lote?.dataProcessamento).format('DD/MM/YYYY [-] HH:mm') : ''}</td>
                                        <td>{lote?.tipoEnvio === 'ELETRONICO' ? 'Eletrônico' : capitalize(lote?.tipoEnvio)}</td>
                                        {/* <td>
                                <ReactSVG src="/faturamento/assets/icons/ic-trash.svg" />
                            </td> */}
                                    </S.TabContent>
                                ))}
                            </S.Table>

                            <div style={{ marginTop: '40px' }}>
                                <Pagination
                                    totalPage={pagination?.totalPaginas}
                                    totalRegister={pagination?.totalRegistros}
                                    actualPage={pagination?.paginaAtual}
                                    setNumberPage={(page) => setNumberPage(page)}
                                />
                            </div>
                        </>
                    )}
                </S.Wrapper>
            )}
        </>
    )
}

export default ListaLotesProcessando
