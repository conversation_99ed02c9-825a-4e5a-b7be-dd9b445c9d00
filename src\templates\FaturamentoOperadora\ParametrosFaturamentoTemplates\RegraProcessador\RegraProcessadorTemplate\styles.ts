import styled from 'styled-components'

export const Content = styled.main`
    margin: 0 auto;
    width: 95%;
    height: 60vh;
    max-width: 1400px;
    border-radius: 8px;
    background-color: #ffffff;
`

export const Header = styled.div`
    width: 95%;
    padding: 16px;
    margin: 0 auto;
    max-width: 1400px;
    border-radius: 8px;
    background-color: #ffffff;
    display: flex;
    flex-direction: column;
`

export const ResultHeader = styled.div`
    display: flex;
    justify-content: space-between;
    margin-bottom: 24px;
`
export const ContainerLottie = styled.div`
    display: flex;
    justify-content: center;
    align-items: center;

    div {
        margin: auto;
        height: full-content;
    }
`
export const ContentItem = styled.div`
    display: grid;
    margin-bottom: 10px;
    padding: 24px 16px 0 16px;
    grid-template-columns: 7fr 2fr 1.8fr 0.2fr;
`

export const ContentItemResult = styled.div`
    display: grid;
    gap: 8px;
    padding: 10px 16px;
    background: #f6f6f9;
    border-radius: 8px;
    align-items: center;
    grid-template-columns: 8fr 2fr 1.8fr 0.2fr;
    margin-bottom: 8px;
`
export const WrapperIcons = styled.span`
    display: flex;
`

export const Icon = styled.a`
    width: 36px;
    height: 36px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50px;
    margin-left: 15px;
    background: rgba(43, 69, 212, 0.04);
    cursor: pointer;
    transition: 0.3s;

    &:hover {
        transition: 0.3s;
        background: rgba(43, 69, 212, 0.2);
    }

    svg path {
        fill: #2b45d4;
    }
`
