import { TipoGuiaEnum, TipoLoteGuiaEnum } from 'src/services/faturamentoPrestadorApi/geracao-recurso-glosa/enuns'
import { IGetLotesGuias, IGetResumo } from 'src/services/faturamentoPrestadorApi/geracao-recurso-glosa/types'
import { OptionsType } from './types'
import { TipoLoteShow } from './enuns'

// IGetResumo

export const loteResumoMock: any = {
    competencia: '2023-07-20',
    id: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
    identificadorLote: 500,
    tipoLoteCobranca: TipoLoteGuiaEnum.CONSULTA,
    valorApresentado: 1340.59,
    valorApurado: 1340.59,
    valorGlosado: 1340.59,
    valorRecursado: 1340.59
}

export const tipoGuiaMock: OptionsType[] = [
    {
        label: ' Consulta',
        value: TipoLoteGuiaEnum.CONSULTA
    },
    {
        label: 'Honor<PERSON><PERSON>',
        value: TipoLoteGuiaEnum.HONORARIO
    },
    {
        label: 'Recurso de glosa',
        value: TipoLoteGuiaEnum.RECURSO_GLOSA
    },
    {
        label: 'Resumo de internação',
        value: TipoLoteGuiaEnum.RESUMO_INTERNACAO
    },
    {
        label: 'SP/SADT',
        value: TipoLoteGuiaEnum.SPSADT
    },
    {
        label: 'Tratamento odontológico',
        value: TipoLoteGuiaEnum.TRATAMENTO_ODONTOLOGICO
    }
]

export const guidListMock: IGetLotesGuias[] = [
    {
        id: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
        tipoGuia: TipoGuiaEnum.RESUMO_INTERNACAO,
        numeroGuiaOperadora: '2001',
        nomeBeneficiario: 'Cícero Carlos Gomes de Lacerda',
        carteiraBeneficiario: '11122233344',
        valorApresentado: 1000,
        valorGlosado: 1000,
        valorApurado: 1000,
        valorRecursado: 1000
    },
    {
        id: '3fa85f64-5717-4562-b3fc-2c963f66afa5',
        tipoGuia: TipoGuiaEnum.HONORARIOS,
        numeroGuiaOperadora: '2001',
        nomeBeneficiario: 'Cícero Carlos Gomes de Lacerda',
        carteiraBeneficiario: '11122233344',
        valorApresentado: 1000,
        valorGlosado: 1000,
        valorApurado: 1000,
        valorRecursado: 1000
    },
    {
        id: '3fa85f64-5717-4562-b3fc-2c963f66afa4',
        tipoGuia: TipoGuiaEnum.SPSADT,
        numeroGuiaOperadora: '2001',
        nomeBeneficiario: 'Cícero Carlos Gomes de Lacerda',
        carteiraBeneficiario: '11122233344',
        valorApresentado: 1000,
        valorGlosado: 1000,
        valorApurado: 1000,
        valorRecursado: 1000
    }
]

export const resourceListMock = [
    {
        idGuia: 1,
        numeroGuia: 10001,
        tipoGuia: 'Prorrogação internação',
        nomeBeneficiario: 'Cícero Carlos Gomes de Lacerda',
        cnpjBeneficiario: 11122233344,
        valorApresentado: 1000,
        valorGlosado: 1000,
        valorApurado: 1000,
        valorRecursado: 1000
    },
    {
        idGuia: 2,
        numeroGuia: 10001,
        tipoGuia: 'Prorrogação internação',
        nomeBeneficiario: 'Cícero Carlos Gomes de Lacerda',
        cnpjBeneficiario: 11122233344,
        valorApresentado: 1000,
        valorGlosado: 1000,
        valorApurado: 1000,
        valorRecursado: 1000
    },
    {
        idGuia: 3,
        numeroGuia: 10001,
        tipoGuia: 'Prorrogação internação',
        nomeBeneficiario: 'Cícero Carlos Gomes de Lacerda',
        cnpjBeneficiario: 11122233344,
        valorApresentado: 1000,
        valorGlosado: 1000,
        valorApurado: 1000,
        valorRecursado: 1000
    }
]
