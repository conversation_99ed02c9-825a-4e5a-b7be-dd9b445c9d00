import { IPage, ISortPage } from 'types/pagination'
import { EnumSituacao } from '../enuns'

export interface IListProps extends ISortPage {
    competencia: string
}

export interface IGetListPageableDTO extends IPage {
    content: IGetListResponse[]
}

export interface IGetListResponse {
    identificador: string
    id: number
    demonstrativoId: string
    nomePrestador: string
    cnpj: string
    nomeArquivoPed: string
    nomeArquivoEmp: string
    numeroPed: string
    numeroEmp: string
    competencia: string
    tipoPrestador: string
    liberado: boolean
    bloqueadoAnaliseNF: boolean
    valorBruto: number
    dataPagamento: string
    statusDemostrativo: EnumSituacao
    tipoFaturamento?: string
    exercicios?: string
}

// GET

export interface IGetDemonstrativeDataDTO {
    demonstrativoUUID: string
    dadosConta: IDadosConta
    dadosFinanceiros: IDadosFinanceiros
    dadosBancarios: IDadosBancarios
}

export interface IDadosBancarios {
    numeroBanco: string
    nomeBanco: string
    numeroAgencia: string
    numeroContaCorrente: string
}

export interface IDadosConta {
    valorProducao: number
    valorGlosa: number
    valorTeto: number
    valorExtraTeto: number
    valorExtraTetoAcumulado: number
    valorLiberadoCompetenciasAnteriores: number
    baseCalculoIRDemaisServicos: number
    baseCalculoIRReduzida: number
    valorAprovadoPagamento: number
}

export interface IDetalhesItem {
    nome: string
    valor: string
}

export interface IDadosFinanceiros {
    codigoEPrestador: string
    cnpj: string
    codigoIBGEMunicipio: string
    nomeMunicipio: string
    competencia: string
    numeroPED: string
    numeroEMP: string
    numeroLIQ: string
    numeroNOB: string
    valorBruto: number
    valorBrutoDetalhes?: IDetalhesItem[]
    valorRetencaoIR: number
    valorRetencaoIRDetalhes?: IDetalhesItem[]
    valorRetencaoISS: number
    valorRetencaoISSDetalhes?: IDetalhesItem[]
    valorOutrosDescontos: number
    valorLiquido: number
    valorLiquidoDetalhes?: IDetalhesItem[]
    statusDemostrativo:
        | 'GERADO'
        | 'CANCELADO'
        | 'AGUARDANDO_ENVIO_NF'
        | 'AGUARDANDO_APROVACAO_NF'
        | 'NF_APROVADA'
        | 'NF_REPROVADA'
        | 'AGUARDANDO_CONFIRMACAO_PAGAMENTO'
        | 'PAGAMENTO_CONFIRMADO'
    dataConfirmacaoPagamento: string
    numeroNotaFiscalAprovada: string
    dataEmissaoNotaFiscalAprovada: string
    dataEnvioNotaFiscalAprovada: string
    dataAprovacaoNotaFiscalAprovada: string
    parcelaDescontos: any[]
    exercicios: string
}

// POST

export type INfDemonstrative = {
    uuid: string
    nome: string
    url: string
}
